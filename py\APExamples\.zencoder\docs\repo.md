# APExamples Information

## Summary
APExamples is a desktop application built with PySide6 (Qt for Python) that serves as a command management tool. It allows users to store, categorize, search, and copy PowerShell commands for various Microsoft services like Exchange Online (EXO), SharePoint Online (SPO), Microsoft Graph, and Azure Security features. The application provides a user-friendly interface for IT administrators to quickly access frequently used commands.

## Structure
- **APExamples.py**: Main application file containing the UI and logic
- **Test-APExamples.py**: Test version of the application with identical code
- **commands.json**: Stores the PowerShell commands organized by category
- **command_history.json**: Tracks search and command usage history
- **settings.json**: Stores user preferences and application configuration
- **.zencoder**: Documentation directory

## Language & Runtime
**Language**: Python
**Version**: Python 3.x
**UI Framework**: PySide6 (Qt for Python)
**Package Manager**: Not specified (no requirements.txt found)

## Dependencies
**Main Dependencies**:
- PySide6: Qt framework for Python GUI development
- PIL (Pillow): Python Imaging Library for image processing
- Standard libraries: sys, os, json, csv, datetime, re

## Build & Installation
No specific build process is documented. The application appears to be run directly:

```bash
python APExamples.py
```

## Configuration
**Settings File**: settings.json
**Configuration Options**:
- Theme: dark/light theme selection
- Auto-save: Enable/disable automatic saving
- Highlight search: Enable/disable search result highlighting
- Sort alphabetically: Enable/disable alphabetical sorting of commands
- Group by verb: Group PowerShell commands by their verb (Get, Set, Add, etc.)
- Tab order: Custom ordering of category tabs

## Data Storage
**Command Storage**: commands.json
**Format**: JSON with categories as keys and arrays of command objects as values
**Command Structure**:
- text: The PowerShell command text
- favorite: Boolean flag for favorite commands

**Categories**:
- EXO: Exchange Online commands
- SPO: SharePoint Online commands
- MsGraph: Microsoft Graph API commands
- ASR and Defender: Attack Surface Reduction and Microsoft Defender commands

## User Interface
**Main Components**:
- Tab-based interface with categories
- Command list with search functionality
- Command editor with syntax highlighting
- Favorites system for frequently used commands
- Copy to clipboard functionality with notifications
- Settings dialog for application configuration

## Features
- PowerShell command organization by category
- Command grouping by PowerShell verbs (Get, Set, Add, etc.)
- Search functionality with history
- Command editing and management
- Dark/light theme support
- Clipboard integration for quick command copying
- Favorites system for frequently used commands
- Automatic saving of changes