﻿## Shared Mailbox Activity Report
Learn how to audit shared mailbox activities with PowerShell to streamline mailbox management and enhance security.

***Sample Output***

The exported report on shared mailbox activity tracking looks like the screenshot below.

![Shared mailbox activity report](https://o365reports.com/wp-content/uploads/2024/09/Audit-Shared-Mailbox-Activities.png?v=1727171462)
## Powerful Microsoft 365 Reporting Tool by AdminDroid
If this script is helpful, you’ll love [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub)! With over 1800 pre-built reports and dashboards, it’s a fantastic way to dive deeper into your M365 data.

*View more comprehensive M365 mailbox reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/10013/1/20](https://demo.admindroid.com/#/1/11/reports/10013/1/20)*
