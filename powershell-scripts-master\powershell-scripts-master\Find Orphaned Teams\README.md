## Finding and Managing Microsoft Teams Without Owner

This script helps to find Microsoft Teams without an owner and manage them properly by assigning new owner to orphaned teams using PowerShell.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Microsoft Teams without Owner Report](https://o365reports.com/wp-content/uploads/2022/01/Find-Teams-without-owner-Powershell.png?v=1705576608)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for detailed reporting? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, it seamlessly complements your PowerShell scripts.

*View more comprehensive Microsoft Teams without owner report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/60078/1/20>*  



