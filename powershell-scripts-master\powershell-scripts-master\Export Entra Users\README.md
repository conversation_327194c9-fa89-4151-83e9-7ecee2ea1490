## How to Get All Users in Microsoft Entra ID

Export Entra users in Microsoft 365 with PowerShell script to gain insights into user type, license usage, sign-in status, and more for efficient user management.

***Sample Output:***

The exported Entra users report looks like the screenshot below.

![Microsoft 365 Users Report ](<https://o365reports.com/wp-content/uploads/2025/04/Entra-users-report.png?v=**********>)

## Microsoft 365 Reporting tool by AdminDroid

Transform your Microsoft 365 data into actionable insights with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), featuring over 1900+ pre-built reports and 30+ dashboards.

*Get comprehensive report on Microsoft 365 Users with AdminDroid: [https://demo.admindroid.com/#/1/11/reports/1/1/20](https://demo.admindroid.com/#/1/11/reports/1/1/20)*