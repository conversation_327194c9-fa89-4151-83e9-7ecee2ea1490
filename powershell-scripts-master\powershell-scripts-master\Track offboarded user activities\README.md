## Track Offboarded Users Activities in Microsoft 365 Using PowerShell
Learn a simple and convenient way to keep track of Microsoft 365 offboarded user activities. Avoid being a victim of data breaches!

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Track offboarded user activities](https://o365reports.com/wp-content/uploads/2024/01/Offboarded-UserActivity-1-1.png?v=1705575596)
## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts.

*View more comprehensive user activities report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/20161/1/20>* 

