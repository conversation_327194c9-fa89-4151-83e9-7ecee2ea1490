﻿## **Identity MFA Deployment Source in Microsoft 365 Using PowerShell**
Identify MFA deployment source methods whether it's per-user MFA, security defaults, or Conditional Access policies.

***Sample Output:***

The script outputs CSV file detailing the MFA deployment source report of all users.

![MFA Deployment Source Report](https://o365reports.com/wp-content/uploads/2024/06/Identify-MFA-Deployment-Source-Report-1024x420.png?v=1719384701)

## **Microsoft 365 Reporting Tool by AdminDroid**
Looking to get more from your Microsoft 365 data? With [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), you can! Explore over 1900+ pre-built reports and 20+ interactive dashboards to dive deep into your Microsoft 365 data. Experience the insights you've been missing!

*View more comprehensive user sign-ins based on MFA enforcement sources through AdminDroid:*  [*https://demo.admindroid.com/#/1/11/reports/20363/1/20*](https://demo.admindroid.com/#/1/11/reports/20363/1/20)


