# Importing the SDK Module
Import-Module -Name Microsoft.Graph.DeviceManagement.Actions

if (!(Get-MgContext)) {
    Connect-MgGraph -scope DeviceManagementManagedDevices.PrivilegedOperations.All, DeviceManagementManagedDevices.ReadWrite.All,DeviceManagementManagedDevices.Read.All
}

#### Gets all Windows devices
$Devices = Get-MgDeviceManagementManagedDevice -Filter "contains(operatingsystem,'Windows')" -All

Foreach ($Device in $Devices)
{
    Sync-MgDeviceManagementManagedDevice -ManagedDeviceId $Device.Id
    Write-Host "Sending Sync request to Device with Device name $($Device.DeviceName)" -ForegroundColor Yellow
}

Write-Host "Disconnecting from MgGraph" -ForegroundColor Cyan
Disconnect-MgGraph > $null 2>&1