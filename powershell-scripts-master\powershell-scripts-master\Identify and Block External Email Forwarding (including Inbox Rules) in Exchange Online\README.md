﻿## Block External Email Forwarding Report
Learn how to identify and block external email forwarding configurations that risk your organization efficiently using PowerShell script.

***Sample Output:***

The script generates two reports, external email forwarding configuration report and inbox rules with external forwarding report.

![External Email Forwarding Configuration <a name="_int_c2sl205a"></a>Report](https://o365reports.com/wp-content/uploads/2024/07/ExternalEmailForwardingReport-Sample-Output-800x158.png?v=1721713117)

![Inbox Rules with External Forwarding <a name="_int_s2hamngb"></a>Report](https://o365reports.com/wp-content/uploads/2024/07/InboxRulesWithExternalForwardingReport-Sample-Output-1024x191.png?v=1721713205)

Once the reports are generated, you will be asked for confirmation to block external forwarding configurations and disable inbox rules generated in the report. After your confirmation, the script will block external forwarding configuration and disable all the inbox rules in the output report and provide the respective log file.

![Removed External Forwarding Configuration Log File](https://o365reports.com/wp-content/uploads/2024/07/RemovedExternalForwarding-Log-File.png?v=1721713371)

![Disabled Inbox Rules with External Forwarding Log File](https://o365reports.com/wp-content/uploads/2024/07/DisableInboxRuleWithExternalForwarding-Log-File.png?v=1721713399)
## Microsoft 365 Reporting Tool by AdminDroid
Are you looking for more detailed reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) contains 1900+ crystal-clear reports and 30+ intuitive dashboards to improve your Microsoft 365 management effectively.

*View detailed external forwarding and inbox rule reports in AdminDroid:*

[*https://demo.admindroid.com/#/1/0/reports/10665/1/20*](https://demo.admindroid.com/#/1/0/reports/10665/1/20)

[*https://demo.admindroid.com/#/1/0/reports/10662/1/20*](https://demo.admindroid.com/#/1/0/reports/10662/1/20)

