"""
Modern themes with complete dark mode support
"""

from enum import Enum
from typing import Dict


class Theme(Enum):
    LIGHT = "light"
    DARK = "dark"


class ThemeManager:
    """Manages application themes"""
    
    def __init__(self):
        self.current_theme = Theme.LIGHT
        self._themes = {
            Theme.LIGHT: self._create_light_theme(),
            Theme.DARK: self._create_dark_theme()
        }
    
    def get_stylesheet(self, theme: Theme = None) -> str:
        """Get complete stylesheet for theme"""
        if theme is None:
            theme = self.current_theme
        return self._themes[theme]
    
    def set_theme(self, theme: Theme):
        """Set current theme"""
        self.current_theme = theme
    
    def toggle_theme(self) -> Theme:
        """Toggle between light and dark theme"""
        self.current_theme = Theme.DARK if self.current_theme == Theme.LIGHT else Theme.LIGHT
        return self.current_theme
    
    def _create_light_theme(self) -> str:
        """Create light theme stylesheet"""
        return """
/* Light Theme */
QMainWindow {
    background-color: #f8f9fa;
    color: #212529;
}

/* Menu Bar */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #dee2e6;
    padding: 4px;
    color: #212529;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #e3f2fd;
}

QMenuBar::item:pressed {
    background-color: #bbdefb;
}

QMenu {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 24px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #e3f2fd;
}

/* Tool Bar */
QToolBar {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 4px;
    spacing: 4px;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 6px;
    padding: 8px 12px;
    color: #495057;
    font-weight: 500;
}

QToolButton:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

QToolButton:pressed {
    background-color: #e9ecef;
}

/* Status Bar */
QStatusBar {
    background-color: #ffffff;
    border-top: 1px solid #dee2e6;
    padding: 6px;
    color: #6c757d;
}

/* Splitter */
QSplitter::handle {
    background-color: #dee2e6;
    width: 3px;
    height: 3px;
    border-radius: 1px;
}

QSplitter::handle:hover {
    background-color: #0078d4;
}

/* Tree Widget (Email List) */
QTreeWidget {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
    outline: none;
    font-size: 13px;
}

QTreeWidget::item {
    padding: 10px 8px;
    border-bottom: 1px solid #f8f9fa;
    color: #495057;
}

QTreeWidget::item:hover {
    background-color: #f8f9fa;
}

QTreeWidget::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QTreeWidget::branch {
    color: #6c757d;
}

QHeaderView::section {
    background-color: #f8f9fa;
    border: none;
    border-bottom: 1px solid #dee2e6;
    padding: 8px;
    font-weight: 600;
    color: #495057;
}

/* Text Edit (Email Content) - Always white background */
QTextEdit {
    background-color: #ffffff !important;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    color: #000000 !important;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
    font-size: 13px;
    line-height: 1.5;
}

QTextEdit:focus {
    border-color: #0078d4;
    outline: none;
}

/* Scroll Bars */
QScrollBar:vertical {
    background-color: #f8f9fa;
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background-color: #dee2e6;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #adb5bd;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #f8f9fa;
    height: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background-color: #dee2e6;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #adb5bd;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* Buttons */
QPushButton {
    background-color: #0078d4;
    border: 1px solid #0078d4;
    border-radius: 6px;
    color: #ffffff;
    padding: 10px 20px;
    font-weight: 600;
    font-size: 13px;
}

QPushButton:hover {
    background-color: #106ebe;
    border-color: #106ebe;
}

QPushButton:pressed {
    background-color: #005a9e;
    border-color: #005a9e;
}

QPushButton:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #adb5bd;
}

/* Secondary Button */
QPushButton[class="secondary"] {
    background-color: transparent;
    border: 1px solid #dee2e6;
    color: #495057;
}

QPushButton[class="secondary"]:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

/* Labels */
QLabel {
    color: #495057;
    font-size: 13px;
}

QLabel[class="info"] {
    color: #0078d4;
    font-weight: 500;
    background-color: rgba(0, 120, 212, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid rgba(0, 120, 212, 0.3);
}

QLabel[class="header"] {
    font-size: 16px;
    font-weight: 600;
    color: #212529;
}

/* Modern Email Header Styling */
QLabel[class="email-subject"] {
    font-size: 18px;
    font-weight: 600;
    color: #212529;
    padding: 0px;
    margin: 0px;
}

QLabel[class="email-metadata"] {
    font-size: 13px;
    color: #6c757d;
    padding: 0px;
    margin: 0px;
    line-height: 1.4;
}

/* Structured Email Fields */
QLabel[class="field-label"] {
    font-size: 11px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0px;
    padding: 0px;
}

QLabel[class="field-value"] {
    font-size: 13px;
    color: #212529;
    margin: 0px;
    padding: 0px;
    line-height: 1.3;
}

/* Simple Gmail-style Email Header */
QLabel[class="email-from"] {
    font-size: 14px;
    color: #5f6368;
    margin: 0px;
    padding: 0px;
}

QLabel[class="email-date"] {
    font-size: 12px;
    color: #5f6368;
    margin: 0px;
    padding: 0px;
}

/* Ultra-Clean Design - My Personal Style */
QLabel[class="clean-subject"] {
    font-size: 16px;
    font-weight: 500;
    color: #2c3e50;
    background-color: transparent;
    border: none;
    padding: 20px;
    margin: 0px;
}

QFrame[class="clean-separator"] {
    background-color: #e9ecef;
    border: none;
    max-height: 1px;
    min-height: 1px;
}

/* Classic Email Header - Tight Layout */
QLabel[class="email-label"] {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-align: right;
    padding-right: 4px;
}

QLabel[class="email-value"] {
    font-size: 13px;
    color: #212529;
    padding-left: 0px;
}

/* Modern Email Header Styling */
QLabel[class="email-subject"] {
    font-size: 18px;
    font-weight: 600;
    color: #212529;
    padding: 0px;
    margin: 0px;
}

QLabel[class="email-metadata"] {
    font-size: 13px;
    color: #6c757d;
    padding: 0px;
    margin: 0px;
    line-height: 1.4;
}

QLabel[class="subheader"] {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
}

QLabel[class="muted"] {
    color: #6c757d;
    font-size: 12px;
}

/* Frames */
QFrame {
    border-radius: 8px;
}

QFrame[class="card"] {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
}

/* Seamless email viewer styling */
QFrame[class="card"] QLabel {
    background-color: transparent;
    border: none;
}

QFrame[class="card"] QTextEdit {
    background-color: #ffffff !important;
    color: #000000 !important;
    border: none;
}

/* Progress Bar */
QProgressBar {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    text-align: center;
    color: #495057;
    font-weight: 500;
}

QProgressBar::chunk {
    background-color: #0078d4;
    border-radius: 5px;
}

/* Line Edit */
QLineEdit {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    color: #495057;
    font-size: 13px;
}

QLineEdit:focus {
    border-color: #0078d4;
    outline: none;
}

/* Modern Search Frame */
QFrame[class="search-frame"] {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0px;
}

QFrame[class="search-frame"]:focus-within {
    border-color: #0078d4;
    background-color: #ffffff;
}

QLabel[class="search-icon"] {
    color: #6c757d;
    font-size: 14px;
}

QLineEdit[class="search-input"] {
    border: none;
    background: transparent;
    padding: 0px;
    font-size: 13px;
    color: #495057;
}

/* Combo Box */
QComboBox {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    color: #495057;
    font-size: 13px;
}

QComboBox:focus {
    border-color: #0078d4;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #6c757d;
    margin-right: 8px;
}

QComboBox QAbstractItemView {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    selection-background-color: #e3f2fd;
}
"""
    
    def _create_dark_theme(self) -> str:
        """Create dark theme stylesheet"""
        return """
/* Dark Theme - Modern VS Code Style */
QMainWindow {
    background-color: #1e1e1e;
    color: #cccccc;
}

QWidget {
    background-color: #1e1e1e;
    color: #cccccc;
}

/* Menu Bar */
QMenuBar {
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 4px;
    color: #ffffff;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #094771;
}

QMenuBar::item:pressed {
    background-color: #0e639c;
}

QMenu {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 8px;
    padding: 6px;
    color: #cccccc;
}

QMenu::item {
    padding: 10px 20px;
    border-radius: 6px;
    color: #cccccc;
    background-color: transparent;
}

QMenu::item:selected {
    background-color: #0e639c;
    color: #ffffff;
}

QMenu::item:disabled {
    color: #6c6c6c;
}

QMenu::separator {
    height: 1px;
    background-color: #3e3e42;
    margin: 4px 8px;
}

/* Tool Bar */
QToolBar {
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    border-radius: 6px;
    padding: 4px;
    spacing: 4px;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 6px;
    padding: 8px 12px;
    color: #ffffff;
    font-weight: 500;
}

QToolButton:hover {
    background-color: #3e3e42;
    border-color: #007acc;
}

QToolButton:pressed {
    background-color: #094771;
}

/* Status Bar */
QStatusBar {
    background-color: #2d2d30;
    border-top: 1px solid #3e3e42;
    padding: 6px;
    color: #cccccc;
}

/* Splitter */
QSplitter::handle {
    background-color: #3e3e42;
    width: 3px;
    height: 3px;
    border-radius: 1px;
}

QSplitter::handle:hover {
    background-color: #007acc;
}

/* Tree Widget (Email List) */
QTreeWidget {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 8px;
    selection-background-color: #094771;
    selection-color: #ffffff;
    outline: none;
    color: #cccccc;
    font-size: 13px;
}

QTreeWidget::item {
    padding: 10px 8px;
    border-bottom: 1px solid #2d2d30;
    color: #cccccc;
}

QTreeWidget::item:hover {
    background-color: #2d2d30;
    color: #ffffff;
}

QTreeWidget::item:selected {
    background-color: #094771;
    color: #ffffff;
}

QTreeWidget::branch {
    color: #cccccc;
}

QHeaderView::section {
    background-color: #2d2d30;
    border: none;
    border-bottom: 1px solid #3e3e42;
    padding: 8px;
    font-weight: 600;
    color: #cccccc;
}

/* Text Edit (Email Content) - Always white background even in dark mode */
QTextEdit {
    background-color: #ffffff !important;
    border: 1px solid #3e3e42;
    border-radius: 8px;
    padding: 16px;
    color: #000000 !important;
    selection-background-color: #094771;
    selection-color: #ffffff;
    font-size: 13px;
    line-height: 1.5;
}

QTextEdit:focus {
    border-color: #007acc;
    outline: none;
}

/* Scroll Bars */
QScrollBar:vertical {
    background-color: #2d2d30;
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background-color: #3e3e42;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #6c6c6c;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #2d2d30;
    height: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background-color: #3e3e42;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #6c6c6c;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* Buttons */
QPushButton {
    background-color: #0e639c;
    border: 1px solid #0e639c;
    border-radius: 6px;
    color: #ffffff;
    padding: 10px 20px;
    font-weight: 600;
    font-size: 13px;
}

QPushButton:hover {
    background-color: #1177bb;
    border-color: #1177bb;
}

QPushButton:pressed {
    background-color: #094771;
    border-color: #094771;
}

QPushButton:disabled {
    background-color: #3e3e42;
    border-color: #3e3e42;
    color: #6c6c6c;
}

/* Secondary Button */
QPushButton[class="secondary"] {
    background-color: transparent;
    border: 1px solid #3e3e42;
    color: #cccccc;
}

QPushButton[class="secondary"]:hover {
    background-color: #2d2d30;
    border-color: #6c6c6c;
}

/* Labels */
QLabel {
    color: #cccccc;
    font-size: 13px;
}

QLabel[class="info"] {
    color: #4fc3f7;
    font-weight: 500;
    background-color: rgba(79, 195, 247, 0.15);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid rgba(79, 195, 247, 0.3);
}

QLabel[class="header"] {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

/* Modern Email Header Styling - Dark Mode */
QLabel[class="email-subject"] {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    padding: 0px;
    margin: 0px;
}

QLabel[class="email-metadata"] {
    font-size: 13px;
    color: #9ca3af;
    padding: 0px;
    margin: 0px;
    line-height: 1.4;
}

/* Structured Email Fields - Dark Mode */
QLabel[class="field-label"] {
    font-size: 11px;
    font-weight: 600;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0px;
    padding: 0px;
}

QLabel[class="field-value"] {
    font-size: 13px;
    color: #ffffff;
    margin: 0px;
    padding: 0px;
    line-height: 1.3;
}

/* Simple Gmail-style Email Header - Dark Mode */
QLabel[class="email-from"] {
    font-size: 14px;
    color: #9aa0a6;
    margin: 0px;
    padding: 0px;
}

QLabel[class="email-date"] {
    font-size: 12px;
    color: #9aa0a6;
    margin: 0px;
    padding: 0px;
}

/* Ultra-Clean Design - Dark Mode */
QLabel[class="clean-subject"] {
    font-size: 16px;
    font-weight: 500;
    color: #ecf0f1;
    background-color: transparent;
    border: none;
    padding: 20px;
    margin: 0px;
}

QFrame[class="clean-separator"] {
    background-color: #3e3e42;
    border: none;
    max-height: 1px;
    min-height: 1px;
}

/* Classic Email Header - Dark Mode - Tight Layout */
QLabel[class="email-label"] {
    font-size: 12px;
    font-weight: 600;
    color: #9ca3af;
    text-align: right;
    padding-right: 4px;
}

QLabel[class="email-value"] {
    font-size: 13px;
    color: #ffffff;
    padding-left: 0px;
}

QLabel[class="subheader"] {
    font-size: 14px;
    font-weight: 500;
    color: #cccccc;
}

QLabel[class="muted"] {
    color: #6c6c6c;
    font-size: 12px;
}

/* Frames */
QFrame {
    border-radius: 8px;
}

QFrame[class="card"] {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 8px;
    padding: 16px;
}

/* Seamless email viewer styling - Dark Mode */
QFrame[class="card"] QLabel {
    background-color: transparent;
    border: none;
}

QFrame[class="card"] QTextEdit {
    background-color: #ffffff !important;
    color: #000000 !important;
    border: none;
}

/* Progress Bar */
QProgressBar {
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    border-radius: 6px;
    text-align: center;
    color: #cccccc;
    font-weight: 500;
}

QProgressBar::chunk {
    background-color: #007acc;
    border-radius: 5px;
}

/* Line Edit */
QLineEdit {
    background-color: #1e1e1e;
    border: 1px solid #3e3e42;
    border-radius: 6px;
    padding: 8px 12px;
    color: #cccccc;
    font-size: 13px;
}

QLineEdit:focus {
    border-color: #007acc;
    outline: none;
}

/* Modern Search Frame - Dark Mode */
QFrame[class="search-frame"] {
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    border-radius: 8px;
    padding: 0px;
}

QFrame[class="search-frame"]:focus-within {
    border-color: #007acc;
    background-color: #1e1e1e;
}

QLabel[class="search-icon"] {
    color: #6c6c6c;
    font-size: 14px;
}

QLineEdit[class="search-input"] {
    border: none;
    background: transparent;
    padding: 0px;
    font-size: 13px;
    color: #cccccc;
}

/* Combo Box */
QComboBox {
    background-color: #1e1e1e;
    border: 1px solid #3e3e42;
    border-radius: 6px;
    padding: 8px 12px;
    color: #cccccc;
    font-size: 13px;
}

QComboBox:focus {
    border-color: #007acc;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #6c6c6c;
    margin-right: 8px;
}

QComboBox QAbstractItemView {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 6px;
    selection-background-color: #0e639c;
    color: #cccccc;
}

QComboBox QAbstractItemView::item {
    padding: 8px 12px;
    color: #cccccc;
    background-color: transparent;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #0e639c;
    color: #ffffff;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #2d2d30;
}

/* Widget-specific styling for dark mode */
QWidget {
    background-color: #1e1e1e;
    color: #cccccc;
}

/* Ensure all text is visible in dark mode */
QWidget * {
    color: #cccccc;
}

/* Override for specific widgets that need different colors */
QTreeWidget QHeaderView::section {
    color: #cccccc;
}

/* Override removed - handled by main QTextEdit rule */

QLabel {
    color: #cccccc;
}

QPushButton {
    color: #ffffff;
}

/* Fix dropdown and menu text visibility */
QComboBox {
    color: #cccccc;
}

QMenu {
    color: #cccccc;
}

QMenu::item {
    color: #cccccc;
}

/* Ensure proper contrast */
QTreeWidget::item {
    color: #cccccc;
}

QTreeWidget::item:selected {
    color: #ffffff;
    background-color: #0e639c;
}
"""


# Global theme manager instance
theme_manager = ThemeManager()
