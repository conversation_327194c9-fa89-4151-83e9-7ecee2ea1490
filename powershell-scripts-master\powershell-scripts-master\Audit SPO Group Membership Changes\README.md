## Audit SharePoint Online Group Membership Changes using PowerShell 
This PowerShell script helps admin to audit SharePoint group membership changes and permission changes & export the report to CSV file.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Audit SharePoint Group Membership Changes](https://o365reports.com/wp-content/uploads/2022/06/audit-sharepoint-group-membership-changes.png?v=1705576513)

## Microsoft 365 Reporting Tool by AdminDroid

For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*Efficiently Track Group Membership Changes in SharePoint Online with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/22014/1/20>*
