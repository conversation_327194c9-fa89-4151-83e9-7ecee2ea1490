## Office 365 License Reporting and Management using PowerShell
This All-in-One PowerShell script can perform more than 10 Office 365 license reporting and management activities, including bulk assign or remove licenses.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Office365 License Reporting and Management](https://o365reports.com/wp-content/uploads/2021/11/Get-all-licensed-users-PowerShell.png?v=**********)
## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts.

*View more comprehensive Office 365 license report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/8/1/20>* 
