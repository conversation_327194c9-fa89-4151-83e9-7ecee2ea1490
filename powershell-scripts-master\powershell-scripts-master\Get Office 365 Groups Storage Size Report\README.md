## Get the storage used by Office 365 Groups Using PowerShell

This PowerShell script gets the storage used by Office 365 Groups using Get-UnifiedGroup & exports the Office 365 Groups storage report to a CSV file.

***Sample Output:***

This script retrieves storage usage details for Office 365 groups and exports the information to a CSV file, as shown in the screenshot below.

![storage used by Office 365 Groups](https://o365reports.com/wp-content/uploads/2022/05/Office-365-Group-Storage-Reports.png?v=1705576534)

## Microsoft 365 Reporting tool by AdminDroid

Want deeper insights into your Microsoft 365 environment? [AdminDroid's M365 Reporting tool](https://admindroid.com/?src=GitHub) offers over 1800+ pre-built reports and dashboards to simplify your management tasks.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/80041/1/20>*

