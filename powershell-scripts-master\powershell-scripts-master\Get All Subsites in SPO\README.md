## Get All Subsites in SharePoint Online Using PowerShell

Learn how to get all subsites in SharePoint Online using PowerShell and keep track of your site structure for proper organization.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![SharePoint Online Subsites Report](https://o365reports.com/wp-content/uploads/2024/06/Get-All-Subsites-in-SharePoint-Online-1024x221.png?v=1717493204)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive SharePoint Online subsites report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/30001/1/20>*  



