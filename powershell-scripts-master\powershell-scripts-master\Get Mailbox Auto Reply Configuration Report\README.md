## Get Mailbox Automatic Reply Configuration Using PowerShell

This PowerShell script gets mailbox automatic reply configuration using PowerShell and export them to CSV to find users’ out of office configuration.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Mailbox Auto Reply Configuration Report](https://o365reports.com/wp-content/uploads/2021/08/ABCBothFinal.png?v=1705576665)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive Microsoft 365 reports through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10783/1/20>*  



