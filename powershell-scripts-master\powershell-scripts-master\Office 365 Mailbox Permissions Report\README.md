## Export Office 365 Mailbox Permissions Report to CSV
This PowerShell script exports Office 365 users’ mailbox delegate permissions like Full access, Send as Send-on-behalf to CSV file with administrative roles.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below

![Mailbox Permission Report](https://o365reports.com/wp-content/uploads/2019/03/Get-mailbox-permission-report-in-Office-365-using-PowerShell.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid 

Discover [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) for comprehensive insights, featuring over 1800 reports designed for detailed analysis and enhanced understanding of your M365 environment.

*Export Office 365 Mailbox Permissions Report to CSV using AdminDroid for detailed insights: <https://demo.admindroid.com/#/1/11/reports/10641/1/20?easyFilter=%7B%22IsInbuiltAccount%22%3A0%7D>*

