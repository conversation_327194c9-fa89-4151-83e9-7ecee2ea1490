﻿## How to Delete Older Emails in Outlook Using PowerShell
This PowerShell script deletes older emails in Outlook after X number of days using the Search-Mailbox and DeleteContent.

***Sample Content:***

This script exports an output CSV file with deletion specifications that look similar to the screenshot below.

![Delete Older Emails in Outlook]( https://m365scripts.com/wp-content/uploads/2022/05/delete-emails-in-outlook-report.png)

## Microsoft 365 Auditing tool by AdminDroid
Transform your Microsoft 365 data into actionable insights with  [AdminDroid Microsoft 365 Auditing tool](https://admindroid.com/?src=GitHub), it offers over 1800 pre-built reports and dynamic dashboards to help you manage your data with ease.

*Access more comprehensive M365 reports with AdminDroid: [https://demo.admindroid.com](https://demo.admindroid.com)*

