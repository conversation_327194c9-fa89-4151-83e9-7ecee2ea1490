import requests
# Config
NTFY_URL = "https://ntfy.duksekkan.nohost.me"
NTFY_TOPIC = "LLMl9s8hdvOq6BCP"
NTFY_TOKEN = "Bearer tk_n3jhq9ds16la4c3lm6ng4e086aibl"

requests.post(f"{NTFY_URL}/{NTFY_TOPIC}", data="Test besked".encode("utf-8"), headers={"Title": "Test", "Authorization": NTFY_TOKEN})

#response = requests.post(
#    f"{NTFY_URL}/{NTFY_TOPIC}",
#    data=message.encode("utf-8"),
#    headers={
#        "Content-Type": "text/plain; charset=utf-8",
#        "Content-Disposition": "inline",
#        "Authorization": NTFY_TOKEN,
#        "Markdown": "yes",
#        "Title": "Madplan"
#    }
#)