## Audit Microsoft Teams Membership Changes using PowerShell
This PowerShell script helps admins to audit Teams membership changes, including private and shared channel membership to ensure data security.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Audit Team Membership Changes Report](https://o365reports.com/wp-content/uploads/2022/12/Audit-MS-Teams-membership-changes-using-PowerShell.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid

Take your Office 365 license management to the next level with the AdminDroid [Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1800+ pre-built reports and dashboards for in-depth analysis and efficient oversight.

*Track Teams Membership Changes with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/24300/1/20>*


