﻿## Monitor Folder Activities in SharePoint Online Using PowerShell 
Discover an easy way to track folder activities in SharePoint and OneDrive using a versatile PowerShell script.

***Sample Output:***

The script generates an output CSV file with details of folder activities in SharePoint and OneDrive. The sample output will resemble the screenshot below.

![Audit SPO Folder Activity](https://o365reports.com/wp-content/uploads/2024/12/Audit-SPO-Folder-Activity.png?v=1733116822)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive SharePoint based reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/22108/1/20/](https://demo.admindroid.com/#/1/11/reports/22108/1/20/)*







