## Export Office 365 User License Report with PowerShell
Using this script administrator can export all Office 365 licensed users with their assigned licenses, services, and service status.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below

![Office 365 License Report](https://o365reports.com/wp-content/uploads/2018/12/Office-365-User-License-report-1-768x308.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid 
Take a closer look at [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), which offers an extensive array of over 1800 reports for gaining detailed insights and a comprehensive understanding of your M365 environment.

*Export the Office 365 User License Report to CSV using AdminDroid for detailed analysis and valuable information: <https://demo.admindroid.com/#/1/11/reports/8/1/20>*

