## List All the Distribution Groups a User Is Member of Using PowerShell

This PowerShell script lists all Office 365 distribution groups a user is member of. You can also generate reports for all or list of users.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Distribution Lists a User Is Member of Report](https://o365reports.com/wp-content/uploads/2022/04/Distribution-Group-A-User-is-Member-Of-.png?v=1705576544)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive Microsoft 365 reports through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/1004/1/20>*  



