## Check Who Deleted Emails from Shared Mailbox in Microsoft 365

This PowerShell script helps to audit shared mailbox email deletion and exports key details such as deletion time, users who deleted emails, deletion type, no.of deleted emails, email subjects, folder name, and more.

***Sample Output:***

The exported audit report on deleted emails in shared mailbox looks like the screenshot below.

![Shared Mailbox Email Deletion Report](<https://o365reports.com/wp-content/uploads/2025/03/Deleted-Emails-in-Shared-Mailbox-Audit-Report.png?v=1741699809>)


## Microsoft 365 Reporting tool by AdminDroid

Transform your Microsoft 365 data into actionable insights with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), featuring over 1900+ pre-built reports and 30+ dashboards.

*Unlock comprehensive reports on Microsoft 365 email operations with AdminDroid: [https://demo.admindroid.com/#/1/11/reports/21012/1/20](https://demo.admindroid.com/#/1/11/reports/21012/1/20?easyFilter=%7B%22ExternalAccess%22%3A0%7D)*