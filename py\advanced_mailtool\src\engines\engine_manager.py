"""
Engine manager for coordinating all mail format engines
"""

from pathlib import Path
from typing import List, Optional, Dict, Any
import logging

from .base_engine import MailEngineBase, ProgressCallback, MailEngineError, engine_registry
from .mbox_engine import MboxEngine
from .pst_engine import PstEngine
from .win32_pst_engine import Win32PstEngine
from .eml_engine import EmlEngine
from .msg_engine import MsgEngine
from ..core.email_message import EmailCollection


class EngineManager:
    """Manages all mail format engines"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.engines = []
        self._initialize_engines()

    def _initialize_engines(self):
        """Initialize and register all engines"""
        engines = [
            MboxEngine(),
            Win32PstEngine(),  # Try Win32 PST first
            PstEngine(),       # Fallback PST engine
            EmlEngine(),
            MsgEngine(),
        ]

        for engine in engines:
            engine_registry.register(engine)
            self.engines.append(engine)
            self.logger.info(f"Registered engine: {engine}")
    
    def load_file(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Load emails from file using appropriate engine"""
        
        if not file_path.exists():
            raise MailEngineError(f"File does not exist: {file_path}")
        
        # Find appropriate engine
        engine = engine_registry.get_engine_for_file(file_path)
        if not engine:
            raise MailEngineError(f"No engine available for file: {file_path}")
        
        self.logger.info(f"Using {engine} for file: {file_path}")
        
        try:
            return engine.load_messages(file_path, progress)
        except Exception as e:
            self.logger.error(f"Failed to load {file_path} with {engine}: {e}")
            raise
    
    def load_multiple_files(self, file_paths: List[Path], 
                           progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Load emails from multiple files"""
        
        combined_collection = EmailCollection()
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            try:
                if progress:
                    progress.update(f"Loading file {i+1}/{total_files}: {file_path.name}", i, total_files)
                
                collection = self.load_file(file_path)
                combined_collection.add_messages(collection.messages)
                
                self.logger.info(f"Loaded {len(collection)} messages from {file_path}")
                
            except Exception as e:
                self.logger.warning(f"Failed to load {file_path}: {e}")
                continue
        
        if progress:
            progress.update(f"Loaded {len(combined_collection)} total messages", total_files, total_files)
        
        return combined_collection
    
    def save_file(self, messages: EmailCollection, file_path: Path, 
                  format_hint: Optional[str] = None,
                  progress: Optional[ProgressCallback] = None) -> bool:
        """Save emails to file using appropriate engine"""
        
        # Determine format from extension or hint
        if format_hint:
            extension = f".{format_hint.lower()}"
        else:
            extension = file_path.suffix.lower()
        
        # Find appropriate engine
        engines = engine_registry.get_engines_by_extension(extension)
        if not engines:
            raise MailEngineError(f"No engine available for format: {extension}")
        
        engine = engines[0]  # Use first available engine
        self.logger.info(f"Using {engine} to save to: {file_path}")
        
        try:
            return engine.save_messages(messages, file_path, progress)
        except Exception as e:
            self.logger.error(f"Failed to save to {file_path} with {engine}: {e}")
            raise
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """Get detailed file information"""
        
        info = {
            'path': str(file_path),
            'name': file_path.name,
            'extension': file_path.suffix.lower(),
            'exists': file_path.exists(),
            'engines': []
        }
        
        if not file_path.exists():
            return info
        
        # Check which engines can handle this file
        for engine in engine_registry.engines:
            if engine.can_handle(file_path):
                engine_info = engine.get_file_info(file_path)
                engine_info['engine_name'] = engine.engine_name
                info['engines'].append(engine_info)
        
        # If we have engines, use the first one for detailed info
        if info['engines']:
            primary_engine = engine_registry.get_engine_for_file(file_path)
            if primary_engine:
                detailed_info = primary_engine.get_file_info(file_path)
                info.update(detailed_info)
        
        return info
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """Get all supported file formats"""
        formats = {
            'import': [],
            'export': []
        }
        
        for engine in engine_registry.engines:
            # All engines support import
            formats['import'].extend(engine.supported_extensions)
            
            # Most engines support export (except PST)
            if engine.engine_name != "PST Engine":
                formats['export'].extend(engine.supported_extensions)
        
        # Remove duplicates and sort
        formats['import'] = sorted(list(set(formats['import'])))
        formats['export'] = sorted(list(set(formats['export'])))
        
        return formats
    
    def validate_files(self, file_paths: List[Path]) -> Dict[str, List[Path]]:
        """Validate multiple files and categorize them"""
        
        result = {
            'valid': [],
            'invalid': [],
            'unsupported': []
        }
        
        for file_path in file_paths:
            if not file_path.exists():
                result['invalid'].append(file_path)
                continue
            
            engine = engine_registry.get_engine_for_file(file_path)
            if engine:
                validation_result = engine.validate_file(file_path)

                # Handle both bool and tuple returns
                if isinstance(validation_result, tuple):
                    is_valid, _ = validation_result
                else:
                    is_valid = validation_result

                if is_valid:
                    result['valid'].append(file_path)
                else:
                    result['invalid'].append(file_path)
            else:
                result['unsupported'].append(file_path)
        
        return result
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get status of all engines"""
        
        status = {
            'total_engines': len(engine_registry.engines),
            'engines': [],
            'supported_extensions': engine_registry.list_supported_extensions()
        }
        
        for engine in engine_registry.engines:
            engine_status = {
                'name': engine.engine_name,
                'version': engine.engine_version,
                'extensions': engine.supported_extensions,
                'available': True
            }
            
            # Check engine-specific availability
            if hasattr(engine, 'available_methods'):
                engine_status['methods'] = engine.available_methods
            
            status['engines'].append(engine_status)
        
        return status
    
    def search_messages(self, collection: EmailCollection, query: str) -> EmailCollection:
        """Search messages in collection"""
        results = collection.search(query)
        
        search_collection = EmailCollection()
        search_collection.add_messages(results)
        
        return search_collection
    
    def filter_messages(self, collection: EmailCollection, 
                       filters: Dict[str, Any]) -> EmailCollection:
        """Filter messages based on criteria"""
        
        filtered_messages = collection.messages.copy()
        
        # Filter by sender
        if 'sender' in filters and filters['sender']:
            sender = filters['sender'].lower()
            filtered_messages = [msg for msg in filtered_messages 
                               if sender in msg.sender.lower()]
        
        # Filter by date range
        if 'date_from' in filters and filters['date_from']:
            filtered_messages = [msg for msg in filtered_messages 
                               if msg.date and msg.date >= filters['date_from']]
        
        if 'date_to' in filters and filters['date_to']:
            filtered_messages = [msg for msg in filtered_messages 
                               if msg.date and msg.date <= filters['date_to']]
        
        # Filter by attachments
        if filters.get('has_attachments', False):
            filtered_messages = [msg for msg in filtered_messages if msg.has_attachments]
        
        # Filter by read status
        if 'is_read' in filters:
            filtered_messages = [msg for msg in filtered_messages 
                               if msg.is_read == filters['is_read']]
        
        # Create filtered collection
        filtered_collection = EmailCollection()
        filtered_collection.add_messages(filtered_messages)
        
        return filtered_collection


# Global engine manager instance
engine_manager = EngineManager()
