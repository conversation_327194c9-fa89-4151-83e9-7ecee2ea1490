﻿## Find All Sharing Links in SharePoint Online
This script retrieves all types of sharing links along with link URLs, file or folder names, access types, expiry settings, and more. Use it to identify potential data exposure caused by unwanted sharing links.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![SPO sharing links report]( https://o365reports.com/wp-content/uploads/2025/06/Sharepoint-Online-Sharing-Links-Report.png)

## Microsoft 365 Reporting Tool by AdminDroid
Need more than what this script offers? Explore the [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub) to access 1800+ out-of-the-box M365 reports and insightful dashboards.

*Get more comprehensive reports to audit sharing link events through AdminDroid:*

<https://demo.admindroid.com/#/1/11/reports/22048/1/20>



