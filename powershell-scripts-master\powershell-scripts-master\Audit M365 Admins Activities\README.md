## Audit Microsoft 365 Admin Activity using PowerShell
Audit Microsoft 365 admin activity using PowerShell to ensure appropriate use of admin privileges and enhance your Microsoft 365 security.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Microsoft 365 Admin Activity Report](https://o365reports.com/wp-content/uploads/2023/12/AuditOuputFinal.png?v=1705575622)

## Microsoft 365 Reporting Tool by AdminDroid

For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*Simplify your Microsoft 365 Admin Auditing with AdminDroid Reports: <https://demo.admindroid.com/#/1/11/dashboards/57?defaultFilterUniqueId=3&easyFilter=%7B%22CreationTime%22%3A6%7D&filterId=174>*

