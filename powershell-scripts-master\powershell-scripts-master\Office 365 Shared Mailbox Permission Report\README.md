## Export Shared Mailbox Permission Report to CSV using PowerShell
 This PowerShell script exports Shared mailbox permission like full access, send as and send on behalf permissions to CSV file.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below

![Shared Mailbox permissions report.](https://o365reports.com/wp-content/uploads/2020/01/Shared-mailbox-permission-report-powershell.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid 
Take a closer look at [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), which offers an extensive array of over 1800 reports for gaining detailed insights and a comprehensive understanding of your M365 environment.

*Export Shared Mailbox Permissions to CSV using AdminDroid for detailed and actionable insights: <https://demo.admindroid.com/#/1/11/reports/10024/1/20?easyFilter=%7B%22IsInbuiltAccount%22%3A0%7D>*


