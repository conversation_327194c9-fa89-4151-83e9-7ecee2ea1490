## Export Office 365 Admin Role Report using PowerShell
This PowerShell script exports Office 365 admin report to CSV. Also, you can generate admin role group members report by using in-built params.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![AdminReport](https://o365reports.com/wp-content/uploads/2021/03/Microsoft-365-admin-report.png?v=1711111454).

## Microsoft 365 Reporting Tool by AdminDroid 
For deeper insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), featuring over 1800 ready-to-use reports and insightful dashboards.

*Streamline the export of Office 365 admin role reports with AdminDroid:<https://demo.admindroid.com/#/1/11/reports/10/1/20>*
