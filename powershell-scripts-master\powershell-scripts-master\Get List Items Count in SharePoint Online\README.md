﻿## Get List Item Count in SharePoint Online Using PowerShell 
Execute the PowerShell script to get all lists and their item count in SharePoint Online. 
***Sample Output:***

The script generates an output CSV file which will look similar to the screenshot provided below.

![Get SPO list items count](https://o365reports.com/wp-content/uploads/2024/09/Get-SPO-list-items-count-800x195.png?v=1725355412)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive SharePoint Online reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/30001/1/20](https://demo.admindroid.com/#/1/11/reports/30001/1/20)*




