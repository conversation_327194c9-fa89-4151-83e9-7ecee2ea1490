## Export Microsoft 365 Room Mailbox Reports Using PowerShell
Utilize pre-built PowerShell script to effortlessly export M365 room mailbox reports for detailed analysis and streamlined management of meeting rooms.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Room Mailbox report](https://o365reports.com/wp-content/uploads/2023/12/All-mailbox-report-Poweshell.png?v=1705575607)
## Microsoft 365 Reporting Tool by AdminDroid
Seeking in-depth analysis? [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, perfectly complementing your PowerShell scripts.

*View more comprehensive room mailbox report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10001/1/20?easyFilter=%7B%22RecipientTypeDetails%22%3A%2216%22%7D>* 
