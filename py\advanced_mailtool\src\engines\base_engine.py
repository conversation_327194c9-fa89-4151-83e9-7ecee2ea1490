"""
Base engine class for mail format handlers
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Callable, Any
from pathlib import Path
import logging

from ..core.email_message import EmailMessage, EmailCollection


class ProgressCallback:
    """Progress callback for long operations"""
    
    def __init__(self, callback: Optional[Callable[[str, int, int], None]] = None):
        self.callback = callback
    
    def update(self, message: str, current: int = 0, total: int = 0):
        """Update progress"""
        if self.callback:
            self.callback(message, current, total)


class MailEngineError(Exception):
    """Base exception for mail engine errors"""
    pass


class MailEngineBase(ABC):
    """Base class for all mail format engines"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.supported_extensions = []
        self.engine_name = "Base Engine"
        self.engine_version = "1.0"
    
    @abstractmethod
    def can_handle(self, file_path: Path) -> bool:
        """Check if this engine can handle the given file"""
        pass
    
    @abstractmethod
    def load_messages(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Load messages from file"""
        pass
    
    @abstractmethod
    def save_messages(self, messages: EmailCollection, file_path: Path, 
                     progress: Optional[ProgressCallback] = None) -> bool:
        """Save messages to file"""
        pass
    
    def get_file_info(self, file_path: Path) -> dict:
        """Get basic file information"""
        try:
            stat = file_path.stat()
            return {
                'size': stat.st_size,
                'size_mb': stat.st_size / (1024 * 1024),
                'modified': stat.st_mtime,
                'exists': True,
                'readable': file_path.is_file(),
                'extension': file_path.suffix.lower()
            }
        except Exception as e:
            return {
                'size': 0,
                'size_mb': 0.0,
                'modified': 0,
                'exists': False,
                'readable': False,
                'extension': '',
                'error': str(e)
            }
    
    def validate_file(self, file_path: Path) -> tuple[bool, str]:
        """Validate file before processing"""
        if not file_path.exists():
            return False, f"File does not exist: {file_path}"
        
        if not file_path.is_file():
            return False, f"Path is not a file: {file_path}"
        
        if file_path.stat().st_size == 0:
            return False, f"File is empty: {file_path}"
        
        try:
            with open(file_path, 'rb') as f:
                f.read(1)  # Try to read first byte
        except PermissionError:
            return False, f"Permission denied: {file_path}"
        except Exception as e:
            return False, f"Cannot read file: {e}"
        
        return True, "File is valid"
    
    def log_info(self, message: str):
        """Log info message"""
        self.logger.info(message)
    
    def log_warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)
    
    def log_error(self, message: str):
        """Log error message"""
        self.logger.error(message)
    
    def __str__(self) -> str:
        return f"{self.engine_name} v{self.engine_version}"


class EngineRegistry:
    """Registry for mail engines"""
    
    def __init__(self):
        self.engines: List[MailEngineBase] = []
    
    def register(self, engine: MailEngineBase):
        """Register an engine"""
        self.engines.append(engine)
        logging.info(f"Registered engine: {engine}")
    
    def get_engine_for_file(self, file_path: Path) -> Optional[MailEngineBase]:
        """Get appropriate engine for file"""
        for engine in self.engines:
            if engine.can_handle(file_path):
                return engine
        return None
    
    def get_engines_by_extension(self, extension: str) -> List[MailEngineBase]:
        """Get engines that support given extension"""
        extension = extension.lower()
        return [engine for engine in self.engines 
                if extension in engine.supported_extensions]
    
    def list_supported_extensions(self) -> List[str]:
        """Get all supported extensions"""
        extensions = set()
        for engine in self.engines:
            extensions.update(engine.supported_extensions)
        return sorted(list(extensions))
    
    def get_engine_info(self) -> List[dict]:
        """Get information about all registered engines"""
        return [
            {
                'name': engine.engine_name,
                'version': engine.engine_version,
                'extensions': engine.supported_extensions,
                'class': engine.__class__.__name__
            }
            for engine in self.engines
        ]


# Global engine registry
engine_registry = EngineRegistry()
