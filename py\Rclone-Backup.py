import os
import json
import subprocess
import requests
import sys
from datetime import datetime

# Configuration
SOURCE_DIR = "/home/<USER>/scripts"
DEST = "GDrive:Rclone"
LOG_FILE = "/home/<USER>/rclone.log"
STATE_FILE = "/home/<USER>/scripts/rclone_state.json"
NTFY_URL = "https://ntfy.sh/dit_emne"

# Debug mode - set to True for verbose output, False for silent operation
DEBUG = "--debug" in sys.argv or "-v" in sys.argv

def debug_print(message):
    """Print message only if DEBUG mode is enabled"""
    if DEBUG:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")

def get_file_state(path):
    """Get file modification time and size"""
    return {
        "mtime": os.path.getmtime(path),
        "size": os.path.getsize(path)
    }

def load_state():
    """Load previous backup state from JSON file"""
    if os.path.exists(STATE_FILE):
        try:
            with open(STATE_FILE, "r") as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            debug_print(f"Fejl ved indlæsning af state fil: {e}")
            return {}
    return {}

def save_state(state):
    """Save current backup state to JSON file"""
    try:
        with open(STATE_FILE, "w") as f:
            json.dump(state, f, indent=2)
        debug_print(f"State gemt til {STATE_FILE}")
    except IOError as e:
        debug_print(f"Fejl ved gemning af state fil: {e}")

def send_notification(message):
    """Send notification via ntfy"""
    try:
        response = requests.post(NTFY_URL, data=message.encode("utf-8"), timeout=10)
        if response.status_code == 200:
            debug_print("Notifikation sendt succesfuldt")
        else:
            debug_print(f"Fejl ved sending af notifikation: {response.status_code}")
    except requests.RequestException as e:
        debug_print(f"Fejl ved sending af notifikation: {e}")

def run_rclone_sync():
    """Run rclone sync to synchronize directories (including deletions)"""
    cmd = [
        "rclone", "sync", SOURCE_DIR, DEST,
        "--log-file", LOG_FILE,
        "--delete-during",  # Delete files during sync
        "--transfers", "4",  # Parallel transfers
        "--checkers", "8"    # Parallel file checks
    ]

    if DEBUG:
        cmd.append("--verbose")

    debug_print(f"Kører rclone sync: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            debug_print("Rclone sync gennemført succesfuldt")
            return True
        else:
            debug_print(f"Rclone sync fejlede med kode {result.returncode}")
            debug_print(f"Stderr: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        debug_print("Rclone sync timeout efter 5 minutter")
        return False
    except Exception as e:
        debug_print(f"Fejl ved kørsel af rclone: {e}")
        return False

def main():
    """Main backup function"""
    debug_print("Starter backup proces...")

    # Check if source directory exists
    if not os.path.exists(SOURCE_DIR):
        debug_print(f"Fejl: Kilde mappe {SOURCE_DIR} findes ikke")
        return 1

    # Load previous state
    prev_state = load_state()
    curr_state = {}
    changed_files = []
    deleted_files = []

    debug_print(f"Scanner filer i {SOURCE_DIR}...")

    # Scan current files
    for root, _, files in os.walk(SOURCE_DIR):
        for fname in files:
            fpath = os.path.join(root, fname)
            rel_path = os.path.relpath(fpath, SOURCE_DIR)
            curr_state[rel_path] = get_file_state(fpath)

            prev = prev_state.get(rel_path)
            curr = curr_state[rel_path]

            if not prev or prev["mtime"] != curr["mtime"] or prev["size"] != curr["size"]:
                changed_files.append(rel_path)
                debug_print(f"Ændret/ny fil: {rel_path}")

    # Find deleted files
    for rel_path in prev_state:
        if rel_path not in curr_state:
            deleted_files.append(rel_path)
            debug_print(f"Slettet fil: {rel_path}")

    # Determine if sync is needed
    needs_sync = bool(changed_files or deleted_files)

    if needs_sync:
        debug_print(f"Fundet {len(changed_files)} ændrede/nye filer og {len(deleted_files)} slettede filer")

        # Run rclone sync
        if run_rclone_sync():
            # Save new state only if sync was successful
            save_state(curr_state)

            # Send notification
            total_changes = len(changed_files) + len(deleted_files)
            msg = f"Backup gennemført: {len(changed_files)} ændrede/nye, {len(deleted_files)} slettede filer"
            send_notification(msg)
            debug_print("Backup gennemført succesfuldt")
            return 0
        else:
            debug_print("Backup fejlede - state ikke opdateret")
            send_notification("Backup fejlede - tjek logs")
            return 1
    else:
        debug_print("Ingen ændringer fundet - ingen backup nødvendig")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)