## Export Office 365 Spam and Malware Reports using PowerShell

A detailed guide to obtain Microsoft 365 spam and malware reports using PowerShell script to enhance Exchange Online email security.

***Sample Output:***

This script exports Office 365 spam, malware and phish report to CSV file that looks like the screenshot below.

![Office 365 Spam and Malware Report](https://o365reports.com/wp-content/uploads/2021/05/Spam-Email-Received-Report.png?v=1705575820)

## Microsoft 365 Reporting tool by AdminDroid

Stay protected by tracking Office 365 spam, malware, and phish with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)!, featuring over 1800+ pre-built reports and insightful dashboards.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10313/1/20>*
