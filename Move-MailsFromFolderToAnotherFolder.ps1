
<#
.author       : JDH_admin

.SYNOPSIS
    Move mails from one folder to another in a mailbox using Microsoft Graph API

.DESCRIPTION
    This script connects to Microsoft Graph API and moves mails from one folder to another in a mailbox.
    It retrieves the folder IDs for the source and destination folders, then moves all mails from the source folder to the destination folder.
    The script uses the Microsoft Graph PowerShell SDK to perform the operations.

.NOTES
    Vigtigt: Du skal have installeret Microsoft.Graph PowerShell SDK for at kunne køre dette script.
    Du kan installere det ved at køre følgende kommando i PowerShell:       
    Install-Module Microsoft.Graph -Scope CurrentUser -AllowClobber -Force

    Login ind med den bruger du vil flytte mails for.
    Giv $parentFolderID og $childFolderID de rigtige værdier.
    Mails flyttes fra $childFolderID til $parentFolderID.
    Dette er udarbejdet fordi mail mails skulle flyttes fra en mappe til en anden i en mailboks ifm. Horten migreringen.
    
#>

# Login til MGraph, men den bruger du vil flytte mails for
Connect-MgGraph -Scopes "Mail.ReadWrite"

# Hent parent folders
$UserId = "<EMAIL>"
Get-MgUserMailFolder -UserId $UserId

# Get folder ID på parent folder. Sendt Post, Indbakke, Arkiv, Slettet Post osv.
$parentFolder = "Sendt Post"
$parentFolderID = (Get-MgUserMailFolder -UserId $UserId | Where-Object {$_.Displayname -eq $parentFolder}).Id

# Get folder ID på child folder.
$childFolder = "Sendt post"
$childFolderID = (Get-MgUserMailFolderChildFolder -UserId $UserId -MailFolderId $parentFolderID | Where-Object {$_.DisplayName -eq $childFolder}).Id

# Get alle mails i mappen
$messages = Get-MgUserMailFolderMessage -UserId $UserId -MailFolderId $childFolderID -All

# Flyt mails fra child folder til parent folder.
foreach ($message in $messages) {
    Move-MgUserMessage -UserId $UserId -MessageId $message.Id -DestinationId $parentFolderID -Verbose
    Write-Host "Moved: $($message.Subject)"
}

Disconnect-MgGraph