"""
MSG format engine for Outlook message files
"""

from pathlib import Path
from typing import Optional
import tempfile
import os

from .base_engine import Mail<PERSON>ngineBase, ProgressCallback, MailEngineError
from ..core.email_message import EmailMessage, EmailCollection, Attachment

# Try to import extract-msg
try:
    import extract_msg
    EXTRACT_MSG_AVAILABLE = True
except ImportError:
    EXTRACT_MSG_AVAILABLE = False


class MsgEngine(MailEngineBase):
    """Engine for MSG format files (Outlook messages)"""
    
    def __init__(self):
        super().__init__()
        self.engine_name = "MSG Engine"
        self.supported_extensions = ['.msg']
    
    def can_handle(self, file_path: Path) -> bool:
        """Check if this is a MSG file"""
        if file_path.suffix.lower() in self.supported_extensions:
            return True
        
        # Check MSG signature (OLE compound document)
        try:
            with open(file_path, 'rb') as f:
                signature = f.read(8)
                return signature == b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
        except Exception:
            return False
    
    def load_messages(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Load MSG message"""
        
        if not EXTRACT_MSG_AVAILABLE:
            raise MailEngineError(
                "extract-msg library not available. "
                "Install with: pip install extract-msg"
            )
        
        # Validate file
        is_valid, error_msg = self.validate_file(file_path)
        if not is_valid:
            raise MailEngineError(error_msg)
        
        collection = EmailCollection()
        
        if progress:
            progress.update("Loading MSG file...", 0, 1)
        
        try:
            # Open MSG file
            msg = extract_msg.Message(str(file_path))
            
            # Convert to EmailMessage
            email_message = self._parse_msg(msg, file_path)
            if email_message:
                collection.add_message(email_message)
            
            if progress:
                progress.update("MSG file loaded", 1, 1)
            
            self.log_info(f"Successfully loaded MSG file: {file_path}")
            return collection
            
        except Exception as e:
            error_msg = f"Failed to load MSG file {file_path}: {e}"
            self.log_error(error_msg)
            raise MailEngineError(error_msg)
    
    def save_messages(self, messages: EmailCollection, file_path: Path, 
                     progress: Optional[ProgressCallback] = None) -> bool:
        """Save messages as MSG files"""
        
        if len(messages) == 0:
            raise MailEngineError("No messages to save")
        
        try:
            if len(messages) == 1:
                # Save single MSG file (not directly supported - convert to EML)
                return self._save_as_eml_alternative(messages[0], file_path, progress)
            else:
                # Save multiple MSG files (convert to EML)
                return self._save_multiple_as_eml(messages, file_path, progress)
                
        except Exception as e:
            error_msg = f"Failed to save MSG file(s): {e}"
            self.log_error(error_msg)
            raise MailEngineError(error_msg)
    
    def _parse_msg(self, msg, source_file: Path) -> Optional[EmailMessage]:
        """Parse MSG object to EmailMessage"""
        try:
            # Extract basic fields
            subject = getattr(msg, 'subject', '') or '(No Subject)'
            sender = getattr(msg, 'sender', '') or ''
            sender_name = getattr(msg, 'senderName', '') or ''
            
            # Recipients
            recipients = []
            if hasattr(msg, 'to') and msg.to:
                recipients = [addr.strip() for addr in msg.to.split(';') if addr.strip()]
            
            cc = []
            if hasattr(msg, 'cc') and msg.cc:
                cc = [addr.strip() for addr in msg.cc.split(';') if addr.strip()]
            
            bcc = []
            if hasattr(msg, 'bcc') and msg.bcc:
                bcc = [addr.strip() for addr in msg.bcc.split(';') if addr.strip()]
            
            # Date
            date = getattr(msg, 'date', None)
            
            # Message ID
            message_id = getattr(msg, 'messageId', '') or ''
            
            # Body content
            body_text = getattr(msg, 'body', '') or ''
            body_html = getattr(msg, 'htmlBody', '') or ''
            
            # Attachments
            attachments = []
            if hasattr(msg, 'attachments') and msg.attachments:
                for att in msg.attachments:
                    attachment = self._create_attachment_from_msg(att)
                    if attachment:
                        attachments.append(attachment)
            
            # Create EmailMessage
            email_message = EmailMessage(
                subject=subject,
                sender=sender,
                sender_name=sender_name,
                recipients=recipients,
                cc=cc,
                bcc=bcc,
                body_text=body_text,
                body_html=body_html,
                message_id=message_id,
                date=date,
                source_file=str(source_file),
                source_format='msg',
                attachments=attachments
            )
            
            # Add additional properties as headers
            for prop_name in dir(msg):
                if not prop_name.startswith('_') and not callable(getattr(msg, prop_name)):
                    try:
                        value = getattr(msg, prop_name)
                        if isinstance(value, (str, int, float)) and value:
                            email_message.set_header(f'msg-{prop_name}', str(value))
                    except Exception:
                        continue
            
            return email_message
            
        except Exception as e:
            self.log_warning(f"Error parsing MSG file: {e}")
            return None
    
    def _create_attachment_from_msg(self, att) -> Optional[Attachment]:
        """Create attachment from MSG attachment object"""
        try:
            filename = getattr(att, 'longFilename', None) or getattr(att, 'shortFilename', 'attachment')
            
            # Get attachment data
            data = None
            size = 0
            
            try:
                if hasattr(att, 'data'):
                    data = att.data
                    size = len(data) if data else 0
            except Exception:
                pass
            
            # Content type (try to guess from filename)
            content_type = 'application/octet-stream'
            if filename:
                import mimetypes
                guessed_type, _ = mimetypes.guess_type(filename)
                if guessed_type:
                    content_type = guessed_type
            
            return Attachment(
                filename=filename,
                content_type=content_type,
                size=size,
                data=data,
                is_inline=False
            )
            
        except Exception as e:
            self.log_warning(f"Error creating attachment: {e}")
            return None
    
    def _save_as_eml_alternative(self, message: EmailMessage, file_path: Path, 
                                progress: Optional[ProgressCallback] = None) -> bool:
        """Save as EML since direct MSG creation is complex"""
        
        if progress:
            progress.update("Converting MSG to EML format...", 0, 1)
        
        # Change extension to .eml
        eml_path = file_path.with_suffix('.eml')
        
        # Use EML engine to save
        from .eml_engine import EmlEngine
        eml_engine = EmlEngine()
        
        collection = EmailCollection()
        collection.add_message(message)
        
        result = eml_engine.save_messages(collection, eml_path, progress)
        
        self.log_info(f"Saved MSG as EML: {eml_path}")
        return result
    
    def _save_multiple_as_eml(self, messages: EmailCollection, base_path: Path, 
                             progress: Optional[ProgressCallback] = None) -> bool:
        """Save multiple messages as EML files"""
        
        # Use EML engine for multiple files
        from .eml_engine import EmlEngine
        eml_engine = EmlEngine()
        
        # Change base path to indicate EML format
        if base_path.suffix == '.msg':
            base_path = base_path.with_suffix('_eml')
        
        result = eml_engine.save_messages(messages, base_path, progress)
        
        self.log_info(f"Saved {len(messages)} MSG files as EML format")
        return result
    
    def get_msg_info(self, file_path: Path) -> dict:
        """Get detailed MSG file information"""
        info = self.get_file_info(file_path)
        
        if not EXTRACT_MSG_AVAILABLE:
            info['msg_info'] = "extract-msg library not available"
            return info
        
        try:
            msg = extract_msg.Message(str(file_path))
            
            msg_info = {
                'subject': getattr(msg, 'subject', ''),
                'sender': getattr(msg, 'sender', ''),
                'date': str(getattr(msg, 'date', '')),
                'attachment_count': len(msg.attachments) if hasattr(msg, 'attachments') else 0,
                'has_html': bool(getattr(msg, 'htmlBody', '')),
                'message_class': getattr(msg, 'messageClass', ''),
            }
            
            info['msg_info'] = msg_info
            
        except Exception as e:
            info['msg_info'] = f"Error reading MSG: {e}"
        
        return info
