@echo off
echo 📧 Starting Advanced MailTool...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8 or newer
    pause
    exit /b 1
)

REM Check if requirements are installed
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Dependencies not found. Installing...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Start the application
echo ✅ Starting Advanced MailTool...
python main.py

if errorlevel 1 (
    echo.
    echo ❌ Application exited with error
    pause
)
