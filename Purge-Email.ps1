# Connect to SCC (Security and Compliance)
Connect-IPPSSession -UserPrincipalName "<EMAIL>"

# Search variables here - !! Remember to change $ComplianceSearchName and $emailSubject:
$ComplianceSearchName = "Remove Phishing Message_IT"
$currentDate          = (get-date -Format "MM/dd/yyyy").Replace('-','/')
$dateMinusDays       = ((get-date).AddDays(-3) | get-date -Format "MM/dd/yyyy").Replace('-','/')

# Create ComplianceSearch - By subject
$emailSubject         = '"*Kundesupport Brobizz sent you Brobizz Support Case via PandaDoc*"'
$Search               = New-ComplianceSearch -Name $ComplianceSearchName -ExchangeLocation All -ContentMatchQuery "(Received:$dateMinusDays..$currentDate) AND (Subject:$emailSubject)"

# Create ComplianceSearch - By sender
$fromEmail            = "<EMAIL>"
$Search               = New-ComplianceSearch -Name $ComplianceSearchName -ExchangeLocation All -ContentMatchQuery "(Received:$dateMinusDays..$currentDate) AND (From:$fromEmail)"

# Log
$timestamp = (Get-Date -Format "dd-MM-yyyy HH:mm:ss")
$info      = "[$timestamp] [INFO]"
$success   = "[$timestamp] [SUCCESS]"
# Start ComplianceSearch
Start-ComplianceSearch -Identity $Search.Identity

# Vent på, at compliance search er fuldført
$status = ""
while ($status -ne "Completed") {
    # Hent status for compliance search
    $status = (Get-ComplianceSearch -Identity $ComplianceSearchName).Status
    if ($status -ne "Completed") {
        Write-Host "$info Current status: $status" -ForegroundColor Gray
    } else {
        Write-Host "$success Current status: $status" -ForegroundColor Green
    }
    # Vent i et par sekunder før næste tjek (tilpas ventetiden efter behov)
    Start-Sleep -Seconds 5
}

Write-Host "`nCompliance search is completed. Proceeding with the script." -ForegroundColor Green

########### Wait for Get-ComplianceSearch status i Completed before proceeding ###########

# Create preview and show the preview output
New-ComplianceSearchAction -SearchName $ComplianceSearchName -Preview -Confirm:$false

########### Wait for Get-ComplianceSearchAction status i Completed before proceeding ###########

# Vent på, at complianceSearchAction er fuldført
$status = ""
while ($status -ne "Completed") {
    # Hent status for compliance search
    $status = (Get-ComplianceSearchAction -Identity $ComplianceSearchName"_Preview").Status
    if ($status -ne "Completed") {
        Write-Host "$info Current status (Preview): $status" -ForegroundColor Gray
    } else {
        Write-Host "$success Current status (Preview): $status" -ForegroundColor Green
    }
    # Vent i et par sekunder før næste tjek (tilpas ventetiden efter behov)
    Start-Sleep -Seconds 5
}

Write-Host "`nComplianceSearchAction is completed. Proceeding with the script." -ForegroundColor Green

(Get-ComplianceSearchAction $ComplianceSearchName"_Preview" -ResultSize Unlimited | Select-Object -ExpandProperty Results) -split ","
# Get-ComplianceSearchAction -Identity $ComplianceSearchName"_Preview" | Format-List

# Purge email and show purged email when done
    # Soft delete (will end in users recovery bin)
#New-ComplianceSearchAction -SearchName $ComplianceSearchName -Purge -PurgeType SoftDelete -Confirm:$false

#Hard delete will remove the emails from all places
New-ComplianceSearchAction -SearchName "$ComplianceSearchName" -Purge -PurgeType HardDelete -Confirm:$false

########### Wait for Get-ComplianceSearchAction status i Completed before proceeding ###########

# Vent på, at complianceSearchAction er fuldført
$status = ""
while ($status -ne "Completed") {
    # Hent status for compliance search
    $status = (Get-ComplianceSearchAction -Identity $ComplianceSearchName"_Purge").Status
    if ($status -ne "Completed") {
        Write-Host "$info Current status (Purge): $status" -ForegroundColor Gray
    } elseif ($status -eq "Completed") {
        Write-Host "$success Current status (Purge): $status" -ForegroundColor Green
    } {
        
    }
    # Vent i et par sekunder før næste tjek (tilpas ventetiden efter behov)
    Start-Sleep -Seconds 5
}

# Display purged emails
(Get-ComplianceSearchAction $ComplianceSearchName"_Purge" | Select-Object -ExpandProperty Results) -split ","

# Remove the ComplianceSearch
Remove-ComplianceSearch $ComplianceSearchName -Confirm:$false
# Get-ComplianceSearch | Remove-ComplianceSearch -Confirm:$false

# Disconnect from EXO/SCC
Disconnect-ExchangeOnline -Confirm:$false