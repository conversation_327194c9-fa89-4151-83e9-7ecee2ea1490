# Importer Hyper-V modulet
Import-Module Hyper-V

# Indstil e-mail indstillinger
$smtpServer = "smtp.yourserver.com"
$smtpFrom = "<EMAIL>"
$smtpTo = "<EMAIL>"
$smtpSubject = "Hyper-V VM Alert: Ældre end 5 år"

# Funktion til at sende e-mail
function Send-Email {
    param (
        [string]$subject,
        [string]$body
    )
    $message = @{
        To       = $smtpTo
        From     = $smtpFrom
        Subject  = $subject
        Body     = $body
        SmtpServer = $smtpServer
    }
    Send-MailMessage @message
}

# Hent alle virtuelle maskiner
$vms = Get-VM

# Dagens dato
$today = Get-Date

# Tærskel for 5 år
$threshold = $today.AddYears(-5)

# Loop gennem alle virtuelle maskiner
foreach ($vm in $vms) {
    $vmName = $vm.Name
    $creationDate = (Get-VM -Name $vmName).CreationTime

    if ($creationDate -lt $threshold) {
        # VM er ældre end 5 år
        $body = "Den virtuelle maskine '$vmName' har en creation date på $creationDate, hvilket er ældre end 5 år."
        Write-Output $body
        Send-Email -subject $smtpSubject -body $body
    }
}