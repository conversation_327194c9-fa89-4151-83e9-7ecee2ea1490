## Manage and Report Microsoft Teams Tag Members Using PowerShell

Download this handy PowerShell script to view and manage Microsoft Teams
tag members for your entire organization

***Sample Output:***

The script exports an output CSV file that looks similar to the
screenshot below.

![Microsoft Teams Tag Members
Report](https://o365reports.com/wp-content/uploads/2024/07/Get-Microsoft-Teams-Tags-and-Tag-Members-Report\--768x181.png?v=1719917245)

## Microsoft 365 Reporting Tool by AdminDroid

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting
tool](https://admindroid.com/?src=GitHub) offers an extensive collection
of over 1800 out-of-the-box reports and dashboards. It's the perfect
complement to your PowerShell scripts.

*View more comprehensive Microsoft Teams reports through AdminDroid:
<https://demo.admindroid.com/#/1/11/reports/70021/1/20>*
