## Audit & Report MS Teams Channel Creations Using PowerShell
Audit Microsoft Teams channel creations using PowerShell to ensure efficient resource management and security in the organization.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Microsoft 365 Teams Channel Creations](https://o365reports.com/wp-content/uploads/2023/12/Audit-Teams-Channels-768x167.png?v=1705575613)

## Microsoft 365 Reporting Tool by AdminDroid

For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*Simplify Microsoft Teams Channel Management with AdminDroid! <https://demo.admindroid.com/#/1/11/reports/24003/1/20>*
