## Export Office 365 Users’ Last Password Change Date to CSV
This PowerShell script exports 7+ Microsoft 365 last password change date &amp; Password expiry reports to CSV.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below

![Password Expiry Date Report](https://o365reports.com/wp-content/uploads/2020/02/M365-users-password-expiry-report-768x192.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid 

Take a deeper look into [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), boasting an extensive array of over 1800 reports to provide thorough insights and a comprehensive overview of your M365 environment.

*Export Password Last Change Date and Password Expiry Reports to CSV using AdminDroid for detailed insights: <https://demo.admindroid.com/#/1/11/reports/133/1/20?easyFilter=%7B%22accountEnabled%22%3A1%7D>*

