﻿## Connect to all Microsoft 365 Services using PowerShell
This “All-in-One” PowerShell script helps to connect all Microsoft 365 services using PowerShell with MFA/non-MFA account.

***Sample Output:***

This All-in-One PowerShell script connects various Microsoft 365 PowerShell modules, including MS Graph, Exchange Online, Azure AD, SharePoint Online, SharePoint PnP, Teams, and Compliance Center using either an MFA/non-MFA account or certificate. The output of the script looks similar to the screenshot below.

![Script execution]( https://o365reports.com/wp-content/uploads/2019/10/Connect-to-all-Microsoft-365-services-using-PowerShell.png)

## Microsoft 365 Reporting tool by AdminDroid
Want to enhance your reporting capabilities? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) provides 1800+ pre-built reports and dashboards for comprehensive M365 insights.

*Find more comprehensive reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/20300/1/20](https://demo.admindroid.com/#/1/11/reports/20300/1/20)*

