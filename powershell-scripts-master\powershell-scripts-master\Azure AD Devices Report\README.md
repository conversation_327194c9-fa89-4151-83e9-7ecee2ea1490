﻿## Get Azure AD Devices Report Using PowerShell
Get Azure AD devices report using PowerShell now for continuous monitoring of devices in and around your organization and thereby protecting the Office 365.

***Sample Output:***

The script exports an output CSV file that provides details of all registered devices in Azure AD with their other attributes. The output looks similar to the screenshot below.

![Azure AD Devices report]( https://o365reports.com/wp-content/uploads/2023/04/Sample-output-1536x536.png)

## Microsoft 365 Auditing tool by AdminDroid
If you find this script beneficial, you’ll be amazed by the [AdminDroid Microsoft 365 auditing tool](https://admindroid.com/?src=GitHub)! Unlock 1800+ pre-built reports and dashboards to dive deeper into your M365 data.

*Access detailed Azure AD devices auditing report through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/20142/1/20](https://demo.admindroid.com/#/1/11/reports/20142/1/20)*

