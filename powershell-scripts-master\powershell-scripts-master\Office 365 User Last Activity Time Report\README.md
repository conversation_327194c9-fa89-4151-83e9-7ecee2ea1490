## Export Office 365 users Real Last Logon Time Report to CSV
The Last Logon Time attribute gives inaccurate data to identify the Office 365 mailbox’s inactivity. Users&#039; real last logon time report can be retrieved from

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below

![Last Logon Time (Last Activity Time) Report](https://o365reports.com/wp-content/uploads/2019/06/Export-Office-365-users-last-logon-time-report-to-csv-using-PowerShell-768x145.png?v=1705576872)

## Microsoft 365 Reporting Tool by AdminDroid 

Take a closer look at [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), which offers an extensive array of over 1800 reports for gaining detailed insights and a comprehensive understanding of your M365 environment.

*Export the Office 365 Users' Real Last Logon Time Report to CSV using AdminDroid for precise insight: <https://demo.admindroid.com/#/1/11/reports/20164/1/20?easyFilter=%7B%22CreationTime%22%3A1%7D>*
