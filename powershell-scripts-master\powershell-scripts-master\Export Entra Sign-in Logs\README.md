## **Get Entra Sign-in Logs**

Discover how to export comprehensive sign-in reports for Microsoft 365
users using PowerShell. Enhance security with practical execution
examples for streamlined reporting.

***Sample Output***

The exported report on Microsoft Entra ID sign-in logs looks like the
screenshot below.

![M365 User Sign-in
Report](https://o365reports.com/wp-content/uploads/2024/07/Export-Microsoft-365-Users-Sign-in-Report-Using-PowerShell.png?v=1719913223)

## **AdminDroid: Your Go-To Tool for Microsoft 365 Reporting**

Need more than what this script offers? Explore [AdminDroid Microsoft
365 reporting tool](https://admindroid.com/?src=GitHub) to get access to
1800+ out-of-box M365 reports and insightful dashboards.

*View more comprehensive M365 user sign-in reports through AdminDroid:<https://demo.admindroid.com/#/1/11/reports/20161/1/20>*