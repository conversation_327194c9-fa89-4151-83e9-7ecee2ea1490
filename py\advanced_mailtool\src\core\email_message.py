"""
Enhanced email message class with full metadata and attachment support
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import email.utils
from pathlib import Path


@dataclass
class Attachment:
    """Represents an email attachment"""
    filename: str
    content_type: str = "application/octet-stream"
    size: int = 0
    data: Optional[bytes] = None
    content_id: Optional[str] = None
    is_inline: bool = False
    
    def save_to_file(self, path: Union[str, Path]) -> bool:
        """Save attachment to file"""
        try:
            if self.data:
                with open(path, 'wb') as f:
                    f.write(self.data)
                return True
        except Exception:
            pass
        return False
    
    @property
    def size_mb(self) -> float:
        """Get size in MB"""
        return self.size / (1024 * 1024) if self.size else 0.0


@dataclass
class EmailMessage:
    """Enhanced email message with full metadata"""
    
    # Core fields
    subject: str = ""
    sender: str = ""
    sender_name: str = ""
    recipients: List[str] = field(default_factory=list)
    cc: List[str] = field(default_factory=list)
    bcc: List[str] = field(default_factory=list)
    
    # Content
    body_text: str = ""
    body_html: str = ""
    
    # Metadata
    message_id: str = ""
    date: Optional[datetime] = None
    received_date: Optional[datetime] = None
    
    # Headers
    headers: Dict[str, str] = field(default_factory=dict)
    
    # Attachments
    attachments: List[Attachment] = field(default_factory=list)
    
    # Source information
    source_file: str = ""
    source_format: str = ""  # mbox, pst, msg, eml
    folder_path: str = ""
    
    # Flags and status
    is_read: bool = False
    is_flagged: bool = False
    is_deleted: bool = False
    priority: str = "normal"  # low, normal, high
    
    # Additional metadata
    thread_id: str = ""
    conversation_id: str = ""
    size: int = 0
    
    def __post_init__(self):
        """Post-initialization processing"""
        if not self.message_id:
            self.message_id = f"<{id(self)}@advanced-mailtool>"
        
        # Parse date if it's a string
        if isinstance(self.date, str):
            self.date = self._parse_date(self.date)
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string to datetime"""
        try:
            return email.utils.parsedate_to_datetime(date_str)
        except (ValueError, TypeError):
            return None
    
    @property
    def sender_display(self) -> str:
        """Get display name for sender"""
        if self.sender_name:
            return f"{self.sender_name} <{self.sender}>"
        return self.sender
    
    @property
    def recipients_display(self) -> str:
        """Get formatted recipients string"""
        return ", ".join(self.recipients)
    
    @property
    def has_attachments(self) -> bool:
        """Check if message has attachments"""
        return len(self.attachments) > 0
    
    @property
    def attachment_count(self) -> int:
        """Get number of attachments"""
        return len(self.attachments)
    
    @property
    def total_attachment_size(self) -> int:
        """Get total size of all attachments"""
        return sum(att.size for att in self.attachments)
    
    @property
    def date_display(self) -> str:
        """Get formatted date string"""
        if self.date:
            return self.date.strftime("%Y-%m-%d %H:%M")
        return "Unknown"
    
    @property
    def size_mb(self) -> float:
        """Get message size in MB"""
        return self.size / (1024 * 1024) if self.size else 0.0
    
    def get_header(self, name: str, default: str = "") -> str:
        """Get header value"""
        return self.headers.get(name.lower(), default)
    
    def set_header(self, name: str, value: str):
        """Set header value"""
        self.headers[name.lower()] = value
    
    def add_attachment(self, attachment: Attachment):
        """Add attachment to message"""
        self.attachments.append(attachment)
    
    def get_body(self, prefer_html: bool = False) -> str:
        """Get message body, preferring HTML or text"""
        if prefer_html and self.body_html:
            return self.body_html
        return self.body_text or self.body_html
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'subject': self.subject,
            'sender': self.sender,
            'sender_name': self.sender_name,
            'recipients': self.recipients,
            'cc': self.cc,
            'bcc': self.bcc,
            'body_text': self.body_text,
            'body_html': self.body_html,
            'message_id': self.message_id,
            'date': self.date.isoformat() if self.date else None,
            'received_date': self.received_date.isoformat() if self.received_date else None,
            'headers': self.headers,
            'attachments': [
                {
                    'filename': att.filename,
                    'content_type': att.content_type,
                    'size': att.size,
                    'content_id': att.content_id,
                    'is_inline': att.is_inline
                }
                for att in self.attachments
            ],
            'source_file': self.source_file,
            'source_format': self.source_format,
            'folder_path': self.folder_path,
            'is_read': self.is_read,
            'is_flagged': self.is_flagged,
            'is_deleted': self.is_deleted,
            'priority': self.priority,
            'thread_id': self.thread_id,
            'conversation_id': self.conversation_id,
            'size': self.size
        }
    
    def __str__(self) -> str:
        """String representation"""
        return f"EmailMessage(subject='{self.subject}', sender='{self.sender}', date='{self.date_display}')"
    
    def __repr__(self) -> str:
        """Detailed representation"""
        return (f"EmailMessage(subject='{self.subject}', sender='{self.sender}', "
                f"recipients={len(self.recipients)}, attachments={len(self.attachments)})")


class EmailCollection:
    """Collection of email messages with search and filtering capabilities"""
    
    def __init__(self):
        self.messages: List[EmailMessage] = []
        self._index_by_sender: Dict[str, List[EmailMessage]] = {}
        self._index_by_subject: Dict[str, List[EmailMessage]] = {}
        self.source_files: List[str] = []
    
    def add_message(self, message: EmailMessage):
        """Add message to collection"""
        self.messages.append(message)
        self._update_indexes(message)
    
    def add_messages(self, messages: List[EmailMessage]):
        """Add multiple messages"""
        for message in messages:
            self.add_message(message)

    def add_source_file(self, file_path: str):
        """Add source file to collection"""
        if file_path not in self.source_files:
            self.source_files.append(file_path)

    def get_messages_by_source(self) -> Dict[str, List[EmailMessage]]:
        """Group messages by source file"""
        grouped = {}
        for message in self.messages:
            source = message.source_file or "Unknown Source"
            if source not in grouped:
                grouped[source] = []
            grouped[source].append(message)
        return grouped

    def get_source_file_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for each source file"""
        grouped = self.get_messages_by_source()
        stats = {}

        for source, messages in grouped.items():
            stats[source] = {
                'count': len(messages),
                'has_attachments': sum(1 for m in messages if m.has_attachments),
                'total_attachments': sum(m.attachment_count for m in messages),
                'date_range': self._get_date_range(messages),
                'file_type': self._get_file_type(source)
            }

        return stats

    def _get_date_range(self, messages: List[EmailMessage]) -> Dict[str, str]:
        """Get date range for messages"""
        dates = [m.date for m in messages if m.date and m.date != "Unknown date"]
        if not dates:
            return {'earliest': 'Unknown', 'latest': 'Unknown'}

        # Simple string comparison for now
        dates.sort()
        return {'earliest': dates[0], 'latest': dates[-1]}

    def _get_file_type(self, source: str) -> str:
        """Determine file type from source"""
        if source.lower().endswith('.pst'):
            return 'PST'
        elif source.lower().endswith('.mbox') or source.lower().endswith('.mbx'):
            return 'MBOX'
        elif source.lower().endswith('.eml'):
            return 'EML'
        elif source.lower().endswith('.msg'):
            return 'MSG'
        else:
            return 'Unknown'
    
    def _update_indexes(self, message: EmailMessage):
        """Update search indexes"""
        # Index by sender
        sender = message.sender.lower()
        if sender not in self._index_by_sender:
            self._index_by_sender[sender] = []
        self._index_by_sender[sender].append(message)
        
        # Index by subject
        subject = message.subject.lower()
        if subject not in self._index_by_subject:
            self._index_by_subject[subject] = []
        self._index_by_subject[subject].append(message)
    
    def get_by_sender(self, sender: str) -> List[EmailMessage]:
        """Get messages by sender"""
        return self._index_by_sender.get(sender.lower(), [])
    
    def get_by_subject(self, subject: str) -> List[EmailMessage]:
        """Get messages by subject"""
        return self._index_by_subject.get(subject.lower(), [])
    
    def search(self, query: str) -> List[EmailMessage]:
        """Search messages by query"""
        query = query.lower()
        results = []
        
        for message in self.messages:
            if (query in message.subject.lower() or
                query in message.sender.lower() or
                query in message.body_text.lower() or
                query in message.body_html.lower()):
                results.append(message)
        
        return results
    
    def filter_by_date_range(self, start_date: datetime, end_date: datetime) -> List[EmailMessage]:
        """Filter messages by date range"""
        return [msg for msg in self.messages 
                if msg.date and start_date <= msg.date <= end_date]
    
    def filter_with_attachments(self) -> List[EmailMessage]:
        """Get messages with attachments"""
        return [msg for msg in self.messages if msg.has_attachments]
    
    def get_senders(self) -> List[str]:
        """Get unique senders"""
        return list(self._index_by_sender.keys())
    
    def get_subjects(self) -> List[str]:
        """Get unique subjects"""
        return list(self._index_by_subject.keys())
    
    def __len__(self) -> int:
        return len(self.messages)
    
    def __iter__(self):
        return iter(self.messages)
    
    def __getitem__(self, index):
        return self.messages[index]
