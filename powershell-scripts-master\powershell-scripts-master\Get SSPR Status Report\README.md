## Export Microsoft 365 SSPR Status Reports using PowerShell

Export self-service password reset status reports using PowerShell to monitor user password resets and increase Microsoft 365 security.

***Sample Output:***

This script retrieves Microsoft 365 users' Self-service Password Reset (SSPR) status reports and exports the information to a CSV file, as shown in the screenshot below.

![Microsoft 365 SSPR Status Reports](https://o365reports.com/wp-content/uploads/2024/02/Final-Output-SS.png?v=1707806405)

## Microsoft 365 Reporting tool by AdminDroid

Simplify your M365 management with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)! Explore 1800+ pre-built reports and insightful dashboards tailored to your needs.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/20012/1/20>*

