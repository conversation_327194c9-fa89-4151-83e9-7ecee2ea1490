# PowerShell Syntaksfremhævning

## Oversigt
Din PowerShell OneLiners applikation har nu fået PowerShell syntaksfremhævning implementeret, som giver dine kommandoer et professionelt udseende med farver der matcher PowerShell ISE og Visual Studio Code.

## Funktioner

### Syntaksfremhævning
Applikationen fremhæver nu automatisk forskellige elementer i PowerShell-kode:

#### Farver og Elementer:
- **Cmdlets** (Get-, Set-, New-, osv.): <PERSON><PERSON><PERSON> (#569CD6) med fed skrift
- **Parametre** (-Name, -Path, osv.): <PERSON><PERSON> blå (#9CDCFE)
- **Strenge** ("tekst", 'tekst'): Orange (#CE9178)
- **Variabler** ($var): <PERSON><PERSON> (#4FC1FF)
- **Kommentarer** (# kommentar): <PERSON><PERSON><PERSON><PERSON> (#6A9955)
- **Operatorer** (=, +, -eq, -like, osv.): <PERSON><PERSON> (#D4D4D4)
- **Tal**: <PERSON><PERSON> grø<PERSON> (#B5CEA8)
- **Keywords** (if, else, foreach, osv.): <PERSON>la (#C586C0) med fed skrift
- **Standard tekst**: Hvid (#D4D4D4)

### Understøttede PowerShell Elementer

#### Cmdlets
Automatisk genkendelse af PowerShell cmdlets baseret på Verb-Noun mønsteret:
- Get-Process, Set-Location, New-Item
- Start-Service, Stop-Process, Remove-Item
- Connect-ExchangeOnline, Get-Mailbox
- Og mange flere...

#### Operatorer
- **Sammenligning**: -eq, -ne, -lt, -le, -gt, -ge
- **Matching**: -like, -notlike, -match, -notmatch
- **Indhold**: -contains, -notcontains, -in, -notin
- **Logiske**: -and, -or, -not, -xor
- **String**: -replace, -split, -join
- **Type**: -is, -isnot, -as
- **Matematiske**: +, -, *, /, %
- **Tildeling**: =, +=, -=, *=, /=

#### Keywords
- **Kontrol strukturer**: if, else, elseif, switch
- **Loops**: foreach, for, while, do
- **Fejlhåndtering**: try, catch, finally, throw
- **Funktioner**: function, filter, param
- **Avanceret**: class, enum, using, workflow

### Indstillinger

#### Aktivér/Deaktivér Syntaksfremhævning
1. Klik på **⚙️ Indstillinger** i værktøjslinjen
2. Find **"PowerShell syntaksfremhævning"** checkboxen
3. Markér for at aktivere, fjern markering for at deaktivere
4. Klik **OK** for at gemme

#### Andre relaterede indstillinger:
- **Fremhæv søgeresultater**: Fremhæver søgeord i gul
- **Gruppér kommandoer efter verb**: Organiserer kommandoer efter Get-, Set-, osv.

## Eksempler

### Basis Kommando
```powershell
Get-Process | Where-Object {$_.CPU -gt 100}
```
- `Get-Process` vises i blå (cmdlet)
- `Where-Object` vises i blå (cmdlet)
- `$_.CPU` vises i cyan (variabel)
- `-gt` vises i hvid (operator)
- `100` vises i lys grøn (tal)

### Exchange Online Kommando
```powershell
Get-Mailbox -Identity "<EMAIL>" -RecipientTypeDetails UserMailbox
```
- `Get-Mailbox` vises i blå (cmdlet)
- `-Identity`, `-RecipientTypeDetails` vises i lys blå (parametre)
- `"<EMAIL>"` vises i orange (streng)
- `UserMailbox` vises i hvid (standard tekst)

### Script med Variabler
```powershell
# Dette er en kommentar
$users = Get-ADUser -Filter *
if ($users.Count -gt 0) {
    Write-Host "Found users" -ForegroundColor Green
}
```
- `# Dette er en kommentar` vises i grøn (kommentar)
- `$users` vises i cyan (variabel)
- `Get-ADUser` vises i blå (cmdlet)
- `if` vises i lilla (keyword)
- `-gt` vises i hvid (operator)
- `"Found users"` vises i orange (streng)

## Tekniske Detaljer

### Implementation
- Syntaksfremhævningen er implementeret i `ScrolledTextEdit` klassen
- Bruger Qt's `QTextCharFormat` til at anvende farver og formatering
- Parsing sker linje for linje for optimal performance
- Understøtter både enkelt og dobbelt anførselstegn for strenge
- Håndterer escape-karakterer i strenge korrekt

### Performance
- Lazy loading af syntaksfremhævning
- Caching af formatering for bedre performance
- Minimal påvirkning af applikationens hastighed

### Kompatibilitet
- Virker med både mørkt og lyst tema
- Bevarer eksisterende søgefremhævning funktionalitet
- Kompatibel med alle eksisterende funktioner

## Test
For at teste syntaksfremhævningen kan du køre test-scriptet:
```bash
python py/APExamples/test/test_syntax_highlighting.py
```

Dette åbner et testvindue med forskellige PowerShell eksempler der demonstrerer syntaksfremhævningen.

## Fejlfinding

### Syntaksfremhævning virker ikke
1. Kontroller at indstillingen er aktiveret i **⚙️ Indstillinger**
2. Prøv at vælge en kommando igen fra listen
3. Genstart applikationen hvis nødvendigt

### Farver ser forkerte ud
- Syntaksfremhævningen tilpasser sig automatisk til dit valgte tema (mørkt/lyst)
- Mørkt tema: Bruger lyse, kontrastrige farver
- Lyst tema: Bruger mørkere farver for bedre læsbarhed

### Alle kommandoer vises med hvid tekst
Dette problem er nu løst! Applikationen bruger nu:
- Specifik CSS styling for kommando-tekstboksen
- Automatisk farvetilpasning baseret på tema
- Korrekt håndtering af rich text formatering

### Performance problemer
- Syntaksfremhævning kan være lidt langsommere for meget lange kommandoer
- Deaktivér funktionen hvis du oplever problemer

### Test syntaksfremhævning
Kør test-scriptet for at verificere at alt virker:
```bash
python py/APExamples/test/quick_syntax_test.py
```

## Fremtidige Forbedringer
- Understøttelse af flere PowerShell konstruktioner
- Tilpasselige farvetemaer
- Forbedret performance for store scripts
- IntelliSense-lignende funktionalitet
