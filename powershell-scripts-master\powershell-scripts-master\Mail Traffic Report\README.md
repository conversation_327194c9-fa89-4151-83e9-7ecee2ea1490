## Export Office 365 Mail Traffic Report with PowerShell - Office 365 Reports

This PowerShell script exports a number of emails sent and received by a per-user in your Office 365 org. Download mail traffic report for your tenant now!

***Sample Output:***

This script exports Microsoft 365 Email Statistics Report to an output CSV file that looks similar to the screenshot below.

![Office 365 Mail Traffic Report](https://o365reports.com/wp-content/uploads/2020/08/Office-365-mail-traffic-report-by-user-statistics.png?v=1705576553)

## Microsoft 365 Reporting tool by AdminDroid

Optimize your Microsoft 365 Email management with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), featuring over 1800 pre-built reports and insightful dashboards.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10403/1/20>*