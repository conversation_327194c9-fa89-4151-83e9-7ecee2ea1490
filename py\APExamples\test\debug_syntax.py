#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script til at teste syntaksfremhævning direkte
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# Import ScrolledTextEdit
import importlib.util
spec = importlib.util.spec_from_file_location("test_apexamples", os.path.join(os.path.dirname(__file__), "Test-APExamples.py"))
test_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(test_module)
ScrolledTextEdit = test_module.ScrolledTextEdit

class DebugWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Debug Syntaks Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Info label
        self.info_label = QLabel("Debug info:")
        layout.addWidget(self.info_label)
        
        # Text editor med samme setup som hovedapp
        self.text_edit = ScrolledTextEdit()
        self.text_edit.setObjectName("commandTextBox")  # Samme som hovedapp
        self.text_edit.setFont(QFont("Consolas", 12))
        self.text_edit.powershell_syntax_enabled = True
        self.text_edit.update_syntax_colors("dark")
        layout.addWidget(self.text_edit)
        
        # Test knapper
        test_btn = QPushButton("Test PowerShell Kommando")
        test_btn.clicked.connect(self.test_command)
        layout.addWidget(test_btn)
        
        toggle_btn = QPushButton("Toggle Syntaksfremhævning")
        toggle_btn.clicked.connect(self.toggle_syntax)
        layout.addWidget(toggle_btn)
        
        debug_btn = QPushButton("Debug Info")
        debug_btn.clicked.connect(self.show_debug_info)
        layout.addWidget(debug_btn)
        
        # Indlæs test
        self.test_command()
        
    def test_command(self):
        test_code = """Get-Mailbox -Identity "<EMAIL>" | Set-Mailbox -ProhibitSendQuota "2GB"
$users = Get-ADUser -Filter {Department -eq "IT"}
if ($users.Count -gt 0) {
    Write-Host "Found users" -ForegroundColor Green
}"""
        print(f"Setting text: {test_code[:50]}...")
        print(f"Syntax enabled: {self.text_edit.powershell_syntax_enabled}")
        self.text_edit.setText(test_code)
        print("Text set complete")
        
    def toggle_syntax(self):
        self.text_edit.powershell_syntax_enabled = not self.text_edit.powershell_syntax_enabled
        print(f"Syntax toggled to: {self.text_edit.powershell_syntax_enabled}")
        current_text = self.text_edit.toPlainText()
        if current_text:
            self.text_edit.setText(current_text)
            
    def show_debug_info(self):
        info = f"""
Syntax enabled: {self.text_edit.powershell_syntax_enabled}
Object name: {self.text_edit.objectName()}
Accept rich text: {self.text_edit.acceptRichText()}
Plain text length: {len(self.text_edit.toPlainText())}
HTML length: {len(self.text_edit.toHtml())}
Font: {self.text_edit.font().family()}
"""
        self.info_label.setText(info)
        print(info)
        print("HTML content preview:")
        print(self.text_edit.toHtml()[:500] + "...")

def main():
    app = QApplication(sys.argv)
    
    # Samme styling som hovedapp (mørkt tema)
    app.setStyleSheet("""
        QMainWindow, QWidget {
            background-color: #2d2d30;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        QPushButton {
            background-color: #0e639c;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
            min-height: 30px;
        }
        QPushButton:hover {
            background-color: #1177bb;
        }
        /* Specifik styling for kommando text box - ingen farve override når syntaksfremhævning er aktiv */
        QTextEdit#commandTextBox {
            background-color: #1E1E1E;
            border: 1px solid #3F3F46;
            border-radius: 8px;
            padding: 6px 8px;
            selection-background-color: #0078D7;
            font-size: 12pt;
            /* Ingen color property - tillader syntaksfremhævning */
        }
        /* Andre QTextEdit elementer */
        QTextEdit {
            background-color: #1E1E1E;
            color: #ffffff;
            border: 1px solid #3F3F46;
            border-radius: 8px;
            padding: 6px 8px;
            selection-background-color: #0078D7;
            font-size: 12pt;
        }
        QLabel {
            color: #ffffff;
            padding: 8px;
        }
    """)
    
    window = DebugWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
