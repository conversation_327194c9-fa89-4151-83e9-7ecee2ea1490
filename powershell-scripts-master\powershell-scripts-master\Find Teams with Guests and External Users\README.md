﻿## Find Teams with Guests and External Users in Microsoft Teams  
Execute the PowerShell script to list the teams with guests and external access to secure external access in Microsoft Teams.

***Sample Output:***

The script generates two output CSV files: one containing details of Teams and their guest users, and the other summarizing the guest user count for each Team. The sample outputs will resemble the screenshot below.

**Detailed report: Teams and their guest user details**  

![Get Teams with Guests in Microsoft 365](https://o365reports.com/wp-content/uploads/2025/01/Get-All-Guests-in-Teams.png)

**Summary report: Teams and their guest users count**

![Teams and their guest users count](https://o365reports.com/wp-content/uploads/2025/01/Teams-and-their-guest-users-count.png)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive Microsoft Teams based reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/60073/1/20](https://demo.admindroid.com/#/1/11/reports/60073/1/20)*










