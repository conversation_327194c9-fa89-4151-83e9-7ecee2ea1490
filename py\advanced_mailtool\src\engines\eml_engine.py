"""
EML format engine for individual email files
"""

import email
from email import policy
from pathlib import Path
from typing import Optional
import chardet

from .base_engine import Mail<PERSON>ngineB<PERSON>, ProgressCallback, MailEngineError
from ..core.email_message import EmailMessage, EmailCollection, Attachment


class EmlEngine(MailEngineBase):
    """Engine for EML format files (individual emails)"""
    
    def __init__(self):
        super().__init__()
        self.engine_name = "EML Engine"
        self.supported_extensions = ['.eml', '.email']
    
    def can_handle(self, file_path: Path) -> bool:
        """Check if this is an EML file"""
        if file_path.suffix.lower() in self.supported_extensions:
            return True
        
        # Check if file looks like an email
        try:
            with open(file_path, 'rb') as f:
                first_lines = f.read(1024).decode('utf-8', errors='ignore')
                # Look for email headers
                return any(header in first_lines.lower() for header in 
                          ['from:', 'to:', 'subject:', 'date:', 'message-id:'])
        except Exception:
            return False
    
    def load_messages(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Load single EML message"""
        
        # Validate file
        is_valid, error_msg = self.validate_file(file_path)
        if not is_valid:
            raise MailEngineError(error_msg)
        
        collection = EmailCollection()
        
        if progress:
            progress.update("Loading EML file...", 0, 1)
        
        try:
            # Detect encoding
            encoding = self._detect_encoding(file_path)
            
            # Read and parse email
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            # Parse with email library
            msg = email.message_from_bytes(raw_data, policy=policy.default)
            
            # Convert to EmailMessage
            email_message = self._parse_message(msg, file_path)
            if email_message:
                collection.add_message(email_message)
            
            if progress:
                progress.update("EML file loaded", 1, 1)
            
            self.log_info(f"Successfully loaded EML file: {file_path}")
            return collection
            
        except Exception as e:
            error_msg = f"Failed to load EML file {file_path}: {e}"
            self.log_error(error_msg)
            raise MailEngineError(error_msg)
    
    def save_messages(self, messages: EmailCollection, file_path: Path, 
                     progress: Optional[ProgressCallback] = None) -> bool:
        """Save messages as EML files"""
        
        if len(messages) == 0:
            raise MailEngineError("No messages to save")
        
        try:
            if len(messages) == 1:
                # Save single EML file
                return self._save_single_eml(messages[0], file_path, progress)
            else:
                # Save multiple EML files in directory
                return self._save_multiple_eml(messages, file_path, progress)
                
        except Exception as e:
            error_msg = f"Failed to save EML file(s): {e}"
            self.log_error(error_msg)
            raise MailEngineError(error_msg)
    
    def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding"""
        try:
            with open(file_path, 'rb') as f:
                sample = f.read(10240)
                result = chardet.detect(sample)
                return result.get('encoding', 'utf-8') or 'utf-8'
        except Exception:
            return 'utf-8'
    
    def _parse_message(self, msg, source_file: Path) -> Optional[EmailMessage]:
        """Parse email message to EmailMessage object"""
        try:
            # Extract basic fields
            subject = str(msg.get('Subject', '(No Subject)'))
            sender = str(msg.get('From', ''))
            sender_name = self._extract_name_from_address(sender)

            # Recipients
            recipients = self._parse_addresses(str(msg.get('To', '')))
            cc = self._parse_addresses(str(msg.get('Cc', '')))
            bcc = self._parse_addresses(str(msg.get('Bcc', '')))

            # Date
            date_str = str(msg.get('Date', ''))

            # Message ID
            message_id = str(msg.get('Message-ID', ''))
            
            # Extract content and attachments
            body_text, body_html, attachments = self._extract_content(msg)
            
            # Create EmailMessage
            email_message = EmailMessage(
                subject=subject,
                sender=sender,
                sender_name=sender_name,
                recipients=recipients,
                cc=cc,
                bcc=bcc,
                body_text=body_text,
                body_html=body_html,
                message_id=message_id,
                date=date_str,
                source_file=str(source_file),
                source_format='eml',
                attachments=attachments
            )
            
            # Add all headers
            for key, value in msg.items():
                email_message.set_header(key, str(value))
            
            return email_message
            
        except Exception as e:
            self.log_warning(f"Error parsing EML message: {e}")
            return None
    
    def _extract_name_from_address(self, address: str) -> str:
        """Extract display name from email address"""
        try:
            parsed = email.utils.parseaddr(address)
            return parsed[0] if parsed[0] else ""
        except Exception:
            return ""
    
    def _parse_addresses(self, address_str: str) -> list:
        """Parse comma-separated email addresses"""
        if not address_str:
            return []
        
        try:
            addresses = email.utils.getaddresses([address_str])
            return [addr[1] for addr in addresses if addr[1]]
        except Exception:
            return [address_str.strip()] if address_str.strip() else []
    
    def _extract_content(self, message) -> tuple[str, str, list]:
        """Extract text, HTML content and attachments"""
        body_text = ""
        body_html = ""
        attachments = []
        
        try:
            if message.is_multipart():
                for part in message.walk():
                    content_type = part.get_content_type()
                    disposition = part.get_content_disposition()
                    
                    if disposition == 'attachment':
                        attachment = self._create_attachment(part)
                        if attachment:
                            attachments.append(attachment)
                    
                    elif content_type == 'text/plain' and not disposition:
                        try:
                            content = part.get_content()
                            if isinstance(content, str):
                                body_text += content
                        except Exception:
                            payload = part.get_payload(decode=True)
                            if payload:
                                body_text += payload.decode('utf-8', errors='ignore')
                    
                    elif content_type == 'text/html' and not disposition:
                        try:
                            content = part.get_content()
                            if isinstance(content, str):
                                body_html += content
                        except Exception:
                            payload = part.get_payload(decode=True)
                            if payload:
                                body_html += payload.decode('utf-8', errors='ignore')
            
            else:
                # Single part message
                content_type = message.get_content_type()
                try:
                    content = message.get_content()
                    if content_type == 'text/html':
                        body_html = content
                    else:
                        body_text = content
                except Exception:
                    payload = message.get_payload(decode=True)
                    if payload:
                        content = payload.decode('utf-8', errors='ignore')
                        if content_type == 'text/html':
                            body_html = content
                        else:
                            body_text = content
        
        except Exception as e:
            self.log_warning(f"Error extracting content: {e}")
        
        return body_text, body_html, attachments
    
    def _create_attachment(self, part) -> Optional[Attachment]:
        """Create attachment from email part"""
        try:
            filename = part.get_filename()
            if not filename:
                return None
            
            content_type = part.get_content_type()
            content_id = part.get('Content-ID', '').strip('<>')
            
            # Get attachment data
            data = None
            size = 0
            try:
                payload = part.get_payload(decode=True)
                if payload:
                    data = payload
                    size = len(payload)
            except Exception:
                pass
            
            return Attachment(
                filename=filename,
                content_type=content_type,
                size=size,
                data=data,
                content_id=content_id,
                is_inline=part.get_content_disposition() == 'inline'
            )
            
        except Exception:
            return None
    
    def _save_single_eml(self, message: EmailMessage, file_path: Path, 
                        progress: Optional[ProgressCallback] = None) -> bool:
        """Save single message as EML file"""
        
        if progress:
            progress.update("Creating EML file...", 0, 1)
        
        # Create email message
        email_msg = self._create_email_message(message)
        
        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(str(email_msg))
        
        if progress:
            progress.update("EML file saved", 1, 1)
        
        self.log_info(f"Saved EML file: {file_path}")
        return True
    
    def _save_multiple_eml(self, messages: EmailCollection, base_path: Path, 
                          progress: Optional[ProgressCallback] = None) -> bool:
        """Save multiple messages as EML files in directory"""
        
        # Create directory if it doesn't exist
        if base_path.suffix:
            # If base_path has extension, use it as directory name
            directory = base_path.with_suffix('')
        else:
            directory = base_path
        
        directory.mkdir(parents=True, exist_ok=True)
        
        total = len(messages)
        for i, message in enumerate(messages):
            if progress:
                progress.update(f"Saving EML {i+1}/{total}...", i, total)
            
            # Create safe filename
            safe_subject = self._make_safe_filename(message.subject)
            filename = f"{i+1:04d}_{safe_subject}.eml"
            file_path = directory / filename
            
            # Save message
            email_msg = self._create_email_message(message)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(str(email_msg))
        
        if progress:
            progress.update(f"Saved {total} EML files", total, total)
        
        self.log_info(f"Saved {total} EML files to {directory}")
        return True
    
    def _make_safe_filename(self, text: str, max_length: int = 50) -> str:
        """Create safe filename from text"""
        import re
        
        # Remove/replace unsafe characters
        safe = re.sub(r'[<>:"/\\|?*]', '_', text)
        safe = safe.strip()
        
        # Limit length
        if len(safe) > max_length:
            safe = safe[:max_length]
        
        return safe if safe else "untitled"
    
    def _create_email_message(self, message: EmailMessage):
        """Convert EmailMessage to email.message.Message"""
        from email.mime.multipart import MIMEMultipart
        from email.mime.text import MIMEText
        from email.mime.base import MIMEBase
        from email import encoders
        
        # Create message
        if message.has_attachments or message.body_html:
            msg = MIMEMultipart()
        else:
            msg = MIMEText(message.body_text, 'plain', 'utf-8')
            msg['Subject'] = message.subject
            msg['From'] = message.sender
            msg['To'] = ', '.join(message.recipients)
            if message.cc:
                msg['Cc'] = ', '.join(message.cc)
            if message.message_id:
                msg['Message-ID'] = message.message_id
            return msg
        
        # Set headers
        msg['Subject'] = message.subject
        msg['From'] = message.sender
        msg['To'] = ', '.join(message.recipients)
        if message.cc:
            msg['Cc'] = ', '.join(message.cc)
        if message.message_id:
            msg['Message-ID'] = message.message_id
        
        # Add body parts
        if message.body_text:
            msg.attach(MIMEText(message.body_text, 'plain', 'utf-8'))
        if message.body_html:
            msg.attach(MIMEText(message.body_html, 'html', 'utf-8'))
        
        # Add attachments
        for attachment in message.attachments:
            if attachment.data:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.data)
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename="{attachment.filename}"'
                )
                msg.attach(part)
        
        return msg
