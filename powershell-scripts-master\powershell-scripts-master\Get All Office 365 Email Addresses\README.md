## Get All Office 365 Email Address and Alias Using PowerShell

This script helps to get all Office 365 email addresses, including SMTP address and alias. Also, you can export reports based on the mailbox type. 

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Export Microsoft 365 Email Addresses Report](https://m365scripts.com/wp-content/uploads/2022/02/Get-all-Office-365-email-addresses.png?v=1701520120)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive Microsoft 365 reports through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10010/1/20>*  



