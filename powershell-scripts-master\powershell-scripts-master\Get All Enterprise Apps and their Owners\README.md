﻿## Export All Enterprise Apps and Their Owners in Microsoft Entra 
Run the PowerShell script to quickly retrieve a list of all enterprise apps and their owners- no more manual searches!

***Sample Output:***

The script generates an output CSV file with details of enterprise apps and their owners. The sample output will resemble the screenshot below.

**DLP Detected Exchange Emails:** 

![Export All Enterprise Apps and Their Owners in Microsoft Entra](https://o365reports.com/wp-content/uploads/2024/11/Export-All-Enterprise-Apps-and-Their-Owners-in-Microsoft-Entra.png?v=1732531469)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive application based reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/20042/1/20](https://demo.admindroid.com/#/1/11/reports/20042/1/20)*






