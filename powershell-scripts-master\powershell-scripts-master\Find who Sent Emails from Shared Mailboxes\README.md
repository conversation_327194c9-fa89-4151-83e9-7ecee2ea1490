## Find Who Sent Email from Shared Mailbox in Office 365 using PowerShell

This PowerShell script helps admins to find out who sent what email from the shared mailbox in your Office 365 environment.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Find who Sent Emails from Shared Mailboxes Report](https://o365reports.com/wp-content/uploads/2022/05/Find-who-sent-email-from-shared-mailbox-Powershell.png?v=1705576538)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for detailed reporting? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, it seamlessly complements your PowerShell scripts.

*View more comprehensive reports through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10959/1/20?easyFilter=%7B%22Date%22%3A6%7D>*  



