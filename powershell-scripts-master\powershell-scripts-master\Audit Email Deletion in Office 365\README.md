## Audit Email Deletion in Office 365: Find Out Who Deleted an Email from a Mailbox
This PowerShell script helps to find who deleted email from the mailboxes and exports email deletion audit records to CSV file.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Audit Email Deletion Report](https://o365reports.com/wp-content/uploads/2021/09/Find-who-deleted-email-from-mailbox-1.png?v=1705576537)

## Microsoft 365 Reporting Tool by AdminDroid

For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*Audit Email Deletion in a More Effective Way with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/21012/1/20>*


