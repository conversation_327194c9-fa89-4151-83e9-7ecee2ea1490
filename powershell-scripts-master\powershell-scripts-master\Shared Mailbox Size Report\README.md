## Get Shared Mailbox Size Report Using PowerShell
Discover how to efficiently get shared mailbox size with our PowerShell script in Microsoft 365. Export detailed report and optimize storage.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Shared Mailbox Size Report](https://o365reports.com/wp-content/uploads/2024/02/Get-shared-mailbox-size-report-PowerShell-Script-1024x245.png?v=1707203216)
## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts.

*View more comprehensive mailbox size report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10013/1/20>* 

