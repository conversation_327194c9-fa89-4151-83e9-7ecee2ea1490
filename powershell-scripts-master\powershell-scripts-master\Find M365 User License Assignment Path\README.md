## Find & Export Microsoft 365 User License Assignment Paths Using PowerShell

Check Microsoft 365 user license assignment path -whether directly or inherited from a group using PowerShell.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![M365 User License Assignment Path Report](https://o365reports.com/wp-content/uploads/2024/05/script-result-1080x168.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive M365 user license assignment path report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/8/1/20>*



