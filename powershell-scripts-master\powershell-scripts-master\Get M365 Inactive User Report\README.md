## Export Microsoft 365 Inactive User Report using MS Graph PowerShell

This PowerShell script helps admins to identify users last sign-in activity & generates 10+ Microsoft 365 inactive user report using MS Graph PowerShell.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Microsoft 365 Inactive User Report](https://o365reports.com/wp-content/uploads/2023/06/Microsoft-365-inactive-user-report-using-MS-graph-PowerShell-1.png?v=1705575958)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for detailed reporting? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, it seamlessly complements your PowerShell scripts.

*View more comprehensive Microsoft 365 Inactive user report through AdminDroid: <https://demo.admindroid.com/#/1/11/reportboard/2/1/1/2/reports/80002/1/20?source=%7B%22boardId%22%3A2%7D>*  



