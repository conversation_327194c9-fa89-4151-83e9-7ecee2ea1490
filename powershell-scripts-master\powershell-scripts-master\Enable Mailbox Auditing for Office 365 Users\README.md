﻿## Enable Mailbox Auditing in Office 365 Users using PowerShell
Quickly enable mailbox auditing in Office 365 using this PowerShell script or you can enable mailbox auditing for all Office 365 users by default.

***Sample Output:***

The output of the PowerShell script looks similar to the below screenshot.

![Enable Mailbox Auditing]( https://o365reports.com/wp-content/uploads/2020/01/Enable-mailbox-auditing-for-all-office-365-users.png)

## Microsoft 365 Reporting tool by AdminDroid
Looking for advanced M365 reports? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) gives you access to over 1800 pre-built reports and insightful dashboards, making your job easier and data analysis more effective.

*Get a complete Mailbox Audit report view through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10617/1/20>*

