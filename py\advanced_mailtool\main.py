#!/usr/bin/env python3
"""
Advanced MailTool - Modern email viewer and processor
"""

import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QSettings
from PySide6.QtGui import QIcon

from src.gui.main_window import MainWindow
from src.gui.themes import theme_manager


def setup_logging():
    """Setup application logging"""
    
    # Create logs directory
    log_dir = Path(__file__).parent / "logs"
    log_dir.mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "advanced_mailtool.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set specific loggers
    logging.getLogger("PySide6").setLevel(logging.WARNING)
    logging.getLogger("extract_msg").setLevel(logging.WARNING)


def check_dependencies():
    """Check if required dependencies are available"""
    
    missing_deps = []
    
    try:
        import PySide6
    except ImportError:
        missing_deps.append("PySide6")
    
    try:
        import chardet
    except ImportError:
        missing_deps.append("chardet")
    
    try:
        import email_validator
    except ImportError:
        missing_deps.append("email-validator")
    
    if missing_deps:
        print("Missing required dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nInstall with: pip install -r requirements.txt")
        return False
    
    return True


def main():
    """Main application entry point"""
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger("AdvancedMailTool")
    
    logger.info("Starting Advanced MailTool")
    
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Advanced MailTool")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("AdvancedMailTool")
    
    # Set application properties (removed deprecated attributes for Qt 6.x)
    # High DPI support is enabled by default in Qt 6
    
    try:
        # Create and show main window
        window = MainWindow()
        window.show()
        
        logger.info("Advanced MailTool started successfully")
        
        # Run application
        exit_code = app.exec()
        
        logger.info(f"Advanced MailTool exiting with code {exit_code}")
        return exit_code
        
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        
        # Show error dialog
        try:
            QMessageBox.critical(
                None,
                "Fatal Error",
                f"A fatal error occurred:\n\n{str(e)}\n\nCheck the log file for details."
            )
        except Exception:
            print(f"Fatal error: {e}")
        
        return 1


if __name__ == "__main__":
    sys.exit(main())