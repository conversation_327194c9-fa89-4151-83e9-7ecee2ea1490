## Export Office 365 Mailbox Size Report Using PowerShell
This PowerShell script exports mailbox size report and quotas to a CSV file. This report helps to analyse mailbox size usage and mailbox warning size.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Mailbox Size Report](https://o365reports.com/wp-content/uploads/2020/10/mailbox-size-report-office-365.png?v=1705576765)
## Microsoft 365 Reporting Tool by AdminDroid 
For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*Access detailed mailbox size reports effortlessly with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10005/1/20>*
