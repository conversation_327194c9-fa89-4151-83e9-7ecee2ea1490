## Export Office 365 Groups a User is Member Of Using PowerShell

This PowerShell exports Office 365 groups a user is member of. With this script, you can generate more than 10 user membership report with advanced filters. 

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Groups a User is Member Of Report](https://o365reports.com/wp-content/uploads/2021/04/List-groups-a-user-is-member-of-1.png?v=1705576735) 

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive groups a user is member of report through AdminDroid:  <https://demo.admindroid.com/#/1/11/reports/1017/1/20?easyFilter=%7B%22memberType%22%3A1%7D>*  

