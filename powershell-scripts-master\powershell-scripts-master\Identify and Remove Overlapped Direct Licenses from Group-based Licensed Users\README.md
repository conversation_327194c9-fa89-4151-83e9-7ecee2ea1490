﻿## Remove Direct Licenses for Group-Licensed Users in Microsoft 365 
Find and remove direct licenses for users with group-inherited licenses in Microsoft 365 using PowerShell.

***Sample Output:***

The script generates an output CSV file and a log file, which will look similar to the screenshots provided below.

![Remove Direct Licensing for Microsoft 365 Users with Group Licenses](https://o365reports.com/wp-content/uploads/2024/08/Remove-Direct-Licensing-for-Microsoft-365-Users-with-Group-Licenses-768x181.png?v=**********)

![Remove Direct Licenses for Users with Group Licenses](https://o365reports.com/wp-content/uploads/2024/08/Remove-Direct-Licenses-for-Users-with-Group-Licenses-768x115.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive Microsoft 365 License reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/8/1/20](https://demo.admindroid.com/#/1/11/reports/8/1/20)*




