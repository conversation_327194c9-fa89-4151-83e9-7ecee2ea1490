<#
    ##################################
    # Script til at oprette ny Xmail #
    # Created by jdh_admin           #
    ##################################
#>
function New-Xmail {
    param(
        [Parameter(Mandatory=$true)]
        [string]$SharedMailboxName,

        [Parameter(Mandatory=$true)]
        [string]$UserToAdd
    )

    # Test if connected to EXO and connect if not
    if (!(Get-ConnectionInformation).TokenStatus -eq 'Active') {
        Connect-ExchangeOnline -UserPrincipalName "<EMAIL>"
    }

    # Create Shared Mailbox
    New-Mailbox -Shared -Name $SharedMailboxName -Alias $SharedMailboxName -DisplayName $SharedMailboxName -PrimarySmtpAddress "$<EMAIL>"

    do {
        Start-Sleep -Seconds 1
    } until (
        <# Condition that stops the loop if it returns true #>
        Get-Mailbox $SharedMailboxName -ErrorAction SilentlyContinue
    )

    # Set Message Copy Options
    Set-Mailbox -Identity $SharedMailboxName -MessageCopyForSentAsEnabled $true -MessageCopyForSendOnBehalfEnabled $true

    # Set Mailbox Regional Configuration
    Set-MailboxRegionalConfiguration -Identity $SharedMailboxName -Language da-DK -DateFormat "dd-MM-yyyy" -LocalizeDefaultFolderName

    # Add User with Full Access, Send As, and Send on Behalf Permissions
    Add-MailboxPermission -Identity $SharedMailboxName -User $UserToAdd -AccessRights FullAccess -Automapping:$false
    Add-RecipientPermission -Identity $SharedMailboxName -Trustee $UserToAdd -AccessRights SendAs -confirm:$false

    # Add to "X-Alle Partnere" Dist. Group
    Add-DistributionGroupMember -Identity "X-Alle Partnere" -Member "$<EMAIL>"
 
    # Disable legacy
    Set-CASMailbox $SharedMailboxName -ImapEnabled $false -PopEnabled $false -SmtpClientAuthenticationDisabled $true

    # Get info for verifications
    Get-Mailbox $SharedMailboxName | Select-Object Name, MessageCopyForSentAsEnabled, MessageCopyForSendOnBehalfEnabled
    Get-MailboxPermission -Identity $SharedMailboxName -User $UserToAdd
    Get-RecipientPermission -Identity $SharedMailboxName -Trustee $UserToAdd
    Get-CASMailbox $SharedMailboxName | Select-Object DisplayName, ImapEnabled, PopEnabled, SmtpClientAuthenticationDisabled
}

New-Xmail

# Example Usage:
# New-Xmail -SharedMailboxName "XJDH" -UserToAdd "jdh"