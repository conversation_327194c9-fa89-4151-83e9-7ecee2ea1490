## Identify and Block Sign-in for Shared Mailboxes in Microsoft 365

Learn how to identify and block shared mailbox sign-in using a PowerShell script to prevent unauthorized access and secure your organization exposing sensitive data.

***Sample Output:***

The exported resource and shared mailbox sign-in status report looks like the screenshot below.

![Shared Mailbox Sign-in Status Report](<https://o365reports.com/wp-content/uploads/2025/03/SharedandResource-Mailboxes-SigninStatus-report.png?v=1742896624>)

Once the reports are generated, you will be asked for confirmation to block sign-in for resource and shared mailboxes in the report. After your confirmation, the script will block sign-in and provide the respective log file.

![Sign-in Blocked Mailboxes Log File](<https://o365reports.com/wp-content/uploads/2025/03/shared-mailbox-sign-in-blocked-log-file.png?v=1742896628>)

## Microsoft 365 Reporting tool by AdminDroid

Transform your Microsoft 365 data into actionable insights with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), featuring over 1900+ pre-built reports and 30+ dashboards.

*Unlock comprehensive report on Microsoft 365 shared mailbox sign-in activities with AdminDroid: [https://demo.admindroid.com/#/1/11/reports/20434/1/20](https://demo.admindroid.com/#/1/11/reports/20434/1/20)*