﻿## Automate Microsoft 365 User Offboarding with PowerShell
Try this quick, secure, and comprehensive PowerShell script to automate Microsoft 365 user offboarding.

***Sample Output:*** 

This script automates the user offboarding process and exports an output CSV file based on the action performed and its successful completion. The output of the PowerShell script looks similar to the screenshot below.

![Microsoft 365 Offboarding Status Report]( https://blog.admindroid.com/wp-content/uploads/2023/10/Wordpress\_1.png)

## Microsoft 365 Auditing tool by AdminDroid
Stop wasting time on manual M365 reports! [AdminDroid Microsoft 365 auditing tool](https://admindroid.com/?src=GitHub) automates everything, saving you valuable hours.

*Get more detailed insights through AdminDroid: [https://demo.admindroid.com](https://demo.admindroid.com)*




