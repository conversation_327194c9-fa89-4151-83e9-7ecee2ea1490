## Audit Mailbox Permission Changes in Office 365 using PowerShell
This PowerShell script helps to audit mailbox permission changes, such as adding and removing full access, send as & send on behalf permissions in Office 365.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Mailbox Permission Changes Report](https://o365reports.com/wp-content/uploads/2022/07/Detect-who-granted-full-access-permission-to-a-mailbox.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid

Take your Office 365 license management to the next level with the AdminDroid [Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1800+ pre-built reports and dashboards for in-depth analysis and efficient oversight.

*Audit Mailbox Permission Changes Efficiently with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10641/1/20?easyFilter=%7B%22IsInbuiltAccount%22%3A0%7D>*

