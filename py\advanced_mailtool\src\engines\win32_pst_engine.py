"""
Win32COM PST Engine for reading and writing PST files directly
Uses Microsoft Outlook COM interface for full PST support
"""

import os
import sys
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

try:
    import win32com.client
    import pythoncom
    WIN32COM_AVAILABLE = True
except ImportError:
    WIN32COM_AVAILABLE = False

from .base_engine import MailEngineBase, ProgressCallback, MailEngineError
from ..core.email_message import EmailMessage, EmailCollection, Attachment


class Win32PstEngine(MailEngineBase):
    """PST engine using Win32COM and Outlook"""

    def __init__(self):
        self.outlook_app = None
        self.namespace = None
        super().__init__()

        # Override base class attributes
        self.engine_name = "Win32 PST Engine"
        self.supported_extensions = ['.pst', '.ost']

    @property
    def name(self) -> str:
        return self.engine_name
    
    def can_handle(self, file_path: Path) -> bool:
        """Check if this engine can handle the file"""
        return (file_path.suffix.lower() in self.supported_extensions and
                self.is_available())

    def is_available(self) -> bool:
        """Check if Win32COM and Outlook are available"""
        if not WIN32COM_AVAILABLE:
            return False

        try:
            # Try to create Outlook application
            outlook = win32com.client.Dispatch("Outlook.Application")
            outlook = None  # Release
            return True
        except Exception:
            return False
    
    def _initialize_outlook(self):
        """Initialize Outlook COM interface"""
        if self.outlook_app is None:
            try:
                # Initialize COM
                pythoncom.CoInitialize()
                
                # Create Outlook application
                self.outlook_app = win32com.client.Dispatch("Outlook.Application")
                self.namespace = self.outlook_app.GetNamespace("MAPI")
                
                self.log_info("Outlook COM interface initialized")
                
            except Exception as e:
                self.log_error(f"Failed to initialize Outlook: {e}")
                raise MailEngineError(f"Cannot initialize Outlook: {e}")
    
    def _cleanup_outlook(self):
        """Cleanup Outlook COM interface"""
        try:
            if self.namespace:
                self.namespace = None
            if self.outlook_app:
                self.outlook_app = None
            pythoncom.CoUninitialize()
        except Exception as e:
            self.log_warning(f"Cleanup warning: {e}")
    
    def load_messages(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Load messages from PST file using Outlook COM"""

        print(f"DEBUG: Win32PstEngine.load_messages called with {file_path}")

        if not self.is_available():
            print("DEBUG: Win32COM or Outlook not available")
            raise MailEngineError("Win32COM or Outlook not available")

        print("DEBUG: Win32COM available, creating collection")
        collection = EmailCollection()

        try:
            print("DEBUG: Initializing Outlook")
            self._initialize_outlook()

            if progress:
                progress.update("Opening PST file...", 0, 0)

            print(f"DEBUG: Adding PST file to Outlook: {file_path}")
            # Add PST file to Outlook
            pst_store = self._add_pst_file(file_path)
            
            if progress:
                progress.update("Reading PST structure...", 0, 0)
            
            # Get root folder
            root_folder = pst_store.GetRootFolder()
            
            # Count total messages for progress
            total_messages = self._count_messages_recursive(root_folder)
            
            if progress:
                progress.update(f"Found {total_messages} messages", 0, total_messages)
            
            # Process all folders recursively
            processed = 0
            self._process_folder_recursive(root_folder, collection, progress, processed, total_messages)
            
            if progress:
                progress.update(f"Loaded {len(collection)} messages", len(collection), len(collection))
            
            # Remove PST file from Outlook
            self._remove_pst_file(pst_store)
            
            collection.add_source_file(str(file_path))
            
            return collection
            
        except Exception as e:
            self.log_error(f"Failed to load PST file {file_path}: {e}")
            raise MailEngineError(f"Failed to load PST file: {e}")
        
        finally:
            self._cleanup_outlook()
    
    def _add_pst_file(self, file_path: Path):
        """Add PST file to Outlook namespace"""
        try:
            # Convert to absolute path
            abs_path = str(file_path.resolve())
            
            # Add PST file
            self.namespace.AddStore(abs_path)
            
            # Find the added store
            for store in self.namespace.Stores:
                if store.FilePath.lower() == abs_path.lower():
                    self.log_info(f"PST file added: {store.DisplayName}")
                    return store
            
            raise MailEngineError("PST file was added but not found in stores")
            
        except Exception as e:
            raise MailEngineError(f"Failed to add PST file: {e}")
    
    def _remove_pst_file(self, store):
        """Remove PST file from Outlook namespace"""
        try:
            self.namespace.RemoveStore(store.GetRootFolder())
            self.log_info("PST file removed from Outlook")
        except Exception as e:
            self.log_warning(f"Failed to remove PST file: {e}")
    
    def _count_messages_recursive(self, folder) -> int:
        """Count total messages in folder and subfolders"""
        count = 0
        
        try:
            # Count messages in current folder
            count += folder.Items.Count
            
            # Count messages in subfolders
            for subfolder in folder.Folders:
                count += self._count_messages_recursive(subfolder)
                
        except Exception as e:
            self.log_warning(f"Error counting messages in folder: {e}")
        
        return count
    
    def _process_folder_recursive(self, folder, collection: EmailCollection, 
                                progress: Optional[ProgressCallback], 
                                processed: int, total: int) -> int:
        """Process folder and all subfolders recursively"""
        
        try:
            folder_name = getattr(folder, 'Name', 'Unknown')
            self.log_info(f"Processing folder: {folder_name}")
            
            # Process messages in current folder - high performance batch processing
            items = folder.Items
            batch_size = 100  # Larger batches for better performance
            item_count = items.Count

            # Process in batches to reduce COM overhead
            for batch_start in range(1, item_count + 1, batch_size):
                batch_end = min(batch_start + batch_size - 1, item_count)

                for i in range(batch_start, batch_end + 1):
                    try:
                        item = items(i)

                        # Quick class check first
                        if not hasattr(item, 'Class') or item.Class != 43:  # olMail
                            processed += 1
                            continue

                        # Convert item
                        message = self._convert_outlook_item(item, folder_name)
                        if message:
                            collection.add_message(message)

                        processed += 1

                    except Exception as e:
                        self.log_warning(f"Error processing message {i}: {e}")
                        processed += 1
                        continue

                # Update progress after each batch
                if progress:
                    progress.update(f"Processing {folder_name}... ({processed}/{total})",
                                  processed, total)
            
            # Process subfolders
            for subfolder in folder.Folders:
                processed = self._process_folder_recursive(subfolder, collection, 
                                                         progress, processed, total)
            
        except Exception as e:
            self.log_error(f"Error processing folder: {e}")
        
        return processed
    
    def _convert_outlook_item(self, item, folder_name: str) -> Optional[EmailMessage]:
        """Convert Outlook mail item to EmailMessage"""
        
        try:
            # Basic properties
            subject = getattr(item, 'Subject', '') or ''
            sender_name = getattr(item, 'SenderName', '') or ''
            sender_email = getattr(item, 'SenderEmailAddress', '') or ''

            # Clean up Exchange addresses
            if sender_email and sender_email.startswith('/o='):
                # Try to get SMTP address instead
                try:
                    sender_email = getattr(item, 'SenderEmailAddress', '')
                    # If still Exchange format, try to extract from sender
                    if sender_email.startswith('/o='):
                        sender_email = ""  # Clear Exchange format
                except:
                    sender_email = ""

            # Clean up sender name if it's an Exchange DN
            if sender_name and sender_name.startswith('/o='):
                sender_name = ""

            # Format sender display
            if sender_name and sender_email:
                sender_display = f"{sender_name} <{sender_email}>"
            elif sender_name:
                sender_display = sender_name
            elif sender_email:
                sender_display = sender_email
            else:
                sender_display = "Unknown Sender"
            
            # Recipients - clean up Exchange addresses
            recipients = []
            try:
                for recipient in item.Recipients:
                    addr = recipient.Address
                    # Skip Exchange DN addresses
                    if addr and not addr.startswith('/o='):
                        recipients.append(addr)
                    elif hasattr(recipient, 'Name') and recipient.Name:
                        # Use display name if available
                        recipients.append(recipient.Name)
            except:
                pass

            # Date - fast parsing
            date_str = ""
            try:
                # Try most common date field first
                received_time = getattr(item, 'ReceivedTime', None)
                if received_time and hasattr(received_time, 'strftime'):
                    date_str = received_time.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    # Quick fallback
                    sent_time = getattr(item, 'SentOn', None)
                    if sent_time and hasattr(sent_time, 'strftime'):
                        date_str = sent_time.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        date_str = "Unknown date"
            except Exception:
                date_str = "Unknown date"
            
            # Body - prioritize HTML for better formatting
            body_html = getattr(item, 'HTMLBody', '') or ''
            body_text = getattr(item, 'Body', '') or ''

            # Attachments - simplified for performance
            attachments = []

            try:
                attachment_count = getattr(item.Attachments, 'Count', 0)
                for i in range(1, attachment_count + 1):
                    try:
                        attachment = item.Attachments.Item(i)

                        # Get basic attachment properties only
                        att_name = getattr(attachment, 'FileName', '') or getattr(attachment, 'DisplayName', f'attachment_{i}')
                        att_size = getattr(attachment, 'Size', 0)

                        # Determine content type from extension
                        content_type = 'application/octet-stream'
                        if att_name:
                            ext = att_name.lower().split('.')[-1] if '.' in att_name else ''
                            content_type_map = {
                                'pdf': 'application/pdf',
                                'jpg': 'image/jpeg', 'jpeg': 'image/jpeg',
                                'png': 'image/png',
                                'gif': 'image/gif',
                                'txt': 'text/plain',
                                'zip': 'application/zip'
                            }
                            content_type = content_type_map.get(ext, 'application/octet-stream')

                        # Create attachment object - no data extraction for performance
                        att = Attachment(
                            filename=att_name,
                            size=att_size,
                            content_type=content_type
                        )
                        att.data = None  # Skip data extraction for speed

                        attachments.append(att)

                    except Exception as e:
                        self.log_warning(f"Error processing attachment {i}: {e}")
                        continue

            except Exception as e:
                self.log_warning(f"Error processing attachments: {e}")

            # Basic HTML cleanup for performance
            if body_html:
                import re
                # Basic cleanup only
                body_html = re.sub(r'<\?xml[^>]*\?>', '', body_html)
                body_html = re.sub(r'<o:p[^>]*>', '', body_html)
                body_html = re.sub(r'</o:p>', '', body_html)
                # Fix broken CID images with placeholder
                body_html = re.sub(r'src="cid:([^"]*)"', 'src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="', body_html)

            # Create EmailMessage
            message = EmailMessage(
                subject=subject,
                sender=sender_email,
                sender_name=sender_name,
                recipients=recipients,
                body_text=body_text,
                body_html=body_html,
                date=date_str,
                attachments=attachments,
                source_file=folder_name,
                source_format='pst'
            )
            
            return message
            
        except Exception as e:
            self.log_error(f"Error converting Outlook item: {e}")
            return None
    
    def extract_attachment_data(self, message: EmailMessage, attachment_index: int) -> Optional[bytes]:
        """Extract attachment data on-demand"""

        # This would require re-opening the PST and finding the specific message
        # For now, return None - attachments are extracted during initial load
        self.log_warning("On-demand attachment extraction not yet implemented for PST files")
        return None

    def save_messages(self, collection: EmailCollection, file_path: Path,
                     progress: Optional[ProgressCallback] = None) -> bool:
        """Save messages to PST file (not implemented - complex operation)"""

        # PST writing is very complex and requires careful handling
        # For now, we'll focus on reading PST files
        raise MailEngineError("PST writing not yet implemented. Use export to MBOX or EML instead.")
    
    def validate_file(self, file_path: Path) -> tuple[bool, str]:
        """Validate if file is a readable PST file"""

        if not file_path.exists():
            return False, "File does not exist"

        if file_path.suffix.lower() not in self.supported_extensions:
            return False, f"Unsupported extension: {file_path.suffix}"

        # Try to check PST signature
        try:
            with open(file_path, 'rb') as f:
                signature = f.read(4)
                if signature == b'!BDN':  # PST signature
                    return True, "Valid PST file"
                else:
                    return False, "Invalid PST signature"
        except Exception as e:
            return False, f"Cannot read file: {e}"
