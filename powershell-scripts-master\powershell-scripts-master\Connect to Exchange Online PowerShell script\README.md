﻿## Quickly Connect to Exchange Online PowerShell (In a Single Step)
We have written a user-friendly PowerShell script to connect Exchange Online PowerShell with MFA/non-MFA account.

***Sample Output:***

This PowerShell script helps you connect to Exchange Online PowerShell using both regular and MFA-enabled accounts. If you successfully connected to Exchange Online PowerShell, the output looks similar to the below screenshot.

![Connect to Exchange Online PowerShell]( https://o365reports.com/wp-content/uploads/2019/08/Create-PowerShell-session-to-Exchange-Online.png)

## Microsoft 365 Reporting tool by AdminDroid
Elevate your Exchange Online administration with the [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub). Access 1800+ pre-built reports and dashboards for comprehensive insights and streamlined operations.

*Find complete Exchange Online reporting solutions through AdminDroid: [https://demo.admindroid.com/#/1/11/dashboards/38](https://demo.admindroid.com/#/1/11/dashboards/38)*

