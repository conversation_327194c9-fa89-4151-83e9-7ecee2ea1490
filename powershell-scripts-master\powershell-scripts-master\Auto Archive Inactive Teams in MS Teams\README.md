﻿# Archive Inactive Teams in Microsoft Teams

Not sure how to handle inactive Microsoft Teams? Learn how to auto-archive inactive teams in Microsoft Teams to ensure security while keeping all your conversations and files accessible.

***Sample Output***

The script identifies all inactive teams in your organization and archives teams with no activity over a specific period. By default, it generates a Microsoft inactive teams report, and while archiving, it generates a CSV file of all archived teams.

**Inactive Teams in Microsoft Teams Report**

![Inactive Microsoft Teams](<https://o365reports.com/wp-content/uploads/2025/03/inactive-teams-in-microsoft-teams-report.png?v=1742276450>)

**Archive Inactive Teams in Microsoft 365**

![Archived Inactive Microsoft Teams](<https://o365reports.com/wp-content/uploads/2025/03/archive-inactive-teams-in-microsoft-teams.png?v=1742276792>)

## Microsoft 365 Reporting tool by AdminDroid 
Transform your Microsoft Teams data into actionable insights with [AdminDroid’s Microsoft 365 Auditing tool](https://admindroid.com/?src=GitHub), it offers over 1900 pre-built reports and 30+ dynamic dashboards to help you manage inactive teams with ease. 

*Access detailed Archived Microsoft Teams reports with AdminDroid:* [*https://demo.admindroid.com/#/1/11/reports/60051/1/20*](https://demo.admindroid.com/#/1/11/reports/60051/1/20)

