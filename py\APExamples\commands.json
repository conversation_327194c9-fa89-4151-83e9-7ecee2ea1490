{"EXO": [{"text": "Remove-MailboxFolderPermission -Identity <EMAIL>:\\kalender -user \"xxx\" -Confirm:$false", "favorite": false}, {"text": "Remove-MailboxFolderPermission -Identity <EMAIL>:\\kalender -ResetDelegateUserCollection -Confirm:$false", "favorite": false}, {"text": "Remove-MailboxPermission <EMAIL> -User xxx -AccessRights FullAccess -Confirm:$false", "favorite": false}, {"text": "Enable-Mailbox <user mailbox> -AutoExpandingArchive", "favorite": false}, {"text": "Search-MailboxAuditLog -Mailboxes \"UserNameHere\" -LogonTypes Owner -ShowDetails -Operations HardDelete, SoftDelete, MoveToDeletedItems -StartDate \"2024-11-15\" -EndDate \"2024-11-22\"", "favorite": false}, {"text": "New-DynamicDistributionGroup -Name \"Partnere1\" -RecipientFilter { (Title -like \"Partnere, Advokat*\") -and (RecipientType -eq \"UserMailbox\") }", "favorite": false}, {"text": "New-DynamicDistributionGroup -Name \"Alle1\" -PrimarySmtpAddress <EMAIL> -RecipientFilter { (RecipientTypeDetails -eq \"UserMailbox\") -and (Company -eq \"Andersen Partners\") -and (Enabled -eq $true) }", "favorite": false}, {"text": "Add-Mailboxpermission -Identity <EMAIL> -User <EMAIL> -Accessrights fullaccess -Automapping:$false", "favorite": true}, {"text": "Add-RecipientPermission <EMAIL>-AccessRights SendAs -Trustee <EMAIL>", "favorite": false}, {"text": "Add-MailboxFolderPermission -Identity <EMAIL>:\\kalender -user <EMAIL> -AccessRights editor", "favorite": true}, {"text": "Add-DistributionGroupMember -Identity groupName -Member userName", "favorite": false}, {"text": "Add-MailboxFolderPermission -Identity <EMAIL>:\\kalender -User <EMAIL> -AccessRights Editor -SharingPermissionFlags Delegate, CanViewPrivateItems", "favorite": false}, {"text": "Set-MailboxFolderPermission -Identity <EMAIL>:\\kalender -User Default -AccessRights Editor", "favorite": true}, {"text": "Set-MailboxFolderPermission -Identity <EMAIL>:\\kalender -User <EMAIL> -AccessRights Editor -SharingPermissionFlags Delegate, CanViewPrivateItems", "favorite": false}, {"text": "Set-Mailbox -Identity \"<EMAIL>\" -ForwardingAddress \"<EMAIL>\" -DeliverToMailboxAndForward $True", "favorite": false}, {"text": "Set-Mailbox <EMAIL> -Type Shared", "favorite": false}, {"text": "Set-Mailbox <EMAIL> -Type Regular", "favorite": false}, {"text": "Set-Mailbox <EMAIL> -HiddenFromAddressListsEnabled:$true", "favorite": false}, {"text": "Set-Mailbox DEsagsadm -GrantSendOnBehalfTo @{Add=\"lpa\", \"swj\", \"llv\"}", "favorite": false}, {"text": "Set-Mailbox -Identity \"username\" -AcceptMessagesOnlyFrom @{add=\"jdh_admin\"} -RequireSenderAuthenticationEnabled $true", "favorite": false}, {"text": "Set-DistributionGroup <EMAIL> -GrantSendOnBehalfOfTo <EMAIL>", "favorite": false}, {"text": "Set-FocusedInbox -Identity <EMAIL> -FocusedInboxOn $false", "favorite": false}, {"text": "Set-CASMailbox <EMAIL> -OWAforDevicesEnabled $false", "favorite": false}, {"text": "Set-CASMailbox <EMAIL> -ActiveSyncEnabled $false", "favorite": false}, {"text": "Set-CASMailbox <EMAIL> -OWAEnabled $false", "favorite": false}, {"text": "Set-ExternalInOutlook -AllowList \"<EMAIL>\"", "favorite": false}, {"text": "Set-ExternalInOutlook -Enabled $true", "favorite": false}, {"text": "Set-ExternalInOutlook -AllowList @{Add=\"<EMAIL>\"}", "favorite": false}, {"text": "Set-ExternalInOutlook -AllowList @{Remove=\"<EMAIL>\"}", "favorite": false}, {"text": "Set-MailboxRegionalConfiguration -Identity \"[navn her]\" -Language da-DK -DateFormat \"dd-MM-yyyy\" -LocalizeDefaultFolderName", "favorite": false}, {"text": "Get-Mailbox <EMAIL> | fl displayname, GrantSendOnBehalfTo", "favorite": false}, {"text": "Get-MailboxFolderPermission -Identity <EMAIL>:\\kalender", "favorite": true}, {"text": "Get-FocusedInbox -Identity <EMAIL>", "favorite": false}, {"text": "Get-CASMailbox <EMAIL>| Select ActiveSyncEnabled, OwaEnabled, OWAforDevicesEnabled", "favorite": false}, {"text": "Get-Mailbox <EMAIL> | Select HiddenFromAddressListsEnabled", "favorite": false}, {"text": "Get-Mailbox -RecipientTypeDetails UserMailbox,SharedMailbox -ResultSize Unlimited | Get-MailboxPermission -User <EMAIL>", "favorite": false}, {"text": "Get-Mailbox | % { Get-MailboxFolderPermission (($_.PrimarySmtpAddress.ToString())+”:\\kalender”) -User *UserNameHere* -ErrorAction SilentlyContinue} | select Identity,User,AccessRights", "favorite": false}, {"text": "(Get-Mailbox -ResultSize Unlimited).count", "favorite": false}, {"text": "(Get-Mailbox -RecipientTypeDetails SharedMailbox -ResultSize Unlimited).count", "favorite": false}, {"text": "Get-InboxRule -Mailbox <EMAIL>", "favorite": false}, {"text": "Get-MailboxFolderStatistics -Identity [username] | Select-Object FolderPath, FolderAndSubfolderSize, ArchivePolicy", "favorite": false}, {"text": "Get-MailboxStatistics [username] | ft DisplayName, TotalItemSize, ItemCount", "favorite": false}, {"text": "Get-CalendarProcessing [room name here] | fl", "favorite": false}, {"text": "Get-MailboxRegionalConfiguration [navn her]", "favorite": false}, {"text": "Get-UnifiedGroup <group name> | Set-UnifiedGroup -UnifiedGroupWelcomeMessageEnabled:$false", "favorite": false}, {"text": "Get-AntiPhishPolicy | select TargetedUsersToProtect -ExpandProperty TargetedUsersToProtect | Sort-Object -Descending", "favorite": false}, {"text": "Get-Mailboxpermission -Identity <EMAIL>", "favorite": false}], "SPO": [{"text": "Get-SPOSite -limit ALL -includepersonalsite $True | Select URL, Owner", "favorite": false}, {"text": "Restore-SPODeletedSite -Identity <url here>", "favorite": false}, {"text": "Set-SPOUser -Site [link her] -LoginName <EMAIL> -IsSiteCollectionAdmin $True", "favorite": false}, {"text": "Get-SPOUser -Site [link her] -LoginName [username her] | fl", "favorite": false}, {"text": "Set-SPOUser -site [link her] -LoginName [username her] -IsSiteCollectionAdmin $False", "favorite": false}, {"text": "Add-SPOOrgAssetsLibrary -LibraryURL \"url her\" -OrgAssetType OfficeTemplateLibrary", "favorite": false}], "MsGraph": [{"text": "Get-MgContext", "favorite": false}, {"text": "Connect-MgGraph -Scopes \"User.Read.All\"", "favorite": false}, {"text": "Get-MgContext | Select -ExpandProperty Scopes", "favorite": false}, {"text": "Get-MgUserLicenseDetail -UserId \"<EMAIL>\" | Select Name, SkuPartNumber", "favorite": false}, {"text": "Connect-MgGraph -Scopes \"User.Read.All\", \"Group.Read.All\", \"Directory.Read.All\"", "favorite": false}, {"text": "Get-MgUserMemberOf -UserId \"<EMAIL>\" | Select DisplayName, Id", "favorite": false}, {"text": "Connect-MgGraph -Scopes 'Policy.Read.All'", "favorite": false}, {"text": "Get-MgIdentityConditionalAccessNamedLocation", "favorite": false}, {"text": "(Get-MgIdentityConditionalAccessNamedLocation -NamedLocationId 1c0c267f-3e8a-46e9-b64b-1437c67c2079).AdditionalProperties.countriesAndRegions", "favorite": true}], "ASR and Defender": [{"text": "(Get-MpPreference).AttackSurfaceReductionOnlyExclusions", "favorite": false}, {"text": "Add-MpPreference -AttackSurfaceReductionOnlyExclusions <fully qualified path or resource>", "favorite": false}, {"text": "Get-MpPreference | Select-Object -Property ExclusionPath -ExpandProperty ExclusionPath", "favorite": false}, {"text": "Get-MpPreference | Select AttackSurfaceReductionOnlyExclusions, AttackSurfaceReductionRules_Actions, AttackSurfaceReductionRules_Ids", "favorite": false}]}