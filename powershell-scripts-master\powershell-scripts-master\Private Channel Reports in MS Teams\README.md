## Find Microsoft Teams Private Channel Membership Report with PowerShell
Export Microsoft Teams Private Channel membership report for monitoring team collaboration, ensuring compliance, and enhancing overall security.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Private Channel Reports in MS Teams](https://o365reports.com/wp-content/uploads/2024/01/Action-1.png?v=1705575592)
## Microsoft 365 Reporting Tool by AdminDroid
Need more detailed insights? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) delivers over 1800 pre-configured reports and dashboards, a perfect complement to your PowerShell scripts.

*View more comprehensive MS Teams private channel report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/60059/1/20>* 
