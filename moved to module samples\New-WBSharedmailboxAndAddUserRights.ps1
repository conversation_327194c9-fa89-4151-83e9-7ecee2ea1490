function New-WBSharedmailboxAndAddUserRights {
  <#
  .SY<PERSON><PERSON><PERSON><PERSON>
  Creates a new AP shared Whistleblower mailbox.

  .DESCRIPTION
  Creates a shared mailbox to be used for Whistleblower- An then add <PERSON><PERSON><PERSON> and RPA01 as fullaccess

  .EXAMPLE
  New-WBSharedmailboxAndAddUserRights -sharedMailboxName TestCompany

  .NOTES
  General notes
  #>

  [CmdletBinding()]
  param (
    [parameter(Mandatory=$true)]
    $sharedMailboxName
  )
  # Create shared mailbox
  New-Mailbox -Shared -Name $sharedMailboxName -DisplayName $sharedMailboxName

  # Add user permissions
  $users = "CGU", "RPA01"

  foreach ($u in $users) {
      Add-Mailboxpermission -Identity $sharedMailboxName -User "$<EMAIL>" -Accessrights fullaccess -Automapping:$false
      Add-RecipientPermission $sharedMailboxName -AccessRights SendAs -Trustee "$<EMAIL>" -confirm:$false
  }

  Get-MailboxPermission $sharedMailboxName
  Get-RecipientPermission $sharedMailboxName

}