﻿## SPO File Folder Storage Consumption Report
Learn how to retrieve SharePoint file & folder storage usage report using PowerShell and optimize your storage.

***Sample Output***

The script analyzes and exports two reports for file and folder storage consumption separately such as, 

**SPO File Storage consumption Report:**

![SPO File Storage Consumption Report](https://o365reports.com/wp-content/uploads/2024/08/file-storage-consumption-1-768x201.png?v=1723025898)

**SPO Folder Storage Consumption Report:**

![SPO Folder Storage Consumption Report](https://o365reports.com/wp-content/uploads/2024/08/Folder-storage-consumption-768x249.png?v=1723022088)
## Microsoft 365 Reporting Tool by AdminDroid
If this script is helpful, you’ll love [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub)! With over 1800 pre-built reports and dashboards, it’s a fantastic way to dive deeper into your M365 data.

*View detailed SharePoint storage consumption reports in AdminDroid: [https://demo.admindroid.com/#/1/11/reports/30001/1/20](https://demo.admindroid.com/#/1/11/reports/30001/1/20)*

