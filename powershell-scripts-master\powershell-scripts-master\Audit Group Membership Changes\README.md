## Audit Group Membership Changes in M365 Using PowerShell
Effortlessly audit group membership changes in Microsoft 365 with this PowerShell script. Access granular reports for robust security!

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Audit Group Membership Changes Report](https://o365reports.com/wp-content/uploads/2024/03/Audit-group-membership-changes-audit-sample-output-.png?v=1709626105)

## Microsoft 365 Reporting Tool by AdminDroid

For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*AdminDroid – Monitor Microsoft 365 Group Activities for Free! <https://demo.admindroid.com/#/1/11/reports/20028/1/20>*

