﻿## **Find Expired Anyone Links**
Learn how to retrieve all expired anonymous links in SharePoint Online using PowerShell. Prevent unauthorized access by identifying and revoking links that are no longer valid.

***Sample Output***

The exported report on anonymous link expiration looks like the screenshot below.

![Expired anyone links report](https://o365reports.com/wp-content/uploads/2024/07/Find-Expired-Anyone-links-in-SPO-1536x290.png?v=1721118324)

## **Powerful Microsoft 365 Reporting Tool by AdminDroid**
Need more than what this script offers? Explore [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) to get access to 1800+ out-of-box M365 reports and insightful dashboards.

*View more comprehensive Microsoft 365 reports through AdminDroid:*

[*https://demo.admindroid.com/*](https://demo.admindroid.com/)


