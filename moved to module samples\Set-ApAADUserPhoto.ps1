
function Set-ApAADUserPhoto {
    
    if (!(Get-MgContext)) {
        Connect-MgGraph -Scopes "User.ReadWrite.All"
    }
    # Get photos
    $PhotoLocation = "C:\Users\<USER>\OneDrive - Andersen Partners Advokatpartnerselskab\Portrait\Portrait upload script\Photos\"
    #Set-Location $PhotoLocation
    $photos = Get-ChildItem -Path $PhotoLocation
    Write-Host "Have you added phothos to path: " -ForegroundColor Cyan
    Write-Host "$PhotoLocation" -ForegroundColor DarkCyan
    Read-Host "If Yes, press Enter"
 
    foreach ($photo in $photos) {
        # Get userName from current photo
        $PhotoUsername = (Get-ChildItem $Photo).Name.Replace('.jpg','')

        # Upload billedet til brugeren
        Set-MgUserPhotoContent -UserId "$<EMAIL>" -InFile "C:\Users\<USER>\OneDrive - Andersen Partners Advokatpartnerselskab\Portrait\Portrait upload script\Photos\$PhotoUsername.jpg"
        Write-Host "Photo has been added to user: $PhotoUsername" -ForegroundColor Cyan

        # Move photo to 'Done' folder
        Move-Item -Path $photo.FullName -Destination "C:\Users\<USER>\OneDrive - Andersen Partners Advokatpartnerselskab\Portrait\Portrait upload script\Done" -Force -Verbose
    }
    Disconnect-MgGraph > $null 2>&1
}