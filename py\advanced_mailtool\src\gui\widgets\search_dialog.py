"""
Advanced search dialog for email messages
"""

from typing import List, Optional
from datetime import datetime, date

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QLineEdit, QPushButton, QComboBox, QDateEdit,
                              QCheckBox, QGroupBox, QFormLayout, QSpinBox,
                              QTextEdit, QTabWidget, QWidget)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont

from ...core.email_message import EmailMessage, EmailCollection


class SearchDialog(QDialog):
    """Advanced search dialog for email messages"""
    
    search_requested = Signal(dict)  # search_criteria
    
    def __init__(self, collection: EmailCollection, parent=None):
        super().__init__(parent)
        
        self.collection = collection
        
        self.setWindowTitle("Advanced Search")
        self.setModal(True)
        self.setMinimumSize(500, 600)
        self.resize(600, 700)
        
        self.setup_ui()
        self.populate_suggestions()
    
    def setup_ui(self):
        """Setup the user interface"""
        
        layout = QVBoxLayout(self)
        layout.setSpacing(16)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        title = QLabel("🔍 Advanced Email Search")
        title.setProperty("class", "header")
        layout.addWidget(title)
        
        # Create tabs
        self.tabs = QTabWidget()
        
        # Basic search tab
        basic_tab = QWidget()
        self.setup_basic_search(basic_tab)
        self.tabs.addTab(basic_tab, "Basic Search")
        
        # Advanced search tab
        advanced_tab = QWidget()
        self.setup_advanced_search(advanced_tab)
        self.tabs.addTab(advanced_tab, "Advanced Search")
        
        # Date range tab
        date_tab = QWidget()
        self.setup_date_search(date_tab)
        self.tabs.addTab(date_tab, "Date Range")
        
        layout.addWidget(self.tabs)
        
        # Results preview
        preview_group = QGroupBox("Search Preview")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(80)
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlainText("Enter search criteria to see preview...")
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("Clear All")
        self.clear_btn.setProperty("class", "secondary")
        self.clear_btn.clicked.connect(self.clear_all)
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.search_btn = QPushButton("Search")
        self.search_btn.clicked.connect(self.perform_search)
        self.search_btn.setDefault(True)
        button_layout.addWidget(self.search_btn)
        
        layout.addLayout(button_layout)
    
    def setup_basic_search(self, widget):
        """Setup basic search tab"""
        
        layout = QVBoxLayout(widget)
        layout.setSpacing(12)
        
        # Quick search
        quick_group = QGroupBox("Quick Search")
        quick_layout = QFormLayout(quick_group)
        
        self.quick_search_edit = QLineEdit()
        self.quick_search_edit.setPlaceholderText("Search in subject, sender, and content...")
        self.quick_search_edit.textChanged.connect(self.update_preview)
        quick_layout.addRow("Search:", self.quick_search_edit)
        
        layout.addWidget(quick_group)
        
        # Common filters
        filters_group = QGroupBox("Common Filters")
        filters_layout = QVBoxLayout(filters_group)
        
        self.has_attachments_cb = QCheckBox("Has attachments")
        self.has_attachments_cb.stateChanged.connect(self.update_preview)
        filters_layout.addWidget(self.has_attachments_cb)
        
        self.unread_only_cb = QCheckBox("Unread messages only")
        self.unread_only_cb.stateChanged.connect(self.update_preview)
        filters_layout.addWidget(self.unread_only_cb)
        
        self.flagged_only_cb = QCheckBox("Flagged messages only")
        self.flagged_only_cb.stateChanged.connect(self.update_preview)
        filters_layout.addWidget(self.flagged_only_cb)
        
        layout.addWidget(filters_group)
        
        layout.addStretch()
    
    def setup_advanced_search(self, widget):
        """Setup advanced search tab"""
        
        layout = QVBoxLayout(widget)
        layout.setSpacing(12)
        
        # Specific fields
        fields_group = QGroupBox("Search in Specific Fields")
        fields_layout = QFormLayout(fields_group)
        
        self.subject_edit = QLineEdit()
        self.subject_edit.setPlaceholderText("Search in subject...")
        self.subject_edit.textChanged.connect(self.update_preview)
        fields_layout.addRow("Subject:", self.subject_edit)
        
        self.sender_combo = QComboBox()
        self.sender_combo.setEditable(True)
        self.sender_combo.setPlaceholderText("Search by sender...")
        self.sender_combo.currentTextChanged.connect(self.update_preview)
        fields_layout.addRow("From:", self.sender_combo)
        
        self.recipient_edit = QLineEdit()
        self.recipient_edit.setPlaceholderText("Search by recipient...")
        self.recipient_edit.textChanged.connect(self.update_preview)
        fields_layout.addRow("To:", self.recipient_edit)
        
        self.content_edit = QLineEdit()
        self.content_edit.setPlaceholderText("Search in message content...")
        self.content_edit.textChanged.connect(self.update_preview)
        fields_layout.addRow("Content:", self.content_edit)
        
        layout.addWidget(fields_group)
        
        # Size filters
        size_group = QGroupBox("Message Size")
        size_layout = QFormLayout(size_group)
        
        size_row = QHBoxLayout()
        
        self.size_min_spin = QSpinBox()
        self.size_min_spin.setRange(0, 999999)
        self.size_min_spin.setSuffix(" KB")
        self.size_min_spin.valueChanged.connect(self.update_preview)
        size_row.addWidget(self.size_min_spin)
        
        size_row.addWidget(QLabel("to"))
        
        self.size_max_spin = QSpinBox()
        self.size_max_spin.setRange(0, 999999)
        self.size_max_spin.setSuffix(" KB")
        self.size_max_spin.setValue(999999)
        self.size_max_spin.valueChanged.connect(self.update_preview)
        size_row.addWidget(self.size_max_spin)
        
        size_layout.addRow("Size range:", size_row)
        
        layout.addWidget(size_group)
        
        layout.addStretch()
    
    def setup_date_search(self, widget):
        """Setup date search tab"""
        
        layout = QVBoxLayout(widget)
        layout.setSpacing(12)
        
        # Date range
        date_group = QGroupBox("Date Range")
        date_layout = QFormLayout(date_group)
        
        self.date_from_edit = QDateEdit()
        self.date_from_edit.setCalendarPopup(True)
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_from_edit.dateChanged.connect(self.update_preview)
        date_layout.addRow("From:", self.date_from_edit)
        
        self.date_to_edit = QDateEdit()
        self.date_to_edit.setCalendarPopup(True)
        self.date_to_edit.setDate(QDate.currentDate())
        self.date_to_edit.dateChanged.connect(self.update_preview)
        date_layout.addRow("To:", self.date_to_edit)
        
        layout.addWidget(date_group)
        
        # Quick date filters
        quick_date_group = QGroupBox("Quick Date Filters")
        quick_date_layout = QVBoxLayout(quick_date_group)
        
        date_buttons = [
            ("Today", lambda: self.set_date_range(0)),
            ("Yesterday", lambda: self.set_date_range(1)),
            ("Last 7 days", lambda: self.set_date_range(7)),
            ("Last 30 days", lambda: self.set_date_range(30)),
            ("Last 90 days", lambda: self.set_date_range(90)),
        ]
        
        for text, callback in date_buttons:
            btn = QPushButton(text)
            btn.setProperty("class", "secondary")
            btn.clicked.connect(callback)
            quick_date_layout.addWidget(btn)
        
        layout.addWidget(quick_date_group)
        
        layout.addStretch()
    
    def populate_suggestions(self):
        """Populate suggestion lists"""
        
        # Get unique senders
        senders = set()
        for message in self.collection:
            if message.sender:
                senders.add(message.sender)
        
        # Add to sender combo
        self.sender_combo.addItems(sorted(senders))
    
    def set_date_range(self, days_back: int):
        """Set date range to last N days"""
        
        today = QDate.currentDate()
        if days_back == 0:
            # Today only
            self.date_from_edit.setDate(today)
            self.date_to_edit.setDate(today)
        else:
            # Last N days
            self.date_from_edit.setDate(today.addDays(-days_back))
            self.date_to_edit.setDate(today)
        
        self.update_preview()
    
    def get_search_criteria(self) -> dict:
        """Get current search criteria"""
        
        criteria = {}
        
        # Basic search
        if self.quick_search_edit.text().strip():
            criteria['quick_search'] = self.quick_search_edit.text().strip()
        
        # Advanced search
        if self.subject_edit.text().strip():
            criteria['subject'] = self.subject_edit.text().strip()
        
        if self.sender_combo.currentText().strip():
            criteria['sender'] = self.sender_combo.currentText().strip()
        
        if self.recipient_edit.text().strip():
            criteria['recipient'] = self.recipient_edit.text().strip()
        
        if self.content_edit.text().strip():
            criteria['content'] = self.content_edit.text().strip()
        
        # Filters
        if self.has_attachments_cb.isChecked():
            criteria['has_attachments'] = True
        
        if self.unread_only_cb.isChecked():
            criteria['unread_only'] = True
        
        if self.flagged_only_cb.isChecked():
            criteria['flagged_only'] = True
        
        # Size range
        if self.size_min_spin.value() > 0 or self.size_max_spin.value() < 999999:
            criteria['size_min'] = self.size_min_spin.value() * 1024  # Convert to bytes
            criteria['size_max'] = self.size_max_spin.value() * 1024
        
        # Date range
        date_from = self.date_from_edit.date().toPython()
        date_to = self.date_to_edit.date().toPython()
        
        criteria['date_from'] = datetime.combine(date_from, datetime.min.time())
        criteria['date_to'] = datetime.combine(date_to, datetime.max.time())
        
        return criteria
    
    def update_preview(self):
        """Update search preview"""
        
        criteria = self.get_search_criteria()
        
        if not criteria:
            self.preview_text.setPlainText("Enter search criteria to see preview...")
            return
        
        # Count matching messages
        matching_count = self.count_matching_messages(criteria)
        total_count = len(self.collection)
        
        preview_lines = []
        preview_lines.append(f"Matching messages: {matching_count} of {total_count}")
        
        # Show criteria
        if 'quick_search' in criteria:
            preview_lines.append(f"Quick search: '{criteria['quick_search']}'")
        
        if 'subject' in criteria:
            preview_lines.append(f"Subject contains: '{criteria['subject']}'")
        
        if 'sender' in criteria:
            preview_lines.append(f"From: '{criteria['sender']}'")
        
        if 'recipient' in criteria:
            preview_lines.append(f"To: '{criteria['recipient']}'")
        
        if 'content' in criteria:
            preview_lines.append(f"Content contains: '{criteria['content']}'")
        
        filters = []
        if criteria.get('has_attachments'):
            filters.append("Has attachments")
        if criteria.get('unread_only'):
            filters.append("Unread only")
        if criteria.get('flagged_only'):
            filters.append("Flagged only")
        
        if filters:
            preview_lines.append(f"Filters: {', '.join(filters)}")
        
        if 'date_from' in criteria:
            date_from = criteria['date_from'].strftime('%Y-%m-%d')
            date_to = criteria['date_to'].strftime('%Y-%m-%d')
            preview_lines.append(f"Date range: {date_from} to {date_to}")
        
        self.preview_text.setPlainText("\n".join(preview_lines))
    
    def count_matching_messages(self, criteria: dict) -> int:
        """Count messages matching criteria"""
        
        count = 0
        
        for message in self.collection:
            if self.message_matches_criteria(message, criteria):
                count += 1
        
        return count
    
    def message_matches_criteria(self, message: EmailMessage, criteria: dict) -> bool:
        """Check if message matches search criteria"""
        
        # Quick search
        if 'quick_search' in criteria:
            query = criteria['quick_search'].lower()
            if not (query in message.subject.lower() or
                    query in message.sender.lower() or
                    query in message.body_text.lower() or
                    query in message.body_html.lower()):
                return False
        
        # Subject
        if 'subject' in criteria:
            if criteria['subject'].lower() not in message.subject.lower():
                return False
        
        # Sender
        if 'sender' in criteria:
            if criteria['sender'].lower() not in message.sender.lower():
                return False
        
        # Recipient
        if 'recipient' in criteria:
            recipient_match = False
            for recipient in message.recipients:
                if criteria['recipient'].lower() in recipient.lower():
                    recipient_match = True
                    break
            if not recipient_match:
                return False
        
        # Content
        if 'content' in criteria:
            content_query = criteria['content'].lower()
            if not (content_query in message.body_text.lower() or
                    content_query in message.body_html.lower()):
                return False
        
        # Filters
        if criteria.get('has_attachments') and not message.has_attachments:
            return False
        
        if criteria.get('unread_only') and message.is_read:
            return False
        
        if criteria.get('flagged_only') and not message.is_flagged:
            return False
        
        # Size range
        if 'size_min' in criteria and message.size < criteria['size_min']:
            return False
        
        if 'size_max' in criteria and message.size > criteria['size_max']:
            return False
        
        # Date range
        if 'date_from' in criteria and message.date:
            if message.date < criteria['date_from']:
                return False
        
        if 'date_to' in criteria and message.date:
            if message.date > criteria['date_to']:
                return False
        
        return True
    
    def clear_all(self):
        """Clear all search criteria"""
        
        # Basic search
        self.quick_search_edit.clear()
        self.has_attachments_cb.setChecked(False)
        self.unread_only_cb.setChecked(False)
        self.flagged_only_cb.setChecked(False)
        
        # Advanced search
        self.subject_edit.clear()
        self.sender_combo.setCurrentText("")
        self.recipient_edit.clear()
        self.content_edit.clear()
        self.size_min_spin.setValue(0)
        self.size_max_spin.setValue(999999)
        
        # Date range
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_to_edit.setDate(QDate.currentDate())
        
        self.update_preview()
    
    def perform_search(self):
        """Perform the search"""
        
        criteria = self.get_search_criteria()
        
        if not criteria:
            return
        
        self.search_requested.emit(criteria)
        self.accept()
