# GUI Framework
PySide6>=6.6.0

# Email handling
email-validator>=2.0.0

# File format support
olefile>=0.47  # For PST files
extract-msg>=0.54.0  # For MSG files

# Windows specific (PST support)
pywin32>=306 ; platform_system=="Windows"

# Archive handling
py7zr>=0.21.0  # For 7z archives

# Progress bars and UI enhancements
tqdm>=4.66.0

# Date/time handling
python-dateutil>=2.8.0

# Text processing
chardet>=5.2.0  # Character encoding detection

# PST handling - free alternatives
unidecode>=1.3.0  # Text normalization
# Note: PST is proprietary format - conversion recommended
