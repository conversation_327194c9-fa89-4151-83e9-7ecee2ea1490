## Audit File Access in SharePoint Online Using PowerShell
Audit file access in SharePoint Online using PowerShell to prevent unauthorized access for effective SharePoint Online management.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![SharePoint Online File Access Report](https://o365reports.com/wp-content/uploads/2024/01/Edited-Feature-Image-2-1024x211.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid

Take your Office 365 license management to the next level with the AdminDroid [Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1800+ pre-built reports and dashboards for in-depth analysis and efficient oversight.

*Simplify Your SharePoint Online File Audits with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/22081/1/20>*

