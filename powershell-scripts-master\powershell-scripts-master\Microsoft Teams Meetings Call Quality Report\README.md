## Check Teams Meeting Call Quality Using PowerShell
Export 6 Microsoft Teams meeting call quality reports using PowerShell, which helps you analyze the network, device, and audio health issues.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below.

![Teams call quality reports](https://m365scripts.com/wp-content/uploads/2024/05/Meeting-info-2-1024x103.png?v=**********).

## Microsoft 365 Reporting Tool by AdminDroid
For deeper insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), featuring over 1800 ready-to-use reports and insightful dashboards.

*Gain better control over your Microsoft Teams Meeting with AdminDroid. <https://demo.admindroid.com/#/1/11/reports/24016/1/20>*