## Export Non-audited Mailbox Actions Report using PowerShell 

Using this PowerShell script, you can identify non-audited mailbox activities and improve the mailbox security by enabling them.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Non-audited Mailbox Actions Report](https://o365reports.com/wp-content/uploads/2022/05/Non-audited-mailbox-actions.png?v=1705576521) 

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive Microsoft 365 reports through AdminDroid: <https://demo.admindroid.com/#/1/11/dashboards/10>*