## Get All External Users in SharePoint Online Using PowerShell

This PowerShell script exports all the external users in SharePoint Online to CSV file. You can also get the external users in the SharePoint sites.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![SharePoint Online External Users Report](https://o365reports.com/wp-content/uploads/2021/07/UC1.png?v=1705576674)


## Microsoft 365 Reporting Tool by AdminDroid 

Looking for detailed reporting? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, it seamlessly complements your PowerShell scripts.

*View more comprehensive sharepoint online external users report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/30403/1/20>*  


