<#

.SYNOPSIS
Script to list top large files in a SharePoint site

.NOTES
prerequisite
 - PnP.Powershell module
 - PowerShell 7

#>


# Variabler
$siteUrl     = "https://andersenpartnersdk.sharepoint.com/sites/SDLTradosData"
$libraryName = "Dokumenter"  # Navn på biblioteket (ændr efter behov)
$topN        = 50  # Antal største filer, der skal vises

# Log ind på SharePoint
Connect-PnPOnline -Url $siteUrl -UseWebLogin

# Hent alle filer fra biblioteket
Write-Host "`nHenter filer fra biblioteket '$libraryName'...`n" -ForegroundColor Cyan
$files = Get-PnPListItem -List $libraryName -PageSize 5000 -Fields FileRef, File_x0020_Size | Where-Object { $_.FieldValues.File_x0020_Size -ne $null }

# Konverter og sorter filerne efter størrelse
$largestFiles = $files | Select-Object @{Name="FileName";Expression={$_["FileRef"]}}, 
                                       @{Name="Size (MB)";Expression={[math]::Round($_["File_x0020_Size"] / 1MB, 2)}} |
                                       Sort-Object -Property "Size (MB)" -Descending |
                                       Select-Object -First $topN

# Output resultater
Write-Host "De $topN største filer i biblioteket:" -ForegroundColor Green
$largestFiles | Format-Table -AutoSize

# Alternativt gem som CSV-fil
$largestFiles | Export-Csv -Path "LargestFiles.csv" -NoTypeInformation -Encoding UTF8

# Log ud
Disconnect-PnPOnline