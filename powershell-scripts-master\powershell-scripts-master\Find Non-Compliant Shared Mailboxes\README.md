﻿## Identify Non-Compliant Shared Mailboxes in Microsoft 365  
Run the PowerShell script to track unlicensed shared mailboxes allowing direct sign-in access in Microsoft 365.

***Sample Output:***

The script generates an output CSV file with details of non-compliant shared mailboxes in Microsoft 365. The sample output will resemble the screenshot below.

![Non-Compliant Shared Mailboxes Report](https://o365reports.com/wp-content/uploads/2024/12/NonCompliant\_Shared\_Mailboxe-1.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive shared mailbox based reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/10013/1/20](https://demo.admindroid.com/#/1/11/reports/10013/1/20)*








