﻿## Get All OneDrive Site URLs for Users Using PowerShell
Download the handy PowerShell script to quickly compile a list of all users' OneDrive URLs in your organization

***Sample Output:***

The script exports an output CSV file that looks similar to the screenshot below.

![OneDrive URLs and Size](https://m365scripts.com/wp-content/uploads/2024/06/List-OneDrive-URLs-and-Size-604x175.png?v=1718874429)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive OneDrive reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/70021/1/20*](https://demo.admindroid.com/#/1/11/reports/70021/1/20)*



