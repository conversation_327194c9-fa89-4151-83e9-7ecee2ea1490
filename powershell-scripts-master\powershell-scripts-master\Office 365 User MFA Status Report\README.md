## Export Office 365 Users MFA Status to CSV Using PowerShell
This PowerShell script exports Office 365 users’ MFA status with Default MFA Method, AllMFAMethods, MFAPhone, MFAEmail, LicenseStatus, IsAdmin, SignInStatus.

***Sample Output:***

The exported MFA status report will look similar to the screenshots below.

![MFA enabled users report](https://o365reports.com/wp-content/uploads/2019/05/Get-MFA-status-PowerShell-1-768x146.png)
## Microsoft 365 Reporting Tool by AdminDroid
Seeking in-depth analysis? [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, perfectly complementing your PowerShell scripts.

*View more comprehensive MFA status report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/101/1/20>* 

