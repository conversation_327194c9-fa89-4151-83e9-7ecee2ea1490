## Audit Anonymous Access in SharePoint Online using PowerShell
This PowerShell script audits all the anonymous link activities such as link creation, updation, removal, access and export the report to CSV file.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Audit Anonymous Link Activity Report](https://o365reports.com/wp-content/uploads/2021/06/Anonymous-access-report-1024x257.png?v=1705576688)

## Microsoft 365 Reporting Tool by AdminDroid

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts.

*Gain Better Control Over Anonymous Links with AdminDroid:<https://demo.admindroid.com/#/1/11/reports/22058/1/20>*
