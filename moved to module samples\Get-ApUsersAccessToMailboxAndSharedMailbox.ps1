function Get-ApUsersAccessToMailboxAndSharedMailbox {
    
    [CmdletBinding()]
    
    param (
        [Parameter(Mandatory=$true,
        HelpMessage="Provide users mail to check...")]
        $user
    )
    #Get-EXOMailbox -RecipientTypeDetails UserMailbox,SharedMailbox -ResultSize Unlimited | Get-EXOMailboxPermission | Where-Object {$_.User -eq "$user"}
    Write-Progress "Checking mailbox access"
    Get-Mailbox -RecipientTypeDetails UserMailbox,SharedMailbox -ResultSize Unlimited | Get-MailboxPermission -User $user -Verbose
    
}
Get-ApUsersAccessToMailboxAndSharedMailbox

function Get-UserCalendarPermissions {
    param (
        [Parameter(Mandatory = $true)]
        [string]$userName
    )

    # Get all mailboxes in the organization
    $mailboxes = Get-Mailbox -ResultSize Unlimited

    # Loop through each mailbox to check calendar permissions for the specified user
    foreach ($mailbox in $mailboxes) {
        $access = (Get-MailboxFolderPermission -Identity "$($mailbox.PrimarySmtpAddress):\kalender" -User $userName -ErrorAction SilentlyContinue).AccessRights
        if ($access) {
            <# Action to perform if the condition is true #>
            Write-Output "User has $access access to: $mailbox"
            #Get-MailboxFolderPermission -Identity "$($mailbox.PrimarySmtpAddress):\kalender" -User $userName -ErrorAction SilentlyContinue
        }
    }
}
Get-UserCalendarPermissions