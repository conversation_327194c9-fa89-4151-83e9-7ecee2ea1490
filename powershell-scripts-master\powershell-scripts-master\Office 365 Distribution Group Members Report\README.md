## Export Office 365 Distribution Group Members to CSV Using PowerShell
This PowerShell script exports Office 365 Distribution Group members to CSV file along with Primary SMTP Address, <PERSON><PERSON>, Group Owner, Members &amp: Count, etc

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below

![Distribution Group Summary Report](https://o365reports.com/wp-content/uploads/2019/05/get-Office-365-distribution-group-members-report-3-768x109.png?v=1705576873)

## Microsoft 365 Reporting Tool by AdminDroid 
To delve deeper than this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), which offers over 1800 pre-built reports and insightful dashboards.

*Generate detailed reports for Office 365 distribution group members using AdminDroid: <https://demo.admindroid.com/#/1/11/reports/1018/1/20>*
