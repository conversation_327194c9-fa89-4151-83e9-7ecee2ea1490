

import pandas as pd
import os

# Filstier
input_file = r"c:\Users\<USER>\OneDrive - Andersen Partners Advokatpartnerselskab\Scripts\output.csv"
output_file = r"c:\Users\<USER>\OneDrive - Andersen Partners Advokatpartnerselskab\Scripts\output_with_full_path.csv"

# Base path
base_path = r"\\APA-PVW-FIL01\Patricia$"

# Læs CSV-filen
df = pd.read_csv(input_file, sep=';', encoding='utf-8-sig')

# Funktion til at generere korrekt full_path
def generate_full_path(row):
    folder_path = row['folder_path'].strip('\\')  # Fjern ekstra \ i starten/slutningen
    doc_file_name = row['DOC_FILE_NAME']  # Behold mellemrum i starten af filnavnet
    return os.path.join(base_path, folder_path, doc_file_name)

# Tilføj ny kolonne 'full_path'
df['full_path'] = df.apply(generate_full_path, axis=1)

# Gem den nye CSV-fil
df.to_csv(output_file, sep=';', index=False, encoding='utf-8-sig')

print(f"Filen er gemt som: {output_file}")