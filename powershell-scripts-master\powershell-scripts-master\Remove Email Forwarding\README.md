## Remove Email Forwarding in Office 365 Using PowerShell
This script helps to remove email forwarding configurations & disables inbox rules with forwarding for specific users & bulk users through CSV.

***Sample Output:***

The script will generate output CSV file(s) based on the forwarding configuration and one log file to provide detailed info on email forwarding removal.

*Email configuration report:*

![Email configuration report](https://m365scripts.com/wp-content/uploads/2022/07/Remove-email-forwarding-address-report.png?v=1694788236)

*Inbox rule with forwarding report:*

![Inbox rule with forwarding report](https://m365scripts.com/wp-content/uploads/2022/07/Remove-Inbox-Rules-forwarding-email.png?v=1694788235)

*Log File for Verification:*

![Log File for Verification](https://m365scripts.com/wp-content/uploads/2022/07/Stop-email-forwarding-log-file-report.png?v=1694788235)
## Microsoft 365 Reporting Tool by AdminDroid
Seeking in-depth analysis? [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, perfectly complementing your PowerShell scripts.

*View more comprehensive Mailbox with External Forwarding Inbox Rules report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10662/1/20>*
