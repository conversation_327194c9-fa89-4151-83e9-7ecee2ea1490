# Dette er en kopi af Test-APExamples.py med en simpel fix for syntaksfremhævning
# Jeg kopierer den originale fil og laver kun de nødvendige ændringer

import shutil
import os

# Kopier den originale fil
source = "py/APExamples/test/Test-APExamples.py"
target = "py/APExamples/test/Test-APExamples-BACKUP.py"

# Backup først
if os.path.exists(source):
    shutil.copy2(source, target)
    print(f"Backup oprettet: {target}")
else:
    print(f"Kilde fil ikke fundet: {source}")
