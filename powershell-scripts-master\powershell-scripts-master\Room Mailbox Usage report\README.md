## Get Office 365 Room Mailbox Usage Statistics Using PowerShell
Obtain Office 365 room mailbox usage statistics using PowerShell scripting and get valuable insights into meeting statistics and booking trends.

***Sample Output:***

The script exports an output CSV file that looks like the screenshots below.

*Room Mailbox Usage Statistics Report:*

![Exchange Online Meeting Room Usage Statistics Report](https://o365reports.com/wp-content/uploads/2023/05/Office-365-Room-Mailbox-Usage-Report.png?v=1705576025)

*Room Mailbox Usage Summary Report:*

![Room Mailbox Usage Summary Report](https://o365reports.com/wp-content/uploads/2023/05/Office-365-Room-Mailbox-Summary-Report.png?v=1705576024)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts.

*View more comprehensive room mailbox usage reports through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10004/1/20?easyFilter=%7B%22RecipientTypeDetails%22%3A%2216%22%7D>* 

