## Export Office 365 Mailbox Hold Report Using PowerShell

This PowerShell script exports O365 mailbox hold report and helps to identify the type of holds (litigation, in-place hold, retention) placed on the mailbox

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Office 365 Mailbox Hold Report](https://o365reports.com/wp-content/uploads/2021/06/Get-all-mailboxes-under-hold.png?v=1705576675) 

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for detailed reporting? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, it seamlessly complements your PowerShell scripts.

*View more comprehensive mailbox hold report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10625/1/20>*

