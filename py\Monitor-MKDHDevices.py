import os
import json
import time
import requests
from datetime import datetime
import platform

# Konfiguration
devices = {
    "Laptop": "*************",
    "GamerPC": "*************",
    "GamerPC WiFi": "*************",
    "PS5": "*************"
}

status_file = "/home/<USER>/device_status.json"
NTFY_URL = "https://ntfy.duksekkan.nohost.me"
NTFY_TOPIC = "8U6KbCsKyRAIaBvi"
NTFY_TOKEN = "Bearer tk_n3jhq9ds16la4c3lm6ng4e086aibl"

def is_online(ip):
    param = "-n" if platform.system().lower() == "windows" else "-c"
    try:
        return os.system(f"ping {param} 1 {ip} > nul 2>&1" if platform.system().lower() == "windows"
                         else f"ping {param} 1 -W 1 {ip} > /dev/null 2>&1") == 0
    except Exception as e:
        print(f"[ERROR] Ping failed for {ip}: {e}")
        return False

def send_ntfy(title, message):
    try:
        resp = requests.post(
            f"{NTFY_URL}/{NTFY_TOPIC}",
            data=message.encode("utf-8"),
            headers={
                "Content-Type": "text/plain; charset=utf-8",
                "Authorization": NTFY_TOKEN,
                "Title": title,
            },
            timeout=5
        )
        if resp.status_code != 200:
            print(f"[ERROR] ntfy.sh returned {resp.status_code}: {resp.text}")
    except Exception as e:
        print(f"[ERROR] Failed to send ntfy: {e}")

def load_status():
    if os.path.exists(status_file):
        try:
            with open(status_file, "r") as f:
                return json.load(f)
        except Exception as e:
            print(f"[ERROR] Could not load status file: {e}")
    return {}

def save_status(status):
    try:
        with open(status_file, "w") as f:
            json.dump(status, f)
    except Exception as e:
        print(f"[ERROR] Could not save status file: {e}")

def format_duration(seconds):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    if hours:
        return f"{hours}t {minutes}m"
    elif minutes:
        return f"{minutes} minutter"
    else:
        return f"{int(seconds)} sekunder"

def main():
    now = time.time()
    status = load_status()

    for name, ip in devices.items():
        online = is_online(ip)
        prev = status.get(name, {})

        if online and not prev.get("online", False):
            # Enhed blev tændt
            send_ntfy(f"{name} er TÆNDT", f"{name} blev registreret online kl. {datetime.now().strftime('%H:%M:%S')}")
            status[name] = {"online": True, "start_time": now}
            print(f"[INFO] {name} blev tændt.")
        elif not online and prev.get("online", False):
            # Enhed blev slukket
            start_time = prev.get("start_time", now)
            duration = format_duration(now - start_time)
            send_ntfy(f"{name} er SLUKKET", f"{name} var online i ca. {duration}")
            status[name] = {"online": False}
            print(f"[INFO] {name} blev slukket efter {duration}.")

    save_status(status)

if __name__ == "__main__":
    main()