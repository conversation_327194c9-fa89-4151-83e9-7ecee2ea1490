﻿## Find Inactive Teams in Microsoft Teams

Identify and manage inactive Microsoft Teams with PowerShell scripts to clean up unused teams, optimize resources, and prevent team overload.

***Sample Output***

This script exports all teams along with their last activity date and the number of inactive days. It helps identify inactive teams based on inactive days. The output file looks similar to the screenshot below.

![Inactive Microsoft teams](<https://o365reports.com/wp-content/uploads/2025/02/unused-teams-in-microsoft-365.png?v=1739844875>)

## Microsoft 365 Reporting tool by AdminDroid 
Looking for detailed Microsoft 365 insights? [AdminDroid Microsoft 365 auditing tool](https://admindroid.com/?src=GitHub) provides 1900+ ready-to-use reports and 30+ dashboards for an enhanced data experience.

*Access detailed Microsoft Teams reports with AdminDroid: [https://demo.admindroid.com/#/1/11/reports/60013/1/20](https://demo.admindroid.com/#/1/11/reports/60013/1/20)*



