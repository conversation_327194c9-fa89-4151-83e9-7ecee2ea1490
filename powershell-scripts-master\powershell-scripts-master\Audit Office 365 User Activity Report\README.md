﻿## Export Office 365 User Activity Report to CSV using PowerShell
This PowerShell script exports Office 365 user’s activity report to a CSV file with Activity Time, User, Operation, Result, Workload and Detailed Audit info.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Office 365 User Activity Report](https://o365reports.com/wp-content/uploads/2021/01/User-activity-report.png?v=1705576761)
## Microsoft 365 Reporting Tool by AdminDroid
For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*Monitor Office 365 Users’ Activities in a Few Mouse Clicks: <https://demo.admindroid.com/#/1/11/dashboards/44?defaultFilterUniqueId=1&easyFilter=%7B%22CreationTime%22%3A6%7D&filterId=172>*

