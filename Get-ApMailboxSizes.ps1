# Forbind til Exchange Online (kræver Exchange Online PowerShell V2)
Confirm-ExoConnection

# Timestamp
$timestamp = Get-Date -Format "dd-MM-yyyy HH:mm:ss"
$log = "[$timestamp][INFO]"

# Hent alle brugere og shared mailboxes
Write-Host "`n$log Henter mailbokse..." -ForegroundColor Gray
$mailboxes = Get-Mailbox -ResultSize Unlimited -RecipientTypeDetails UserMailbox, SharedMailbox

# Saml info
Write-Host "$log Indsamler mailbox information..." -ForegroundColor Gray
$result = $mailboxes | Select-Object `
    DisplayName,
    PrimarySmtpAddress,
    @{Name="MailboxSizeGB";Expression={ 
        [math]::Round(((Get-EXOMailboxStatistics -Identity $_.UserPrincipalName).TotalItemSize.Value.toBytes() / 1GB),2)
    }},
    @{Name="OnlineArchiveEnabled";Expression={ $_.ArchiveStatus -eq "Active" }},
    @{Name="ArchiveSizeGB";Expression={
        if ($_.ArchiveStatus -eq "Active") {
            $archiveStats = Get-EXOMailboxStatistics -Identity $_.UserPrincipalName -Archive
            if ($archiveStats.TotalItemSize) {
                [math]::Round(($archiveStats.TotalItemSize.Value.toBytes() / 1GB),2)
            } else {
                0
            }
        } else {
            $null
        }
    }}

# Vis i Out-GridView
Write-Host "$log Viser resultater i Out-HtmlView...`n" -ForegroundColor Gray
$result | Sort-Object MailboxSizeGB -Descending | Out-HtmlView -Title "Exchange Online Mailboxes" -HideFooter -PrettifyObject -PagingLength 300 -Style display, stripe

# Afbryd forbindelsen
Disconnect-ExchangeOnline -Confirm:$false