"""
Main window for Advanced MailTool with modern interface
"""

import sys
from pathlib import Path
from typing import List, Optional
import logging

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                              QSplitter, QMenuBar, QToolBar, QStatusBar, QLabel,
                              QFileDialog, QMessageBox, QProgressBar, QApplication)
from PySide6.QtCore import Qt, QSettings, QThread, Signal, QTimer
from PySide6.QtGui import QAction, QIcon, QFont

from .themes import theme_manager, Theme
from .widgets.email_list import EmailListWidget
from .widgets.email_viewer import EmailViewerWidget
from .widgets.progress_dialog import ProgressDialog
from ..engines.engine_manager import engine_manager
from ..engines.base_engine import ProgressCallback
from ..core.email_message import EmailMessage, EmailCollection


class LoadWorker(QThread):
    """Worker thread for loading email files"""
    
    progress_updated = Signal(str, int, int)  # message, current, total
    finished = Signal(object, str)  # collection, error_message
    
    def __init__(self, file_paths: List[Path]):
        super().__init__()
        self.file_paths = file_paths
    
    def run(self):
        try:
            # Create progress callback
            progress = ProgressCallback(self.progress_updated.emit)
            
            # Load files
            if len(self.file_paths) == 1:
                collection = engine_manager.load_file(self.file_paths[0], progress)
            else:
                collection = engine_manager.load_multiple_files(self.file_paths, progress)
            
            self.finished.emit(collection, "")
            
        except Exception as e:
            self.finished.emit(None, str(e))


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        
        # Setup logging
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Settings
        self.settings = QSettings("AdvancedMailTool", "AdvancedMailTool")
        
        # Current data
        self.current_collection = EmailCollection()
        self.current_files: List[Path] = []
        
        # Worker thread
        self.load_worker: Optional[LoadWorker] = None
        
        # Setup UI
        self.setup_ui()
        self.setup_connections()
        self.restore_settings()
        
        # Apply theme
        self.apply_current_theme()
        
        self.logger.info("Advanced MailTool initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        
        # Window properties
        self.setWindowTitle("📧 Advanced MailTool")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # Create splitter
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Email list widget
        self.email_list = EmailListWidget()
        self.email_list.setMinimumWidth(350)
        
        # Email viewer widget
        self.email_viewer = EmailViewerWidget()
        
        # Add to splitter
        self.splitter.addWidget(self.email_list)
        self.splitter.addWidget(self.email_viewer)
        self.splitter.setStretchFactor(0, 1)  # Email list
        self.splitter.setStretchFactor(1, 2)  # Email viewer (larger)
        
        layout.addWidget(self.splitter)
        
        # Setup menu bar
        self.setup_menu_bar()
        
        # Setup tool bar
        self.setup_tool_bar()
        
        # Setup status bar
        self.setup_status_bar()
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        # Open files
        open_action = QAction("📁 &Open Files...", self)
        open_action.setShortcut("Ctrl+O")
        open_action.setStatusTip("Open email files")
        open_action.triggered.connect(self.open_files)
        file_menu.addAction(open_action)

        # Close files
        self.close_action = QAction("❌ &Close Files", self)
        self.close_action.setShortcut("Ctrl+W")
        self.close_action.setStatusTip("Close currently loaded files")
        self.close_action.setEnabled(False)  # Disabled until files are loaded
        self.close_action.triggered.connect(self.close_files)
        file_menu.addAction(self.close_action)

        file_menu.addSeparator()
        
        # Export menu
        export_menu = file_menu.addMenu("📤 &Export")
        
        export_all_action = QAction("Export All Messages...", self)
        export_all_action.triggered.connect(self.export_all_messages)
        export_menu.addAction(export_all_action)
        
        export_selected_action = QAction("Export Selected Messages...", self)
        export_selected_action.triggered.connect(self.export_selected_messages)
        export_menu.addAction(export_selected_action)
        
        export_attachments_action = QAction("Export All Attachments...", self)
        export_attachments_action.triggered.connect(self.export_all_attachments)
        export_menu.addAction(export_attachments_action)
        
        file_menu.addSeparator()
        
        # Exit
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu("&View")
        
        # Theme submenu
        theme_menu = view_menu.addMenu("🎨 &Theme")
        
        light_action = QAction("☀️ Light Theme", self)
        light_action.setCheckable(True)
        light_action.triggered.connect(lambda: self.set_theme(Theme.LIGHT))
        theme_menu.addAction(light_action)
        
        dark_action = QAction("🌙 Dark Theme", self)
        dark_action.setCheckable(True)
        dark_action.triggered.connect(lambda: self.set_theme(Theme.DARK))
        theme_menu.addAction(dark_action)
        
        # Store theme actions
        self.theme_actions = {
            Theme.LIGHT: light_action,
            Theme.DARK: dark_action
        }
        
        view_menu.addSeparator()
        
        # Refresh
        refresh_action = QAction("🔄 &Refresh", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_view)
        view_menu.addAction(refresh_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("&Tools")
        
        search_action = QAction("🔍 &Search...", self)
        search_action.setShortcut("Ctrl+F")
        search_action.triggered.connect(self.show_search)
        tools_menu.addAction(search_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_tool_bar(self):
        """Setup tool bar"""
        
        toolbar = self.addToolBar("Main")
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        
        # Open files
        open_action = QAction("📁 Open Files", self)
        open_action.setStatusTip("Open email files")
        open_action.triggered.connect(self.open_files)
        toolbar.addAction(open_action)

        # Close files
        self.close_toolbar_action = QAction("❌ Close Files", self)
        self.close_toolbar_action.setStatusTip("Close currently loaded files")
        self.close_toolbar_action.setEnabled(False)  # Disabled until files are loaded
        self.close_toolbar_action.triggered.connect(self.close_files)
        toolbar.addAction(self.close_toolbar_action)

        toolbar.addSeparator()
        
        # Export
        export_action = QAction("📤 Export", self)
        export_action.setStatusTip("Export messages")
        export_action.triggered.connect(self.export_all_messages)
        toolbar.addAction(export_action)
        
        toolbar.addSeparator()
        
        # Search
        search_action = QAction("🔍 Search", self)
        search_action.setStatusTip("Search messages")
        search_action.triggered.connect(self.show_search)
        toolbar.addAction(search_action)
        
        toolbar.addSeparator()
        
        # Theme toggle
        theme_action = QAction("🎨 Toggle Theme", self)
        theme_action.setStatusTip("Switch between light and dark theme")
        theme_action.triggered.connect(self.toggle_theme)
        toolbar.addAction(theme_action)
    
    def setup_status_bar(self):
        """Setup status bar"""
        
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label)
        
        # Progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # Message count
        self.message_count_label = QLabel("0 messages")
        self.status_bar.addPermanentWidget(self.message_count_label)
    
    def setup_connections(self):
        """Setup signal connections"""
        
        # Email list selection
        self.email_list.email_selected.connect(self.email_viewer.display_email)
        self.email_list.selection_changed.connect(self.update_status)
    
    def open_files(self):
        """Open email files dialog"""
        
        # Get supported formats
        formats = engine_manager.get_supported_formats()
        extensions = formats['import']
        
        # Create filter string
        filter_parts = []
        filter_parts.append("All Supported Files (*" + " *".join(extensions) + ")")
        filter_parts.append("MBOX Files (*.mbox *.mbx)")
        filter_parts.append("PST Files (*.pst)")
        filter_parts.append("EML Files (*.eml)")
        filter_parts.append("MSG Files (*.msg)")
        filter_parts.append("All Files (*.*)")
        
        filter_string = ";;".join(filter_parts)
        
        # Open file dialog
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "Open Email Files",
            "",
            filter_string
        )
        
        if files:
            file_paths = [Path(f) for f in files]
            self.load_files(file_paths)

    def close_files(self):
        """Close currently loaded files"""
        self.email_list.clear()
        self.email_viewer.show_welcome_message()
        self.current_collection = None

        # Disable close actions
        if hasattr(self, 'close_action'):
            self.close_action.setEnabled(False)
        if hasattr(self, 'close_toolbar_action'):
            self.close_toolbar_action.setEnabled(False)

        self.status_bar.showMessage("Files closed", 2000)

    def close_files(self):
        """Close currently loaded files"""

        # Ask for confirmation if there are loaded messages
        if hasattr(self, 'current_collection') and self.current_collection and len(self.current_collection) > 0:
            reply = QMessageBox.question(
                self,
                "Close Files",
                f"Close {len(self.current_collection)} loaded messages?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

        # Clear all data
        self.current_collection = None

        # Clear email list
        self.email_list.clear()

        # Clear email viewer
        self.email_viewer.show_welcome_message()

        # Disable close actions
        self.close_action.setEnabled(False)
        self.close_toolbar_action.setEnabled(False)

        # Update status
        self.status_bar.showMessage("Files closed", 2000)

        # Update window title
        self.setWindowTitle("Advanced MailTool")

    def load_files(self, file_paths: List[Path]):
        """Load email files"""
        
        if not file_paths:
            return
        
        # Validate files
        validation = engine_manager.validate_files(file_paths)
        
        if validation['invalid']:
            invalid_names = [f.name for f in validation['invalid']]
            QMessageBox.warning(
                self,
                "Invalid Files",
                f"The following files could not be read:\n\n" + "\n".join(invalid_names)
            )
        
        if validation['unsupported']:
            unsupported_names = [f.name for f in validation['unsupported']]
            QMessageBox.warning(
                self,
                "Unsupported Files",
                f"The following file formats are not supported:\n\n" + "\n".join(unsupported_names)
            )
        
        valid_files = validation['valid']
        if not valid_files:
            return
        
        # Start loading
        self.current_files = valid_files
        self.start_loading(valid_files)
    
    def start_loading(self, file_paths: List[Path]):
        """Start loading files in background thread"""
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
        self.status_label.setText("Loading files...")
        
        # Create and start worker
        self.load_worker = LoadWorker(file_paths)
        self.load_worker.progress_updated.connect(self.update_progress)
        self.load_worker.finished.connect(self.on_loading_finished)
        self.load_worker.start()
    
    def update_progress(self, message: str, current: int, total: int):
        """Update progress display"""
        
        self.status_label.setText(message)
        
        if total > 0:
            self.progress_bar.setRange(0, total)
            self.progress_bar.setValue(current)
        else:
            self.progress_bar.setRange(0, 0)  # Indeterminate
    
    def on_loading_finished(self, collection: EmailCollection, error: str):
        """Handle loading completion"""
        
        # Hide progress
        self.progress_bar.setVisible(False)
        
        if error:
            QMessageBox.critical(self, "Loading Error", f"Failed to load files:\n\n{error}")
            self.status_label.setText("Loading failed")
            return
        
        if not collection or len(collection) == 0:
            QMessageBox.information(self, "No Messages", "No email messages found in the selected files.")
            self.status_label.setText("No messages found")
            return
        
        # Update UI with loaded messages
        self.current_collection = collection
        self.email_list.set_messages(collection)

        # Enable close action
        if hasattr(self, 'close_action'):
            self.close_action.setEnabled(True)
        if hasattr(self, 'close_toolbar_action'):
            self.close_toolbar_action.setEnabled(True)

        # Update status
        self.update_status()
        
        file_names = [f.name for f in self.current_files]
        self.status_label.setText(f"Loaded from: {', '.join(file_names)}")
        
        self.logger.info(f"Successfully loaded {len(collection)} messages from {len(self.current_files)} files")
    
    def update_status(self):
        """Update status bar with current information"""

        if not hasattr(self, 'current_collection') or not self.current_collection:
            self.message_count_label.setText("No messages")
            return

        try:
            total_messages = len(self.current_collection)
            selected_count = len(self.email_list.get_selected_messages())
        except (TypeError, AttributeError):
            self.message_count_label.setText("No messages")
            return
        
        if selected_count > 0:
            self.message_count_label.setText(f"{selected_count} of {total_messages} messages")
        else:
            self.message_count_label.setText(f"{total_messages} messages")
    
    def export_all_messages(self):
        """Export all messages"""
        if len(self.current_collection) == 0:
            QMessageBox.information(self, "No Messages", "No messages to export.")
            return
        
        self.show_export_dialog(self.current_collection.messages)
    
    def export_selected_messages(self):
        """Export selected messages"""
        selected = self.email_list.get_selected_messages()
        if not selected:
            QMessageBox.information(self, "No Selection", "No messages selected.")
            return
        
        self.show_export_dialog(selected)
    
    def export_all_attachments(self):
        """Export all attachments"""
        if len(self.current_collection) == 0:
            QMessageBox.information(self, "No Messages", "No messages loaded.")
            return

        # Count messages with attachments
        messages_with_attachments = [msg for msg in self.current_collection if msg.has_attachments]

        if not messages_with_attachments:
            QMessageBox.information(self, "No Attachments", "No attachments found in loaded messages.")
            return

        # Get directory
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Directory for Attachments",
            ""
        )

        if not directory:
            return

        self.export_attachments_to_directory(messages_with_attachments, directory)
    
    def show_export_dialog(self, messages):
        """Show export dialog"""
        from ..exporters.export_dialog import ExportDialog

        dialog = ExportDialog(messages, self)
        dialog.exec()

    def export_attachments_to_directory(self, messages, directory):
        """Export attachments from messages to directory"""

        from pathlib import Path

        total_attachments = sum(len(msg.attachments) for msg in messages)
        saved_count = 0

        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, total_attachments)

        processed = 0

        for msg in messages:
            for attachment in msg.attachments:
                processed += 1
                self.progress_bar.setValue(processed)
                self.status_label.setText(f"Saving attachment {processed}/{total_attachments}...")

                if attachment.data and attachment.filename:
                    try:
                        # Create safe filename
                        safe_filename = self.make_safe_filename(attachment.filename)
                        file_path = Path(directory) / safe_filename

                        # Handle duplicates
                        counter = 1
                        original_path = file_path
                        while file_path.exists():
                            stem = original_path.stem
                            suffix = original_path.suffix
                            file_path = original_path.parent / f"{stem}_{counter}{suffix}"
                            counter += 1

                        if attachment.save_to_file(file_path):
                            saved_count += 1
                    except Exception as e:
                        self.logger.warning(f"Failed to save attachment {attachment.filename}: {e}")
                        continue

                # Process events to keep UI responsive
                QApplication.processEvents()

        # Hide progress
        self.progress_bar.setVisible(False)

        # Show result
        if saved_count > 0:
            QMessageBox.information(
                self,
                "Attachments Exported",
                f"Successfully exported {saved_count} of {total_attachments} attachments to:\n{directory}"
            )
            self.status_label.setText(f"Exported {saved_count} attachments")
        else:
            QMessageBox.warning(
                self,
                "Export Failed",
                "No attachments could be exported."
            )
            self.status_label.setText("Attachment export failed")

    def make_safe_filename(self, filename: str) -> str:
        """Create safe filename"""
        import re

        # Remove/replace unsafe characters
        safe = re.sub(r'[<>:"/\\|?*]', '_', filename)
        safe = safe.strip()

        return safe if safe else "attachment"
    
    def show_search(self):
        """Show search dialog"""
        if len(self.current_collection) == 0:
            QMessageBox.information(self, "No Messages", "No messages loaded to search.")
            return

        from .widgets.search_dialog import SearchDialog

        dialog = SearchDialog(self.current_collection, self)
        dialog.search_requested.connect(self.perform_search)
        dialog.exec()

    def perform_search(self, criteria: dict):
        """Perform search with given criteria"""

        matching_messages = []

        for message in self.current_collection:
            if self.message_matches_criteria(message, criteria):
                matching_messages.append(message)

        # Create filtered collection
        filtered_collection = EmailCollection()
        filtered_collection.add_messages(matching_messages)

        # Update email list with search results
        self.email_list.set_messages(filtered_collection)

        # Update status
        total = len(self.current_collection)
        found = len(matching_messages)
        self.status_label.setText(f"Search results: {found} of {total} messages")

        self.logger.info(f"Search completed: {found} messages found")

    def message_matches_criteria(self, message: EmailMessage, criteria: dict) -> bool:
        """Check if message matches search criteria"""

        # Quick search
        if 'quick_search' in criteria:
            query = criteria['quick_search'].lower()
            if not (query in message.subject.lower() or
                    query in message.sender.lower() or
                    query in message.body_text.lower() or
                    query in message.body_html.lower()):
                return False

        # Subject
        if 'subject' in criteria:
            if criteria['subject'].lower() not in message.subject.lower():
                return False

        # Sender
        if 'sender' in criteria:
            if criteria['sender'].lower() not in message.sender.lower():
                return False

        # Recipient
        if 'recipient' in criteria:
            recipient_match = False
            for recipient in message.recipients:
                if criteria['recipient'].lower() in recipient.lower():
                    recipient_match = True
                    break
            if not recipient_match:
                return False

        # Content
        if 'content' in criteria:
            content_query = criteria['content'].lower()
            if not (content_query in message.body_text.lower() or
                    content_query in message.body_html.lower()):
                return False

        # Filters
        if criteria.get('has_attachments') and not message.has_attachments:
            return False

        if criteria.get('unread_only') and message.is_read:
            return False

        if criteria.get('flagged_only') and not message.is_flagged:
            return False

        # Size range
        if 'size_min' in criteria and message.size < criteria['size_min']:
            return False

        if 'size_max' in criteria and message.size > criteria['size_max']:
            return False

        # Date range
        if 'date_from' in criteria and message.date:
            if message.date < criteria['date_from']:
                return False

        if 'date_to' in criteria and message.date:
            if message.date > criteria['date_to']:
                return False

        return True
    
    def refresh_view(self):
        """Refresh the current view"""
        if self.current_files:
            self.load_files(self.current_files)
    
    def set_theme(self, theme: Theme):
        """Set application theme"""
        theme_manager.set_theme(theme)
        self.apply_current_theme()
        self.update_theme_actions()
        
        # Save theme preference
        self.settings.setValue("theme", theme.value)
        
        self.status_label.setText(f"Theme changed to {theme.value}")
        QTimer.singleShot(2000, lambda: self.status_label.setText("Ready"))
    
    def toggle_theme(self):
        """Toggle between light and dark theme"""
        new_theme = theme_manager.toggle_theme()
        self.apply_current_theme()
        self.update_theme_actions()
        
        # Save theme preference
        self.settings.setValue("theme", new_theme.value)
    
    def apply_current_theme(self):
        """Apply current theme to application"""
        stylesheet = theme_manager.get_stylesheet()
        QApplication.instance().setStyleSheet(stylesheet)
    
    def update_theme_actions(self):
        """Update theme action checkboxes"""
        current_theme = theme_manager.current_theme
        for theme, action in self.theme_actions.items():
            action.setChecked(theme == current_theme)
    
    def show_about(self):
        """Show about dialog"""
        
        engine_status = engine_manager.get_engine_status()
        engine_info = "\n".join([f"• {e['name']}" for e in engine_status['engines']])
        
        QMessageBox.about(
            self,
            "About Advanced MailTool",
            f"""
<h3>📧 Advanced MailTool</h3>
<p><b>Version:</b> 2.0</p>
<p><b>A modern email viewer and processor</b></p>

<p><b>Features:</b></p>
<ul>
<li>📁 Multi-format support (MBOX, PST, EML, MSG)</li>
<li>📤 Export to multiple formats</li>
<li>📎 Attachment handling</li>
<li>🎨 Modern dark/light themes</li>
<li>🔍 Advanced search and filtering</li>
</ul>

<p><b>Supported Engines:</b></p>
<pre>{engine_info}</pre>

<p><b>Current Messages:</b> {len(self.current_collection)}</p>
<p><b>Theme:</b> {theme_manager.current_theme.value.title()}</p>

<p><i>Built with PySide6 and Python</i></p>
            """.strip()
        )
    
    def restore_settings(self):
        """Restore application settings"""
        
        # Window geometry
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        # Splitter state
        splitter_state = self.settings.value("splitter_state")
        if splitter_state:
            self.splitter.restoreState(splitter_state)
        
        # Theme
        theme_name = self.settings.value("theme", Theme.LIGHT.value)
        try:
            theme = Theme(theme_name)
            theme_manager.set_theme(theme)
            self.update_theme_actions()
        except ValueError:
            pass  # Use default theme
    
    def save_settings(self):
        """Save application settings"""
        
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("splitter_state", self.splitter.saveState())
        self.settings.setValue("theme", theme_manager.current_theme.value)
    
    def closeEvent(self, event):
        """Handle window close event"""
        
        # Stop any running worker
        if self.load_worker and self.load_worker.isRunning():
            self.load_worker.terminate()
            self.load_worker.wait(3000)  # Wait up to 3 seconds
        
        # Save settings
        self.save_settings()
        
        self.logger.info("Advanced MailTool closing")
        event.accept()
