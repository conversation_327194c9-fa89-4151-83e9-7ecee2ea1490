"""
    Script til automatisk at hente 5 vegetariske forslag til verdensretter fra Gemini AI,
    med mindst én asiatisk ret, og sende dem som en pæn Markdown-notifikation via ntfy.

    Scriptet holder styr på tidligere foreslåede retter i en historikfil (retter_gemini_history.json)
    og beder Gemini undgå at foreslå retter, der allerede har været brugt inden for de seneste 30 dage.
    Historikken gemmes i samme mappe som scriptet.

    - Gemini prompten udvides automatisk med listen over retter, der skal undgås.
    - Svaret sendes til ntfy og printes i konsollen.
    - Fejl håndteres og sendes også til ntfy.

    Forudsætter:
    - Python 3.x
    - requests-modul

    Af: <PERSON> / Assistent: Github Copilot
    Version: 1.0
    Dato: 19. juni 2025
"""

import requests
import json
import os
from datetime import datetime, timedelta
import re

# Config
NTFY_URL = "https://ntfy.duksekkan.nohost.me"
NTFY_TOPIC = "LLMl9s8hdvOq6BCP"
NTFY_TOKEN = "Bearer tk_n3jhq9ds16la4c3lm6ng4e086aibl"

ntfy_headers = {
    "Authorization": NTFY_TOKEN,
    "Content-Type": "text/plain; charset=utf-8",
    "Content-Disposition": "inline",
    "Markdown": "yes"
}

# Gemini API endpoint og nøgle
GEMINI_API_KEY = "AIzaSyB-8Mfd6JTKcX87DDy_tdHE5EJ4ufgDHrQ"
url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" + GEMINI_API_KEY
headers = {"Content-Type": "application/json"}

HISTORY_FILE = os.path.join(os.path.dirname(__file__), "retter_gemini_history.json")

def send_ntfy_message(message):
    try:
        requests.post(
            f"{NTFY_URL}/{NTFY_TOPIC}",
            data=message.encode("utf-8"),
            headers=ntfy_headers,
            timeout=10
        )
    except Exception as e:
        print("Kunne ikke sende fejlbesked til ntfy:", e)

def load_history():
    if os.path.exists(HISTORY_FILE):
        with open(HISTORY_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    return []

def save_history(history):
    with open(HISTORY_FILE, "w", encoding="utf-8") as f:
        json.dump(history, f, ensure_ascii=False, indent=2)

def extract_dish_names(answer):
    # Matcher linjer som: 1. 🇹🇭 Pad Thai: ...
    return [re.sub(r"^\d+\.\s*[\w\W]{2,4}\s*", "", line.split(":")[0]).strip()
            for line in answer.splitlines() if re.match(r"^\d+\.", line)]

def dishes_used_last_30_days(history):
    cutoff = datetime.now() - timedelta(days=30)
    dishes = set()
    for entry in history:
        if datetime.fromisoformat(entry["date"]) > cutoff:
            dishes.update(entry["dishes"])
    return dishes

def make_prompt(used_dishes):
    prompt = (
        "Kom med 5 forslag til 5 forskellige retter jeg kan prøve. Det skal være:\n"
        "Verdensmad\n"
        "Vegetarisk\n"
        "Minimum 1 opskrift fra Asien.\n"
        "Inkluder en kort beskrivelse.\n"
        "Opstil svaret let læseligt med flag emojis som indikerer landets ophav.\n"
        "Skriv på dansk.\n"
        "Forslagene skal være forskellige og ikke indeholde gentagelser.\n"
        "Forslagene skal være i Markdown format, så de kan vises pænt i ntfy.\n"
        "Skriv ikke noget andet end forslagene.\n"
        "Opstil det med en overskrift '** Forslag til retter **' og brug en liste.\n"
        "Nummer forslagene.\n"
    )
    if used_dishes:
        prompt += (
            "\nUndgå disse retter, da de allerede har været foreslået inden for den sidste måned:\n"
            + ", ".join(used_dishes) + "."
        )
    return prompt

try:
    history = load_history()
    used_dishes = dishes_used_last_30_days(history)

    body = {
        "contents": [
            {
                "parts": [
                    {
                        "text": make_prompt(used_dishes)
                    }
                ]
            }
        ]
    }

    response = requests.post(url, headers=headers, json=body, timeout=20)
    response.raise_for_status()
    data = response.json()

    answer = data["candidates"][0]["content"]["parts"][0]["text"]
    dishes = extract_dish_names(answer)

    # Gem i historik
    history.append({"date": datetime.now().isoformat(), "dishes": dishes})
    save_history(history)

    send_ntfy_message(answer)
    print("Svar sendt til ntfy:", answer)

except Exception as e:
    error_message = f"Fejl i Gemini AI scriptet (forslag til retter): {e}"
    send_ntfy_message(error_message)