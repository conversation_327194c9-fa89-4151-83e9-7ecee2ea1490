## Find All Inbox Rules that Forwards Emails Externally in Office 365 using PowerShell

This PowerShell script helps to find inbox rules configured with external email forwarding (forwards emails to external users) and export them to CSV file.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Inbox Rules with External Forwarding Report](https://o365reports.com/wp-content/uploads/2022/06/Find-O365-inbox-rules-with-external-forwarding-1.png?v=1705576514)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive inbox rules with external forwarding report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10664/1/20>*  



