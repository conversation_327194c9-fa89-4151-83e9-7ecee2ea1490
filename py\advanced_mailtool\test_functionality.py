#!/usr/bin/env python3
"""
Test script for Advanced MailTool functionality
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.engines.engine_manager import engine_manager
from src.core.email_message import EmailMessage, EmailCollection


def test_engine_status():
    """Test engine status and availability"""
    print("🔧 Testing Engine Status...")
    
    status = engine_manager.get_engine_status()
    
    print(f"✅ Total engines: {status['total_engines']}")
    print(f"✅ Supported extensions: {', '.join(status['supported_extensions'])}")
    
    for engine in status['engines']:
        print(f"   • {engine['name']} - Extensions: {', '.join(engine['extensions'])}")
    
    return True


def test_mbox_loading():
    """Test MBOX file loading"""
    print("\n📧 Testing MBOX Loading...")
    
    test_file = Path("test_emails.mbox")
    if not test_file.exists():
        print("❌ Test MBOX file not found")
        return False
    
    try:
        collection = engine_manager.load_file(test_file)
        
        print(f"✅ Loaded {len(collection)} messages from MBOX")
        
        if len(collection) > 0:
            first_message = collection[0]
            print(f"   First message: {first_message.subject}")
            print(f"   From: {first_message.sender}")
            print(f"   Attachments: {first_message.attachment_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ MBOX loading failed: {e}")
        return False


def test_search_functionality():
    """Test search functionality"""
    print("\n🔍 Testing Search Functionality...")
    
    test_file = Path("test_emails.mbox")
    if not test_file.exists():
        print("❌ Test MBOX file not found")
        return False
    
    try:
        collection = engine_manager.load_file(test_file)
        
        # Test search
        search_results = collection.search("newsletter")
        print(f"✅ Search for 'newsletter': {len(search_results)} results")
        
        # Test filter by attachments
        with_attachments = collection.filter_with_attachments()
        print(f"✅ Messages with attachments: {len(with_attachments)}")
        
        # Test senders
        senders = collection.get_senders()
        print(f"✅ Unique senders: {len(senders)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Search testing failed: {e}")
        return False


def test_export_functionality():
    """Test export functionality"""
    print("\n📤 Testing Export Functionality...")
    
    test_file = Path("test_emails.mbox")
    if not test_file.exists():
        print("❌ Test MBOX file not found")
        return False
    
    try:
        collection = engine_manager.load_file(test_file)
        
        # Test EML export
        export_dir = Path("test_export")
        export_dir.mkdir(exist_ok=True)
        
        eml_path = export_dir / "test_export.eml"
        
        # Create small collection for testing
        test_collection = EmailCollection()
        if len(collection) > 0:
            test_collection.add_message(collection[0])
        
        success = engine_manager.save_file(test_collection, eml_path, "eml")
        
        if success and eml_path.exists():
            print(f"✅ EML export successful: {eml_path}")
            
            # Clean up
            eml_path.unlink()
            export_dir.rmdir()
        else:
            print("❌ EML export failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Export testing failed: {e}")
        return False


def test_email_message_functionality():
    """Test EmailMessage class functionality"""
    print("\n📨 Testing EmailMessage Functionality...")
    
    try:
        # Create test message
        message = EmailMessage(
            subject="Test Message",
            sender="<EMAIL>",
            sender_name="Test User",
            recipients=["<EMAIL>"],
            body_text="This is a test message",
            date="2024-01-01 12:00:00"
        )
        
        print(f"✅ Created message: {message.subject}")
        print(f"✅ Sender display: {message.sender_display}")
        print(f"✅ Date display: {message.date_display}")
        print(f"✅ Has attachments: {message.has_attachments}")
        
        # Test to_dict
        message_dict = message.to_dict()
        print(f"✅ Serialization: {len(message_dict)} fields")
        
        return True
        
    except Exception as e:
        print(f"❌ EmailMessage testing failed: {e}")
        return False


def test_file_validation():
    """Test file validation"""
    print("\n📋 Testing File Validation...")
    
    try:
        test_files = [
            Path("test_emails.mbox"),
            Path("nonexistent.mbox"),
            Path("README.md")  # Wrong format
        ]
        
        validation = engine_manager.validate_files(test_files)
        
        print(f"✅ Valid files: {len(validation['valid'])}")
        print(f"✅ Invalid files: {len(validation['invalid'])}")
        print(f"✅ Unsupported files: {len(validation['unsupported'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ File validation testing failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Advanced MailTool Tests...\n")
    
    tests = [
        ("Engine Status", test_engine_status),
        ("MBOX Loading", test_mbox_loading),
        ("Search Functionality", test_search_functionality),
        ("Export Functionality", test_export_functionality),
        ("EmailMessage Class", test_email_message_functionality),
        ("File Validation", test_file_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS:")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Advanced MailTool is ready to use.")
        print("\nTo start the application:")
        print("  python main.py")
        return 0
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
