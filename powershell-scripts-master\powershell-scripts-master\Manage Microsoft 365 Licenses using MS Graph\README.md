## Manage Microsoft 365 Licenses using MS Graph PowerShell
This All-in-One PowerShell script helps admins to manage Microsoft 365 licenses effectively with 11 reporting and license assignment removal actions.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Microsoft 365 Licensed Users Report.](https://o365reports.com/wp-content/uploads/2022/09/All-in-One-PowerShell-Script-for-Microsoft-365-License-Management-Reporting-1080x567.png)

## Free Microsoft 365 License Reporting Tool by AdminDroid 

Explore [AdminDroid's Microsoft 365 reporting](https://admindroid.com/?src=GitHub) tool for comprehensive insights with 20+ license reports, ideal for detailed license analysis and enhanced understanding of your M365 environment.

*Effortlessly manage Microsoft 365 licenses reports using  AdminDroid: <https://demo.admindroid.com/#/1/11/reports/8/1/20>*
