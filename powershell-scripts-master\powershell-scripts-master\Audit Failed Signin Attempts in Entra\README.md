## How to Get All Failed Login Attempts in Microsoft 365

Export all Microsoft 365 failed login attempts with PowerShell script to identify unauthorized access attempts and address potential security risks before they escalate.

***Sample Output:***

The exported Entra ID failed logins report looks like the screenshot below.

![Microsoft 365 Failed Login Attempts Report](<https://o365reports.com/wp-content/uploads/2025/05/failed-sign-in-report-1.png?v=1747116987>)

## Microsoft 365 Reporting tool by AdminDroid

Get clear visibility into Microsoft 365 sign-in activities and spot risks early with [AdminDroid's free Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering over 120+ free reports and 10+ dashboards.

*Uncover sign-in vulnerabilities with AdminDroid: [https://demo.admindroid.com/#/1/11/reports/20163/1/20](https://demo.admindroid.com/#/1/11/reports/20163/1/20)*