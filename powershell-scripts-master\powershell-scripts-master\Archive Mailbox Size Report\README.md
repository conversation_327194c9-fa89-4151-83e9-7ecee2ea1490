## Export Office 365 Archive Mailbox Size Using PowerShell
The PowerShell script exports Office 365 archive mailbox size report to CSV file along with archive quota, archive status, archive name, unlimited archiving.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Archive Mailbox Size Report](https://o365reports.com/wp-content/uploads/2021/03/Office-365-Archive-mailbox-size-report-768x142.png?v=1705576740)

## Microsoft 365 Reporting Tool by AdminDroid

For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*Get Archive Mailboxes Reports in a Simple Way with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10609/1/20>*


