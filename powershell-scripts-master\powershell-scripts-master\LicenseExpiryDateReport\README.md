## Export Office 365 License Expiry Date Report to CSV using PowerShell

This PowerShell script exports Office 365 license expiry date with subscription plan, subscribed date, type, subscription expiry date and status to CSV file.

***Sample Output:***

The script exports Office 365 license expiry dates as an output CSV file that looks like the screenshot below.

![Office 365 License Expiry Date Report](https://o365reports.com/wp-content/uploads/2020/03/Office-365-license-expiry-date-powershell-1.png?v=**********)

## Microsoft 365 Reporting tool by AdminDroid

Take your Office 365 license expiry management to the next level with the [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1800+ pre-built reports and dashboards for in-depth analysis and efficient oversight.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/14/1/20>*




