<#
.SYNOPSIS
    Synkroniserer kontakter fra en Emply til Microsoft Graph.

.DESCRIPTION
    Dette script synkroniserer kontakter fra Emply til Microsoft Graph.
    Det opretter nye kontakter, hvis de ikke allerede findes, og sletter kontakter, der ikke længere er aktive i Emply.

.PARAMETER UserId
    E-mailadressen på den bruger, hvis kontakter skal synkroniseres.

.PARAMETER WhatIf
    Hvis angivet, viser scriptet kun, hvad der ville ske, uden at foretage ændringer.

.EXAMPLE
    .\Sync-ApGlobalContact.ps1 -UserId "<EMAIL>"

.EXAMPLE
    .\Sync-ApGlobalContact.ps1 -UserId "<EMAIL>" -WhatIf
    Kører scriptet i WhatIf-tilstand, hvor det kun viser, hvad der ville ske, uden at foretage ændringer.

.NOTES
    App registration: 
    - Name: Sync AP Contact Info
    - API permissions: Contacts.ReadWrite, Contacts.ReadWrite.Shared
#>
param (
    [Parameter(Mandatory = $false)]
    [string]$UserId = "<EMAIL>",

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)
# Hent info fra Emply
function Get-UserInfoFromEmply {
    # Henter brugere fra Emply API
    $apiKey              = Get-Secret -Name "EmplyApiKey" -AsPlainText
    $response            = Invoke-RestMethod -Uri "https://api.emply.com/v1/andersen-partners/users?Active=true&apiKey=$apiKey"
    $ekskluderedeBrugere = @("<EMAIL>", "<EMAIL>")

    function Format-PhoneNumber {
        param($number)
        if (-not $number) { return $null }
        $digits = ($number -replace '[^\d]', '')
        if ($digits.Length -eq 8) {
            return ($digits -replace '(\d{2})(\d{2})(\d{2})(\d{2})', '$1 $2 $3 $4')
        }
        elseif ($digits.Length -eq 10 -and $digits.StartsWith('45')) {
            $digits = $digits.Substring(2)
            return ($digits -replace '(\d{2})(\d{2})(\d{2})(\d{2})', '$1 $2 $3 $4')
        }
        else {
            return $number
        }
    }

    function Set-ContactPhones {
        param(
            $mobilePhone,
            $officePhone
        )
        $mobile = Format-PhoneNumber $mobilePhone
        $office = Format-PhoneNumber $officePhone
        $fax = $null

        $officeDigits = ($officePhone -replace '[^\d]', '')

        # Matcher 762222XX eller 762222XXX (8 eller 9 cifre), evt. med landekode. + laver $fax hvis nummere starter med 762222
        if ($officeDigits -match '^(?:45)?762222(\d{2,3})$') {
            $fax = $officeDigits.Substring($officeDigits.Length - 3)
            $office = Format-PhoneNumber($officeDigits)
        }

        # Hvis mobil og office er ens, brug kun mobil
        if ($mobile -and $office -and $mobile -eq $office) {
            $office = $null
        }

        $phones = @()
        if ($office) { $phones += $office }
        if ($fax)    { $phones += $fax }

        return @{
            mobilePhone    = $mobile
            businessPhones = $phones
        }
    }

    $contacts = $response | Where-Object {
        ($_.mobilePhone -or $_.officePhone)
    } | Where-Object {
        $normalized = ($_.officePhone -replace '[^\d]', '')
        -not ($normalized -match '^45?76222222$')
    } | Where-Object {
        $ekskluderedeBrugere -notcontains $_.email
    } | ForEach-Object {
        $phones = Set-ContactPhones $_.mobilePhone $_.officePhone

        @{
            givenName      = $_.firstName
            surname        = $_.lastName
            displayName    = "$($_.firstName) $($_.lastName)"
            FileAs         = "$($_.lastName), $($_.firstName)"
            emailAddresses = @(@{ address = $_.email; name = "$($_.firstName) $($_.lastName)" })
            businessPhones = $phones.businessPhones
            mobilePhone    = $phones.mobilePhone
            companyName    = "Andersen Partners"
        }
    }
    return $contacts
}
function Write-Log {
    # Funktion til at logge meddelelser
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet("Info", "Warning", "Error", "Success")]
        [string]$Level = "Info"
    )
    

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "Info"    { Write-Host $logMessage -ForegroundColor Gray }
        "Success" { Write-Host $logMessage -ForegroundColor Green }
        "Warning" { Write-Host $logMessage -ForegroundColor Yellow }
        "Error"   { Write-Host $logMessage -ForegroundColor Red }
    }
}
function Build-ContactBody {
    # Funktion til at bygge kontaktens krop, fra Emply data til Microsoft Graph format.
    param (
        $contact
    )
    $body = @{}

    if ($contact.givenName)   { $body.givenName   = $contact.givenName }
    if ($contact.surname)     { $body.surname     = $contact.surname }
    if ($contact.displayName) { $body.displayName = $contact.displayName }
    if ($contact.FileAs)      { $body.FileAs      = $contact.FileAs }
    if ($contact.companyName) { $body.companyName = $contact.companyName }

    # businessPhones: array af strenge
    $phones = @()
    if ($contact.businessPhones) {
        if ($contact.businessPhones -is [array]) {
            $phones += $contact.businessPhones | Where-Object { $_ }
        } else {
            $phones += $contact.businessPhones
        }
    }
    if ($phones.Count -gt 0) {
        $body.businessPhones = $phones
    }

    # mobilePhone: kun én streng
    if ($contact.mobilePhone) {
        if ($contact.mobilePhone -is [array]) {
            $body.mobilePhone = $contact.mobilePhone[0]
        } else {
            $body.mobilePhone = $contact.mobilePhone
        }
    }

    # emailAddresses: array af hashtabeller
    if ($contact.emailAddresses -and $contact.emailAddresses[0].address) {
        $body.emailAddresses = @(
            @{
                address = $contact.emailAddresses[0].address
                name    = $contact.emailAddresses[0].name
            }
        )
    }

    return $body
}

try {
    # Forbind til Microsoft Graph
     if (!(Get-MgContext)) {
        $tenantId              = "3b8541d2-0a26-410f-99e7-eeb45b41de6b"
        $clientId              = Get-Secret -Name "MgGraphClientID" -AsPlainText
        $certificateThumbprint = Get-Secret -Name "MgGraphThrumbprint" -AsPlainText
        Write-Log "Forbinder til Microsoft Graph..."
        Connect-MgGraph -ClientId $clientId -TenantId $tenantId -CertificateThumbprint $certificateThumbprint > $null
    }
    Write-Log "Forbundet til Microsoft Graph." -Level "Success"

    # Hent eksisterende kontakter
    Write-Log "Henter eksisterende kontakter for $UserId..."
    $existingContacts = Get-MgUserContact -UserId $UserId -All
    Write-Log "Fandt $($existingContacts.Count) eksisterende kontakter."

    # Indlæs kontakter fra Emply
    $contacts = Get-UserInfoFromEmply
    Write-Log "Indlaeste $($contacts.Count) kontakter fra Emply."

    # Opret nye kontakter
    $createdCount  = 0
    $skippedCount  = 0
    $totalContacts = $contacts.Count
    
    Write-Log "Starter oprettelse af nye kontakter..."
    foreach ($contact in $contacts) {
        $email = $contact.emailAddresses[0].address
        $progressPercent = [math]::Round(($createdCount + $skippedCount) / $totalContacts * 100)
        Write-Progress -Activity "Synkroniserer kontakter" -Status "Behandler $($contact.displayName)" -PercentComplete $progressPercent

        if ($existingContacts | Where-Object { $_.EmailAddresses[0].Address -eq $email }) {
            #Write-Log "Springer over eksisterende kontakt: $($contact.displayName) med email: $email" -Level "Info"
            $skippedCount++
            continue
        }

        # Konverter til hashtable for at sikre korrekt format
        $body = Build-ContactBody $contact
        try {
            Write-Log "Opretter kontakt: $($contact.displayName) med email: $email"
            # Opret kontakt
            if ($WhatIf) {
                Write-Log "WhatIf: Ville oprette kontakt: $($contact.displayName)" -Level "Warning"
            } else {
                #Write-Host "BODY: $($body | ConvertTo-Json -Compress)"
                New-MgUserContact -UserId $UserId -BodyParameter $body > $null
            }
            $createdCount++
        } catch {
            Write-Log "Fejl ved oprettelse af kontakt $($contact.displayName): $_" -Level "Error"
        }
    }
    Write-Log "Synkronisering af kontakter completed" -Level "Success"

    # Slet kontakter som ikke længere er i JSON
    $deletedCount = 0
    $adEmails = $contacts | ForEach-Object { $_.emailAddresses[0].address }
    $toDelete = $existingContacts | Where-Object {
        ($_.EmailAddresses[0].Address -like "*@andersen-partners.dk") -and
        ($adEmails -notcontains $_.EmailAddresses[0].Address)
    }

    Write-Log "Starter sletning af kontakter, der ikke findes i Emply..."
    foreach ($contact in $toDelete) {
        $progressPercent = [math]::Round($deletedCount / $toDelete.Count * 100)
        Write-Progress -Activity "Sletter kontakter" -Status "Sletter $($contact.DisplayName)" -PercentComplete $progressPercent

        try {
            Write-Log "Sletter kontakt: $($contact.DisplayName) med email: $($contact.EmailAddresses[0].Address)" -Level "success"
            # Slet kontakt
            if ($WhatIf) {
                Write-Log "WhatIf: Ville slette kontakt: $($contact.DisplayName)" -Level "Warning"
            } else {
                Remove-MgUserContact -UserId $UserId -ContactId $contact.Id -Confirm:$false > $null
            }
            $deletedCount++
        } catch {
            Write-Log "Fejl ved sletning af kontakt $($contact.DisplayName): $_" -Level "Error"
        }
    }
    Write-Log "Sletning af kontakter completed" -Level "Success"

    # Vis opsummering
    Write-Log "Synkroniseringen er completed!" -Level "success"
    Write-Log "Opsummering:" -Level "Info"
    Write-Log "- Kontakter i Emply: $($contacts.Count)" -Level "Info"
    Write-Log "- Eksisterende kontakter: $($existingContacts.Count)" -Level "Info"
    Write-Log "- Oprettet: $createdCount" -Level "Info"
    Write-Log "- Sprunget over (allerede eksisterende): $skippedCount" -Level "Info"
    Write-Log "- Slettet: $deletedCount" -Level "Info"

} catch {
    Write-Log "Der opstod en fejl: $_" -Level "Error"
} finally {
    # Ryd op
    Write-Log "Script completed." -Level "Success"
}