## Get Shared Mailbox in Office 365 using PowerShell
This PowerShell script helps admins to get a list of shared mailbox in Office 365 and their details like archive status, license, forwarding config, etc.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Shared Mailbox report](https://o365reports.com/wp-content/uploads/2022/07/get-shared-mailboxes-in-Office-365-using-PowerShell.png?v=**********)
## Microsoft 365 Reporting Tool by AdminDroid
Need more detailed insights? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) delivers over 1800 pre-configured reports and dashboards, a perfect complement to your PowerShell scripts.

*View more comprehensive shared mailbox report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10013/1/20>* 

