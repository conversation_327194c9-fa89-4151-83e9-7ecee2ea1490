## Audit External User File Access in SharePoint Online Using PowerShell
This PowerShell script exports Office 365 external users’ file access report to CSV. This helps to find who accessed the file and when in SharePoint Online.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![SharePoint Online External User File Access Report](https://o365reports.com/wp-content/uploads/2021/03/file-access-report.png?v=1705576747)
## Microsoft 365 Reporting Tool by AdminDroid
Need more detailed insights? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) delivers over 1800 pre-configured reports and dashboards, a perfect complement to your PowerShell scripts.

*View more comprehensive external user file access report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/29813/1/20>* 

