﻿## Export All Mailboxes in Microsoft 365 Using PowerShell    
Say goodbye to messy mailbox exports. Our PowerShell script for Microsoft 365 lets you export all mailbox types with clean formatting!

***Sample Output:***

The script generates output log file in CSV format that shows the result of the operation.

![Get Mailboxes in Microsoft 365](https://o365reports.com/wp-content/uploads/2025/05/Get-Mailboxes-in-Microsoft-365-800x244.png?v=1746524485)
## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive Mailbox based reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/10001/1/20](https://demo.admindroid.com/#/1/11/reports/10001/1/20)*



