## Export Distribution Groups Report in Microsoft 365

Export distribution groups report in Microsoft 365 with this PowerShell script to view display name, external sender, authorized members, and more. It helps you identify empty DLs and remove any duplicate distribution lists.

***Sample Output:***

The exported report on distribution groups looks like the screenshot below.

![Distribution Groups Report](<https://o365reports.com/wp-content/uploads/2025/03/distribution-groups-report.png?v=1741081567>)

It also exports the distribution lists membership report that looks like the screenshot below.

![Distribution Groups Membership Report](<https://o365reports.com/wp-content/uploads/2025/03/distribution-groups-membership-report.png?v=1741081659>)

## Microsoft 365 Reporting tool by AdminDroid

Transform your Microsoft 365 data into actionable insights with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), featuring over 1900+ pre-built reports and 30+ dashboards.

*Access more comprehensive Microsoft 365 reports with AdminDroid: [https://demo.admindroid.com/#/1/11/reports/1004/1/20](https://demo.admindroid.com/#/1/11/reports/1004/1/20)*
