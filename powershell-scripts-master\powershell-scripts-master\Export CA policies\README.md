﻿## Export Conditional Access Policies to Excel using PowerShell
Export Conditional Access policies using PowerShell for efficient CA policy management and enhanced Microsoft 365 security.

***Sample Output:***

This script exports an output CSV file that looks similar to the screenshot below.

![Conditional Access Policies report]( https://o365reports.com/wp-content/uploads/2024/02/Output-Export-CA-policies-full-name.png)

## Microsoft 365 Reporting tool by AdminDroid
Finding value in this script? Discover [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) with 1800+ pre-built reports and dashboards for an in-depth data analysis.

*Explore detailed CA policies insight with AdminDroid reports: [https://demo.admindroid.com/#/1/11/reports/301/1/20](https://demo.admindroid.com/#/1/11/reports/301/1/20)*


