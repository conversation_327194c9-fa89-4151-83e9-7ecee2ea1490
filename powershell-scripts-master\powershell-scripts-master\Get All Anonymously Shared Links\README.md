## Get All Anonymous Links Shared from SharePoint Online

Use this PowerShell script to export all anonymous sharing links along with shared link, file/folder name, access type, link expiry date, and more. It helps you identify any sensitive data exposure and remove unnecessary access links.

***Sample Output:***

The exported anonymous sharing links report looks like the screenshot below.

![SPO Anyone Links Report ](<https://o365reports.com/wp-content/uploads/2025/04/AnonymousLink-Report.png?v=1745316103>)

## Microsoft 365 Reporting tool by AdminDroid

Transform your Microsoft 365 data into actionable insights with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), featuring over 1900+ pre-built reports and 30+ dashboards.

*Find sensitive data exposure through anonymous links tracking with AdminDroid: [https://demo.admindroid.com/#/1/11/reports/22056/1/20](https://demo.admindroid.com/#/1/11/reports/22056/1/20)*