## Export M365 Group-Based License Assignment Report Using PowerShell

Find all Microsoft 365 users with group-based license assignment, spot disabled users, and identify license errors using PowerShell.

***Sample Output:***

This script exports all group-based licensing assignments of Office 365 users to an output CSV file that looks like the screenshot below.

![M365 Group-Based License Assignment Report](https://o365reports.com/wp-content/uploads/2024/05/script-1.png?v=**********)

## Microsoft 365 Reporting tool by AdminDroid

Take your Office 365 license management to the next level with the [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1800+ pre-built reports and dashboards for in-depth analysis and efficient oversight.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/8/1/20>*
