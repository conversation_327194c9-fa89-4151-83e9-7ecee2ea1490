# Revoke MFA sessions
# Get username
[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$userName
)
# UPN
$UserID = "$<EMAIL>"

# Do AzureAD stuff
Connect-ApMsOnlineServices -AzureAD
$objid = (Get-AzureADUser -All $true | Where-Object {$_.UserPrincipalName -eq $UserID}).ObjectId
Revoke-AzureADUserAllRefreshToken -ObjectId $objid -Verbose

# Do MgGraph stuff
if (!(Get-MgContext)) {
    Connect-MgGraph -Scopes "UserAuthenticationMethod.ReadWrite.All"
}

# Get Authentication Methods
$MicrosoftAuthenticatorMethods = (Get-MgUserAuthenticationMicrosoftAuthenticatorMethod -UserID $UserID).Id
$PhoneMethods                  = (Get-MgUserAuthenticationPhoneMethod -UserID $UserID).Id
$SoftwareOathMethods           = (Get-MgUserAuthenticationSoftwareOathMethod -UserID $UserID).Id
$EmailMethods                  = (Get-MgUserAuthenticationEmailMethod -UserID $UserID).Id
$FidoMethods                   = (Get-MgUserAuthenticationFido2Method -UserID $UserID).Id
$TapMethods                    = (Get-MgUserAuthenticationTemporaryAccessPassMethod -UserID $UserID).Id
$WH4BMethods                   = (Get-MgUserAuthenticationWindowsHelloForBusinessMethod -UserId $UserID).Id


if ($WH4BMethods) {
    # Remove WH4B method
    foreach ($WH4BMethod in $WH4BMethods) {
        Remove-MgUserAuthenticationWindowsHelloForBusinessMethod -UserId $UserID -WindowsHelloForBusinessAuthenticationMethodId "$WH4BMethod" -ErrorAction Stop
        Write-Host "Successfully removed WH4B Method: " -ForegroundColor Cyan -NoNewline
        Write-Host $WH4BMethod -ForegroundColor DarkYellow
    }
}

if ($TapMethods) {
    # Remove TAP method
    foreach ($TapMethod in $TapMethods) {
        Remove-MgUserAuthenticationTemporaryAccessPassMethod -UserId $UserID -TemporaryAccessPassAuthenticationMethodId "$TapMethod" -ErrorAction Stop
        Write-Host "Successfully removed TAP Method: " -ForegroundColor Cyan
        Write-Host $TapMethod -ForegroundColor DarkYellow
    }
}
if ($FidoMethods) {
    # Remove FIDO method
    foreach ($FidoMethod in $FidoMethods) {
        Remove-MgUserAuthenticationFido2Method -UserId $UserID -Fido2AuthenticationMethodId "$FidoMethod" -ErrorAction Stop
        Write-Host "Successfully removed Fido Method: " -ForegroundColor Cyan -NoNewline
        Write-Host $FidoMethod -ForegroundColor DarkYellow
    }
}
if ($EmailMethods) {
    # Remove Email Method
    foreach ($EmailMethod in $EmailMethods) {
        Remove-MgUserAuthenticationEmailMethod -UserId $UserID -EmailAuthenticationMethodId "$EmailMethod" -ErrorAction Stop
        Write-Host "Successfully removed Email Method: " -ForegroundColor Cyan -NoNewline
        Write-Host $EmailMethod -ForegroundColor DarkYellow
    }
}
if ($SoftwareOathMethods) {
    # Remove Software OAth Method
    foreach ($SoftwareOathMethod in $SoftwareOathMethods){
        Remove-MgUserAuthenticationSoftwareOathMethod -UserID $UserID -SoftwareOathAuthenticationMethodId $SoftwareOathMethod -ErrorAction Stop
        Write-Host "Successfully removed Software OAth Method: " -ForegroundColor Cyan -NoNewline
        Write-Host $SoftwareOathMethod -ForegroundColor DarkYellow
    }
}
if ($PhoneMethods) {
    # Remove Phone Method
    foreach ($PhoneMethod in $PhoneMethods){
        Remove-MgUserAuthenticationPhoneMethod -UserID $UserID -PhoneAuthenticationMethodId $PhoneMethod -ErrorAction Stop
        Write-Host "Successfully removed Phone Method: " -ForegroundColor Cyan -NoNewline
        Write-Host $PhoneMethod -ForegroundColor DarkYellow
    }
}
if ($MicrosoftAuthenticatorMethods) {
    #Remove Microsoft Authenticator Method
    foreach ($MicrosoftAuthenticatorMethod in $MicrosoftAuthenticatorMethods){
        Remove-MgUserAuthenticationMicrosoftAuthenticatorMethod -UserID $UserID -MicrosoftAuthenticatorAuthenticationMethodId $MicrosoftAuthenticatorMethod -ErrorAction Stop
        Write-Host "Successfully removed Microsoft Authenticator Method" $MicrosoftAuthenticatorMethod
    }
}