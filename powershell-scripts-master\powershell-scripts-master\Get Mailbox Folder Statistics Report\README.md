## Export Exchange Online Mailbox Folder Statistics Using PowerShell

Get detailed mailbox folder statistics for all EXO mailboxes with this PS script. It solves all use cases and helps to optimize storage.

***Sample Output:***

This script exports an output CSV file that looks similar to the screenshot below.

![Exchange Online Mailbox Folder Statistics](https://o365reports.com/wp-content/uploads/2024/05/MailboxFolderStatisticsReports-Output-1024x355.png?v=1716285306)

## Microsoft 365 Reporting tool by AdminDroid

Take your mailbox folder management to the next level with the [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1800+ pre-built reports and dashboards for in-depth analysis and efficient oversight.

*Access more comprehensive M365 reports with AdminDroid:<https://demo.admindroid.com>*


