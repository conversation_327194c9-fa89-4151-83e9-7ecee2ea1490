﻿## Export Mailbox Quota Report Using PowerShell
Download the PowerShell script to effortlessly generate a comprehensive mailbox quota report and keep your email system running smoothly.

***Sample Output:***

The script exports an output CSV file that looks similar to the screenshot below.

![Exchange Mailbox quota report](https://o365reports.com/wp-content/uploads/2024/07/Exchange-Mailbox-quota-report-300x81.png)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive mailbox reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/70021/1/20](https://demo.admindroid.com/#/1/11/reports/70021/1/20)*



