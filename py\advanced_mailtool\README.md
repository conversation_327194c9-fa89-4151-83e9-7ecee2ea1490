# 📧 Advanced MailTool

En moderne og kraftfuld email viewer og processor bygget med PySide6.

## ✨ Funktioner

### 📁 **Multi-format Support**
- **MBOX filer** (.mbox, .mbx) - Fuld support med robust parsing
- **PST filer** (.pst) - Outlook integration + direkte læsning
- **EML filer** (.eml) - Individuelle email filer
- **MSG filer** (.msg) - Outlook besked filer

### 🎨 **Moderne Interface**
- **Dark/Light tema** - Komplet tema support med automatisk gemning
- **Responsivt design** - Moderne layout med splitter views
- **Trævisning** - Organiseret email liste med søgning og filtrering
- **Email viewer** - Detaljeret visning med HTML/text support

### 🔍 **Avanceret Søgning**
- **Modern search interface** - Elegant search felt med ikoner
- **Real-time search** - Øjeblikkelige resultater mens du skriver
- **Smart filtering** - <PERSON><PERSON><PERSON>æ<PERSON>ning<PERSON>, dato, ulæste beskeder
- **Cross-field search** - <PERSON>øg på tværs af emne, afsender og indhold

### 📤 **Export Funktioner**
- **MBOX export** - Gem som standard mailbox format
- **EML export** - Individuelle email filer
- **Bulk export** - Eksporter flere beskeder på én gang
- **Vedhæftnings export** - Gem alle vedhæftninger til mappe

### 📎 **Vedhæftnings Håndtering**
- **Visning** - Se alle vedhæftninger med metadata
- **Download** - Gem individuelle vedhæftninger
- **Bulk download** - Gem alle vedhæftninger på én gang
- **Preview** - Filstørrelse og type information

## 🚀 Installation

### Forudsætninger
- Python 3.8 eller nyere
- Windows (for PST support via Outlook)

### Trin 1: Installer dependencies
```bash
pip install -r requirements.txt
```

### Trin 2: Kør applikationen
```bash
python main.py
```

## 📋 Dependencies

```
PySide6>=6.6.0          # GUI framework
email-validator>=2.0.0  # Email validation
olefile>=0.47           # PST file support
extract-msg>=0.54.0     # MSG file support
pywin32>=306            # Windows integration (Windows only)
py7zr>=0.21.0          # Archive support
tqdm>=4.66.0           # Progress bars
python-dateutil>=2.8.0 # Date handling
chardet>=5.2.0         # Character encoding
```

## 🎯 Brug

### Åbning af Filer
1. Klik **"📁 Open Files"** i værktøjslinjen
2. Vælg dine email filer (MBOX, PST, EML, MSG)
3. Vent på indlæsning (progress vises)

### Navigation
- **Email liste** (venstre): Trævisning af emails med multi-selection support
- **Email viewer** (højre): Detaljeret visning af valgte email
- **Multi-selection**: Ctrl+klik eller Shift+klik for at vælge flere emails
- **Søgning**: Brug søgefeltet eller avanceret søgning (Ctrl+F)

### Export
- **Alle beskeder**: File → Export → Export All Messages
- **Valgte beskeder**: Select emails + click "📤 Export Selected" button
- **Enkelt besked**: Select one email + click "📤 Export Message" button
- **Vedhæftninger**: File → Export → Export All Attachments

### Tema Skift
- **Toolbar**: Klik "🎨 Toggle Theme"
- **Menu**: View → Theme → Light/Dark
- **Automatisk**: Tema gemmes og genindlæses

## 🔧 Tekniske Detaljer

### Arkitektur
```
advanced_mailtool/
├── main.py                 # Hovedapplikation
├── src/
│   ├── core/
│   │   └── email_message.py    # Email datamodel
│   ├── engines/
│   │   ├── base_engine.py      # Base engine klasse
│   │   ├── mbox_engine.py      # MBOX håndtering
│   │   ├── pst_engine.py       # PST håndtering
│   │   ├── eml_engine.py       # EML håndtering
│   │   ├── msg_engine.py       # MSG håndtering
│   │   └── engine_manager.py   # Engine koordinator
│   ├── gui/
│   │   ├── main_window.py      # Hovedvindue
│   │   ├── themes.py           # Tema system
│   │   └── widgets/
│   │       ├── email_list.py   # Email liste widget
│   │       ├── email_viewer.py # Email viewer widget
│   │       ├── search_dialog.py # Søge dialog
│   │       └── progress_dialog.py # Progress dialog
│   └── exporters/
│       └── export_dialog.py    # Export dialog
├── test_emails.mbox        # Test data
├── requirements.txt        # Dependencies
└── README.md              # Denne fil
```

### PST Support
- **readpst integration**: Automatisk konvertering hvis readpst er installeret
- **Outlook COM**: Bruger Outlook's COM interface (hvis tilgængeligt)
- **Konverteringsguide**: Detaljeret guide til gratis PST konvertering
- **Installation guide**: Se `INSTALL_READPST.md` for readpst installation
- **Fejlhåndtering**: Brugervenlige meddelelser og løsningsforslag

### MBOX Support
- **Robust parsing**: Håndterer beskadigede eller ugyldige beskeder
- **Encoding detection**: Automatisk detektion af character encoding
- **Multipart support**: Fuld support for multipart beskeder

## 🧪 Test

Test applikationen med den inkluderede test fil:

```bash
# Start applikationen
python main.py

# Åbn test_emails.mbox filen
# Prøv forskellige funktioner:
# - Søgning
# - Filtrering  
# - Export
# - Tema skift
```

## 🎨 Temaer

### ☀️ Light Theme
- Moderne lyst design
- Blå accent farver (#0078d4)
- Høj kontrast for læsbarhed

### 🌙 Dark Theme  
- Elegant mørkt design
- Inspireret af VS Code
- Øjenskånsom for lang brug

## 🐛 Fejlfinding

### PST Filer
- **Konvertering påkrævet**: Se `PST_CONVERSION_GUIDE.md` for detaljeret guide
- **Gratis værktøjer**: PST Viewer, MailStore Home, Aid4Mail Community
- **Outlook metode**: Eksporter til MBOX via Outlook
- **Permission denied**: Kør som administrator eller kopier PST fil

### MBOX Filer
- **Encoding problemer**: Applikationen detekterer automatisk encoding
- **Store filer**: Kan tage tid at indlæse - vent på progress

### Performance
- **Mange beskeder**: Brug filtrering og søgning
- **Store vedhæftninger**: Vedhæftninger indlæses on-demand

## 📝 Changelog

### Version 2.0
- ✅ Komplet omskrivning med moderne arkitektur
- ✅ Multi-format support (MBOX, PST, EML, MSG)
- ✅ Avanceret søgning og filtrering
- ✅ Export funktionalitet
- ✅ Moderne dark/light tema
- ✅ Vedhæftnings håndtering
- ✅ Robust fejlhåndtering

## 🤝 Bidrag

Dette er et internt værktøj. For fejlrapporter eller funktionsanmodninger, kontakt udvikleren.

## 📞 Support

Ved spørgsmål eller problemer:
1. Tjek log filen i `logs/advanced_mailtool.log`
2. Kontakt udviklingsteamet
3. Inkluder fejlmeddelelser og log output

---

**Udviklet med ❤️ og PySide6**

*Advanced MailTool - Din moderne email companion*
