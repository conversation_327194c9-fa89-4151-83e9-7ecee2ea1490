## Find the Teams Meetings Attended by a Specific User Using PowerShell

Learn how to find out the Teams meetings attended by a specific user effectively using our PowerShell script.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Teams Meeting Attended by a User Report](https://o365reports.com/wp-content/uploads/2024/05/Teams-Meeting-Specific-User-Attendance-Report-768x189.png?v=1716291329)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive Teams meeting attended by a user report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/60028/1/20>*