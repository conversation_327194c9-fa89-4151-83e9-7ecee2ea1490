﻿## Get Microsoft 365 Users’ Registered MFA Methods With PowerShell 
Weak authentication methods can be a gateway for attacks & data leaks. Quickly review Microsoft 365 users' registered MFA authentication methods with this PowerShell script!

***Sample Output:***

The script exports an output CSV file that looks similar to the screenshot below.

![Registered MFA Methods in Microsoft 365](https://o365reports.com/wp-content/uploads/2024/08/Registered-MFA-Methods-in-Microsoft-365-768x186.png?v=1723549564)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive MFA reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/70021/1/20](https://demo.admindroid.com/#/1/11/reports/70021/1/20)*



