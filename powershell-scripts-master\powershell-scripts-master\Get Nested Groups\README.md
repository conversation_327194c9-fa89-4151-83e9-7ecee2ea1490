﻿## Microsoft 365 Nested Groups Report
Learn how to get nested groups in Microsoft 365 using PowerShell for managing group hierarchies efficiently.

***Sample Output***

The script analyzes and exports two reports such as,
####
**M365 Nested Groups Summary Report:**

![Nested groups summary report](https://o365reports.com/wp-content/uploads/2024/11/2024-11-19-15\_12\_15-summary-report.csv-Excel-1.png?v=1732009471)

**M365 Nested Groups Detailed Report:**

![Nested groups detailed report](https://o365reports.com/wp-content/uploads/2024/11/2024-11-19-15\_13\_12-detailed-report.csv-Excel.png?v=1732009507)
## Microsoft 365 Reporting Tool by AdminDroid
If this script is helpful, you’ll love [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub)! With over 1800 pre-built reports and dashboards, it’s a fantastic way to dive deeper into your M365 data.

*View detailed SharePoint storage consumption reports in AdminDroid: [https://demo.admindroid.com/#/1/11/reports/1011/1/20](https://demo.admindroid.com/#/1/11/reports/1011/1/20)*




