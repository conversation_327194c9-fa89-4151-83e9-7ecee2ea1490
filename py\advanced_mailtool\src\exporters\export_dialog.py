"""
Export dialog for saving messages in various formats
"""

from typing import List
from pathlib import Path

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QComboBox, QLineEdit, QFileDialog,
                              QCheckBox, QGroupBox, QRadioButton, QButtonGroup,
                              QProgressBar, QTextEdit, QMessageBox)
from PySide6.QtCore import Qt, QThread, Signal

from ..core.email_message import EmailMessage
from ..engines.engine_manager import engine_manager
from ..engines.base_engine import ProgressCallback


class ExportWorker(QThread):
    """Worker thread for exporting messages"""
    
    progress_updated = Signal(str, int, int)
    finished = Signal(bool, str)  # success, message
    
    def __init__(self, messages: List[EmailMessage], output_path: Path, format_type: str):
        super().__init__()
        self.messages = messages
        self.output_path = output_path
        self.format_type = format_type
    
    def run(self):
        try:
            from ..core.email_message import EmailCollection
            
            # Create collection
            collection = EmailCollection()
            collection.add_messages(self.messages)
            
            # Create progress callback
            progress = ProgressCallback(self.progress_updated.emit)
            
            # Export messages
            success = engine_manager.save_file(
                collection, 
                self.output_path, 
                self.format_type,
                progress
            )
            
            if success:
                self.finished.emit(True, f"Successfully exported {len(self.messages)} messages")
            else:
                self.finished.emit(False, "Export failed")
                
        except Exception as e:
            self.finished.emit(False, f"Export error: {str(e)}")


class ExportDialog(QDialog):
    """Dialog for exporting email messages"""
    
    def __init__(self, messages: List[EmailMessage], parent=None):
        super().__init__(parent)
        
        self.messages = messages
        self.export_worker = None
        
        self.setWindowTitle("Export Messages")
        self.setModal(True)
        self.setMinimumSize(500, 400)
        self.resize(600, 500)
        
        self.setup_ui()
        self.update_preview()
    
    def setup_ui(self):
        """Setup the user interface"""
        
        layout = QVBoxLayout(self)
        layout.setSpacing(16)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        title = QLabel(f"Export {len(self.messages)} Messages")
        title.setProperty("class", "header")
        layout.addWidget(title)
        
        # Format selection
        format_group = QGroupBox("Export Format")
        format_layout = QVBoxLayout(format_group)
        
        self.format_group = QButtonGroup()
        
        # MBOX format
        self.mbox_radio = QRadioButton("MBOX Format (.mbox)")
        self.mbox_radio.setToolTip("Standard mailbox format, compatible with most email clients")
        self.mbox_radio.setChecked(True)
        self.format_group.addButton(self.mbox_radio, 0)
        format_layout.addWidget(self.mbox_radio)
        
        # EML format
        self.eml_radio = QRadioButton("EML Format (.eml)")
        self.eml_radio.setToolTip("Individual email files, one per message")
        self.format_group.addButton(self.eml_radio, 1)
        format_layout.addWidget(self.eml_radio)
        
        # MSG format (note)
        self.msg_radio = QRadioButton("MSG Format (.eml) - Converted")
        self.msg_radio.setToolTip("Outlook MSG format (saved as EML due to limitations)")
        self.format_group.addButton(self.msg_radio, 2)
        format_layout.addWidget(self.msg_radio)
        
        layout.addWidget(format_group)
        
        # Output location
        location_group = QGroupBox("Output Location")
        location_layout = QVBoxLayout(location_group)
        
        location_row = QHBoxLayout()
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("Select output file or directory...")
        location_row.addWidget(self.location_edit)
        
        self.browse_btn = QPushButton("Browse...")
        self.browse_btn.clicked.connect(self.browse_location)
        location_row.addWidget(self.browse_btn)
        
        location_layout.addLayout(location_row)
        layout.addWidget(location_group)
        
        # Options
        options_group = QGroupBox("Export Options")
        options_layout = QVBoxLayout(options_group)
        
        self.include_attachments_cb = QCheckBox("Include attachments")
        self.include_attachments_cb.setChecked(True)
        self.include_attachments_cb.setToolTip("Include email attachments in export")
        options_layout.addWidget(self.include_attachments_cb)
        
        self.preserve_structure_cb = QCheckBox("Preserve folder structure")
        self.preserve_structure_cb.setChecked(True)
        self.preserve_structure_cb.setToolTip("Maintain original folder organization")
        options_layout.addWidget(self.preserve_structure_cb)
        
        layout.addWidget(options_group)
        
        # Preview
        preview_group = QGroupBox("Export Preview")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(100)
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_group)
        
        # Progress bar (hidden initially)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.export_btn = QPushButton("Export")
        self.export_btn.clicked.connect(self.start_export)
        self.export_btn.setDefault(True)
        button_layout.addWidget(self.export_btn)
        
        layout.addLayout(button_layout)
        
        # Connect signals
        self.format_group.buttonClicked.connect(self.update_preview)
        self.location_edit.textChanged.connect(self.update_preview)
    
    def browse_location(self):
        """Browse for output location"""
        
        format_id = self.format_group.checkedId()
        
        if format_id == 0:  # MBOX
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Save MBOX File",
                "exported_messages.mbox",
                "MBOX Files (*.mbox);;All Files (*.*)"
            )
        elif format_id == 1:  # EML
            if len(self.messages) == 1:
                # Single email - save as specific file
                subject = self.messages[0].subject or "email"
                # Clean filename
                import re
                clean_subject = re.sub(r'[<>:"/\\|?*]', '_', subject)[:50]
                filename, _ = QFileDialog.getSaveFileName(
                    self,
                    "Save EML File",
                    f"{clean_subject}.eml",
                    "EML Files (*.eml);;All Files (*.*)"
                )
            else:
                # Multiple emails - select directory
                filename = QFileDialog.getExistingDirectory(
                    self,
                    "Select Directory for EML Files",
                    ""
                )
        else:  # MSG (as EML)
            if len(self.messages) == 1:
                # Single email - save as specific file
                subject = self.messages[0].subject or "email"
                # Clean filename
                import re
                clean_subject = re.sub(r'[<>:"/\\|?*]', '_', subject)[:50]
                filename, _ = QFileDialog.getSaveFileName(
                    self,
                    "Save MSG File (as EML)",
                    f"{clean_subject}.eml",
                    "EML Files (*.eml);;All Files (*.*)"
                )
            else:
                # Multiple emails - select directory
                filename = QFileDialog.getExistingDirectory(
                    self,
                    "Select Directory for MSG Files (as EML)",
                    ""
                )
        
        if filename:
            self.location_edit.setText(filename)
    
    def update_preview(self):
        """Update export preview"""
        
        format_id = self.format_group.checkedId()
        location = self.location_edit.text()
        
        preview_lines = []
        preview_lines.append(f"Messages to export: {len(self.messages)}")
        
        if format_id == 0:  # MBOX
            preview_lines.append("Format: MBOX (single file)")
            if location:
                preview_lines.append(f"Output file: {location}")
        elif format_id == 1:  # EML
            preview_lines.append("Format: EML (individual files)")
            if location:
                preview_lines.append(f"Output directory: {location}")
                preview_lines.append(f"Files: {len(self.messages)} .eml files")
        else:  # MSG
            preview_lines.append("Format: MSG (saved as EML)")
            if location:
                preview_lines.append(f"Output directory: {location}")
                preview_lines.append(f"Files: {len(self.messages)} .eml files")
        
        # Add options
        options = []
        if self.include_attachments_cb.isChecked():
            options.append("Include attachments")
        if self.preserve_structure_cb.isChecked():
            options.append("Preserve structure")
        
        if options:
            preview_lines.append(f"Options: {', '.join(options)}")
        
        self.preview_text.setPlainText("\n".join(preview_lines))
        
        # Enable/disable export button
        self.export_btn.setEnabled(bool(location))
    
    def start_export(self):
        """Start the export process"""
        
        location = self.location_edit.text()
        if not location:
            QMessageBox.warning(self, "No Location", "Please select an output location.")
            return
        
        format_id = self.format_group.checkedId()
        
        # Determine format and output path
        if format_id == 0:  # MBOX
            format_type = "mbox"
            output_path = Path(location)
        elif format_id == 1:  # EML
            format_type = "eml"
            output_path = Path(location)
        else:  # MSG (as EML)
            format_type = "eml"
            output_path = Path(location)
        
        # Disable UI during export
        self.export_btn.setEnabled(False)
        self.browse_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
        
        # Start export worker
        self.export_worker = ExportWorker(self.messages, output_path, format_type)
        self.export_worker.progress_updated.connect(self.update_progress)
        self.export_worker.finished.connect(self.on_export_finished)
        self.export_worker.start()
    
    def update_progress(self, message: str, current: int, total: int):
        """Update progress display"""
        
        if total > 0:
            self.progress_bar.setRange(0, total)
            self.progress_bar.setValue(current)
        else:
            self.progress_bar.setRange(0, 0)
    
    def on_export_finished(self, success: bool, message: str):
        """Handle export completion"""
        
        # Re-enable UI
        self.export_btn.setEnabled(True)
        self.browse_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            QMessageBox.information(self, "Export Complete", message)
            self.accept()
        else:
            QMessageBox.critical(self, "Export Failed", message)
    
    def closeEvent(self, event):
        """Handle close event"""
        
        # Stop export worker if running
        if self.export_worker and self.export_worker.isRunning():
            self.export_worker.terminate()
            self.export_worker.wait(3000)
        
        event.accept()
