﻿## Export Per-user MFA Status Report using MS Graph
Download this PowerShell script to retrieve per-user MFA status using MS Graph and export the report to CSV file.

***Sample Output:***

The script exports an output CSV file that looks similar to the screenshot below.

![MFA status report](https://blog.admindroid.com/wp-content/uploads/2024/06/Check-per-user-MFA-status-report-using-MS-Graph-1024x236.png)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts.
<br/>

*View more comprehensive MFA status report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/106/1/20>*

