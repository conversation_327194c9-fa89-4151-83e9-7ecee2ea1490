﻿## Audit Send As Emails in Microsoft 365: Find Out Who Sent Email from Which Mailbox

This PowerShell script helps to audit Send As emails and finds who sent email from the delegated mailbox using Send As permission.

***Sample Output:***

This script exports an output CSV file that resembles the screenshot below.

![Send As Emails report](https://o365reports.com/wp-content/uploads/2022/03/Audit-send-as-emails-in-Office-365.png)


## Microsoft 365 Auditing tool by AdminDroid

Looking for detailed M365 insights? [AdminDroid Microsoft 365 auditing tool](https://admindroid.com/?src=GitHub) provides 1800+ ready-to-use reports and dashboards for an enhanced data experience.

*Get a complete Send as Emails report view through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/21013/1/20](https://demo.admindroid.com/#/1/11/reports/21013/1/20)*

