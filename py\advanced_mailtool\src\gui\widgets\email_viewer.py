"""
Email viewer widget with modern interface and attachment support
"""

from typing import Optional
from pathlib import Path

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                              QTextEdit, QFrame, QPushButton, QScrollArea,
                              QSplitter, QFileDialog, QMessageBox, QSizePolicy)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

# Try to import WebEngine for better HTML rendering
try:
    from PySide6.QtWebEngineWidgets import QWebEngineView
    WEBENGINE_AVAILABLE = True
except ImportError:
    WEBENGINE_AVAILABLE = False

from ...core.email_message import EmailMessage, Attachment


class AttachmentWidget(QFrame):
    """Widget for displaying and managing email attachments"""
    
    attachment_saved = Signal(str)  # file_path
    
    def __init__(self, attachment: Attachment):
        super().__init__()
        
        self.attachment = attachment
        self.setup_ui()
    
    def setup_ui(self):
        """Setup attachment widget UI"""
        
        self.setProperty("class", "card")
        self.setMaximumHeight(80)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(12)
        
        # File icon and info
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        # Filename
        name_label = QLabel(self.attachment.filename or "Unknown file")
        name_label.setProperty("class", "subheader")
        info_layout.addWidget(name_label)
        
        # File details
        details = []
        if self.attachment.size > 0:
            details.append(f"{self.attachment.size_mb:.1f} MB")
        if self.attachment.content_type:
            details.append(self.attachment.content_type)
        
        details_label = QLabel(" • ".join(details) if details else "No details")
        details_label.setProperty("class", "muted")
        info_layout.addWidget(details_label)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # Action buttons
        if self.attachment.data:
            save_btn = QPushButton("💾 Save")
            save_btn.setProperty("class", "secondary")
            save_btn.clicked.connect(self.save_attachment)
            layout.addWidget(save_btn)
        else:
            unavailable_label = QLabel("Data not available")
            unavailable_label.setProperty("class", "muted")
            layout.addWidget(unavailable_label)
    
    def save_attachment(self):
        """Save attachment to file"""
        
        if not self.attachment.data:
            QMessageBox.warning(self, "No Data", "Attachment data is not available.")
            return
        
        # Get save location
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Save Attachment",
            self.attachment.filename or "attachment",
            "All Files (*.*)"
        )
        
        if filename:
            try:
                success = self.attachment.save_to_file(filename)
                if success:
                    QMessageBox.information(
                        self, 
                        "Saved", 
                        f"Attachment saved to:\n{filename}"
                    )
                    self.attachment_saved.emit(filename)
                else:
                    QMessageBox.warning(
                        self, 
                        "Save Failed", 
                        "Failed to save attachment."
                    )
            except Exception as e:
                QMessageBox.critical(
                    self, 
                    "Error", 
                    f"Error saving attachment:\n{str(e)}"
                )


class EmailViewerWidget(QWidget):
    """Modern email viewer widget"""
    
    def __init__(self):
        super().__init__()
        
        self.current_message: Optional[EmailMessage] = None
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the user interface"""
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # Create splitter for content and attachments
        self.splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Main content area
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(8)
        
        # Header frame
        self.header_frame = QFrame()
        self.header_frame.setProperty("class", "card")
        self.setup_header()
        content_layout.addWidget(self.header_frame)
        
        # Email content - better HTML rendering
        if WEBENGINE_AVAILABLE:
            # Try to use WebEngine for better HTML rendering
            self.content_text = QWebEngineView()
            self.content_text.setStyleSheet("""
                QWebEngineView {
                    background-color: #ffffff !important;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                }
            """)
        else:
            # Fallback to QTextEdit with better settings
            self.content_text = QTextEdit()
            self.content_text.setReadOnly(True)
            # Better HTML support
            self.content_text.setAcceptRichText(True)
            self.content_text.setStyleSheet("""
                QTextEdit {
                    background-color: #ffffff !important;
                    color: #000000 !important;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 16px;
                    font-family: "Segoe UI", Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.5;
                }
            """)

        content_layout.addWidget(self.content_text)
        
        self.splitter.addWidget(content_widget)
        
        # Attachments area
        self.attachments_widget = QWidget()
        self.setup_attachments_area()
        self.splitter.addWidget(self.attachments_widget)
        
        # Set splitter proportions
        self.splitter.setStretchFactor(0, 3)  # Content area
        self.splitter.setStretchFactor(1, 1)  # Attachments area
        
        layout.addWidget(self.splitter)
        
        # Initially hide attachments
        self.attachments_widget.setVisible(False)
        
        # Show welcome message
        self.show_welcome_message()
    
    def setup_header(self):
        """Setup classic email header with all fields visible"""

        layout = QVBoxLayout(self.header_frame)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(6)  # Much tighter spacing

        # Subject
        self.subject_label = QLabel()
        self.subject_label.setProperty("class", "email-subject")
        self.subject_label.setWordWrap(True)
        layout.addWidget(self.subject_label)

        # From
        from_row = QHBoxLayout()
        from_row.setSpacing(4)  # Reduced spacing

        from_title = QLabel("From:")
        from_title.setProperty("class", "email-label")
        from_title.setFixedWidth(40)  # Fixed narrow width
        from_row.addWidget(from_title)

        self.from_label = QLabel()
        self.from_label.setProperty("class", "email-value")
        self.from_label.setWordWrap(True)
        from_row.addWidget(self.from_label)

        layout.addLayout(from_row)

        # To
        to_row = QHBoxLayout()
        to_row.setSpacing(4)  # Reduced spacing

        to_title = QLabel("To:")
        to_title.setProperty("class", "email-label")
        to_title.setFixedWidth(40)  # Fixed narrow width
        to_row.addWidget(to_title)

        self.to_label = QLabel()
        self.to_label.setProperty("class", "email-value")
        self.to_label.setWordWrap(True)
        to_row.addWidget(self.to_label)

        layout.addLayout(to_row)

        # Date
        date_row = QHBoxLayout()
        date_row.setSpacing(4)  # Reduced spacing

        date_title = QLabel("Date:")
        date_title.setProperty("class", "email-label")
        date_title.setFixedWidth(40)  # Fixed narrow width
        date_row.addWidget(date_title)

        self.date_label = QLabel()
        self.date_label.setProperty("class", "email-value")
        date_row.addWidget(self.date_label)

        layout.addLayout(date_row)

        # CC (hidden by default)
        self.cc_widget = QWidget()
        cc_layout = QHBoxLayout(self.cc_widget)
        cc_layout.setContentsMargins(0, 0, 0, 0)
        cc_layout.setSpacing(4)  # Reduced spacing

        cc_title = QLabel("CC:")
        cc_title.setProperty("class", "email-label")
        cc_title.setFixedWidth(40)  # Fixed narrow width
        cc_layout.addWidget(cc_title)

        self.cc_label = QLabel()
        self.cc_label.setProperty("class", "email-value")
        self.cc_label.setWordWrap(True)
        cc_layout.addWidget(self.cc_label)

        self.cc_widget.setVisible(False)
        layout.addWidget(self.cc_widget)

    
    def setup_attachments_area(self):
        """Setup attachments display area"""
        
        layout = QVBoxLayout(self.attachments_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # Attachments header
        header_frame = QFrame()
        header_frame.setProperty("class", "card")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(16, 12, 16, 12)
        
        attachments_title = QLabel("📎 Attachments")
        attachments_title.setProperty("class", "header")
        header_layout.addWidget(attachments_title)
        
        header_layout.addStretch()
        
        # Export all button - fix visibility
        self.export_all_btn = QPushButton("💾 Save All")
        self.export_all_btn.setProperty("class", "secondary")
        self.export_all_btn.setMinimumWidth(120)  # Wider minimum width
        self.export_all_btn.setMaximumWidth(120)  # Fixed width
        self.export_all_btn.setMinimumHeight(32)  # Ensure height
        self.export_all_btn.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.export_all_btn.clicked.connect(self.save_all_attachments)
        header_layout.addWidget(self.export_all_btn)
        
        layout.addWidget(header_frame)
        
        # Scroll area for attachments
        self.attachments_scroll = QScrollArea()
        self.attachments_scroll.setWidgetResizable(True)
        self.attachments_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.attachments_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # Attachments container
        self.attachments_container = QWidget()
        self.attachments_layout = QVBoxLayout(self.attachments_container)
        self.attachments_layout.setContentsMargins(0, 0, 0, 0)
        self.attachments_layout.setSpacing(4)
        
        self.attachments_scroll.setWidget(self.attachments_container)
        layout.addWidget(self.attachments_scroll)
    
    def display_email(self, message: EmailMessage):
        """Display email message"""

        self.current_message = message

        # Show header frame when displaying email
        self.header_frame.setVisible(True)

        # Classic email display
        self.subject_label.setText(message.subject or "(No Subject)")

        # From
        self.from_label.setText(message.sender_display or "(Unknown)")

        # To
        if message.recipients:
            if len(message.recipients) == 1:
                self.to_label.setText(message.recipients[0])
            else:
                self.to_label.setText(f"{message.recipients[0]} and {len(message.recipients)-1} others")
        else:
            self.to_label.setText("(No recipients)")

        # Date
        self.date_label.setText(message.date_display or "(Unknown)")

        # CC
        if message.cc:
            self.cc_label.setText(", ".join(message.cc))
            self.cc_widget.setVisible(True)
        else:
            self.cc_widget.setVisible(False)
        
        # Update content - better handling for different widget types
        html_content = message.get_body(prefer_html=True)
        plain_content = message.get_body(prefer_html=False)

        if hasattr(self.content_text, 'setHtml'):
            # QTextEdit or similar
            if html_content and html_content.strip():
                # Wrap HTML in proper document structure for better rendering
                wrapped_html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <style>
                        body {{
                            font-family: "Segoe UI", Arial, sans-serif;
                            font-size: 14px;
                            line-height: 1.5;
                            margin: 0;
                            padding: 16px;
                            background-color: #ffffff;
                            color: #000000;
                        }}
                        img {{
                            max-width: 100%;
                            height: auto;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            margin: 4px 0;
                        }}
                        table {{ border-collapse: collapse; width: 100%; }}
                        td, th {{ padding: 4px 8px; }}
                        .embedded-image {{
                            display: block;
                            margin: 8px auto;
                            max-width: 90%;
                        }}
                    </style>
                </head>
                <body>
                    {html_content}
                </body>
                </html>
                """
                self.content_text.setHtml(wrapped_html)
            elif plain_content and plain_content.strip():
                self.content_text.setPlainText(plain_content)
            else:
                self.content_text.setPlainText("(No content)")
        elif hasattr(self.content_text, 'setHtml'):
            # WebEngine
            if html_content and html_content.strip():
                self.content_text.setHtml(html_content)
            else:
                self.content_text.setHtml(f"<html><body style='font-family: Segoe UI; padding: 16px;'>{plain_content or '(No content)'}</body></html>")
        else:
            # Fallback
            self.content_text.setPlainText(plain_content or "(No content)")
        
        # Update attachments
        self.update_attachments(message.attachments)
    
    def update_attachments(self, attachments: list):
        """Update attachments display"""
        
        # Clear existing attachments
        for i in reversed(range(self.attachments_layout.count())):
            child = self.attachments_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        if not attachments:
            self.attachments_widget.setVisible(False)
            return
        
        # Add attachments
        for attachment in attachments:
            attachment_widget = AttachmentWidget(attachment)
            self.attachments_layout.addWidget(attachment_widget)
        
        # Add stretch at the end
        self.attachments_layout.addStretch()
        
        # Show attachments area
        self.attachments_widget.setVisible(True)
        
        # Update export button
        has_data = any(att.data for att in attachments)
        self.export_all_btn.setEnabled(has_data)
    
    def save_all_attachments(self):
        """Save all attachments to a directory"""
        
        if not self.current_message or not self.current_message.attachments:
            return
        
        # Get directory
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Directory for Attachments",
            ""
        )
        
        if not directory:
            return
        
        saved_count = 0
        total_count = 0
        
        for attachment in self.current_message.attachments:
            if attachment.data and attachment.filename:
                total_count += 1
                try:
                    file_path = Path(directory) / attachment.filename
                    # Handle duplicate filenames
                    counter = 1
                    original_path = file_path
                    while file_path.exists():
                        stem = original_path.stem
                        suffix = original_path.suffix
                        file_path = original_path.parent / f"{stem}_{counter}{suffix}"
                        counter += 1
                    
                    if attachment.save_to_file(file_path):
                        saved_count += 1
                except Exception:
                    continue
        
        if saved_count > 0:
            QMessageBox.information(
                self,
                "Attachments Saved",
                f"Saved {saved_count} of {total_count} attachments to:\n{directory}"
            )
        else:
            QMessageBox.warning(
                self,
                "Save Failed",
                "No attachments could be saved."
            )
    
    def show_welcome_message(self):
        """Show welcome message when no email is selected"""

        # Hide header frame completely when no email is selected
        self.header_frame.setVisible(False)

        # Show welcome message in body
        self.show_welcome_content()

    def show_welcome_content(self):
        """Show welcome content in the text area"""

        welcome_text = """
📧 Advanced MailTool - Modern Email Viewer

🚀 QUICK START GUIDE

1️⃣ LOAD EMAIL FILES
   • Click "📁 Open Files" in the toolbar
   • Select your email files (MBOX, EML, MSG)
   • Wait for loading to complete

2️⃣ BROWSE EMAILS
   • Email list appears on the left
   • Click any email to view it here
   • Use Ctrl+Click for multi-selection

3️⃣ SEARCH & FILTER
   • Use search box in email list
   • Try "🔍 Search" for advanced options
   • Filter by attachments, date, etc.

4️⃣ EXPORT & MANAGE
   • Export individual or multiple emails
   • Save attachments to folders
   • Multiple format support

📁 SUPPORTED FORMATS
   ✅ MBOX files (.mbox, .mbx) - Full support
   ✅ EML files (.eml) - Individual emails
   ✅ MSG files (.msg) - Outlook messages
   ⚠️  PST files (.pst) - Conversion required

🎨 INTERFACE FEATURES
   • Modern dark/light themes (🎨 Toggle Theme)
   • Responsive layout with resizable panels
   • Multi-selection with Ctrl+Click
   • Real-time search and filtering
   • Attachment preview and export

💡 TIPS
   • Drag the splitter to resize panels
   • Right-click for context menus
   • Use keyboard shortcuts (Ctrl+F for search)
   • Switch themes for comfortable viewing

👈 Select an email from the list to start viewing!
        """.strip()

        # Create dark theme welcome HTML
        welcome_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {
                    font-family: "Segoe UI", Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.6;
                    margin: 0;
                    padding: 24px;
                    background-color: #2d2d30;
                    color: #cccccc;
                }
                h1 { color: #ffffff; font-size: 20px; margin-bottom: 16px; }
                h2 { color: #569cd6; font-size: 16px; margin-top: 16px; margin-bottom: 8px; }
                .feature { margin: 6px 0; }
                .highlight { color: #4ec9b0; }
                .tip { background-color: #3c3c3c; padding: 12px; border-radius: 4px; margin: 12px 0; text-align: center; }
            </style>
        </head>
        <body>
            <h1>📧 Advanced MailTool - Modern Email Viewer</h1>

            <h2>🚀 QUICK START GUIDE</h2>

            <h2>1️⃣ LOAD EMAIL FILES</h2>
            <div class="feature">• Click <span class="highlight">"📁 Open Files"</span> in the toolbar</div>
            <div class="feature">• Select your email files (MBOX, EML, MSG, PST)</div>
            <div class="feature">• Wait for loading to complete</div>

            <h2>2️⃣ BROWSE EMAILS</h2>
            <div class="feature">• Email list appears on the left</div>
            <div class="feature">• Click any email to view it here</div>
            <div class="feature">• Use Ctrl+Click for multi-selection</div>

            <h2>📁 SUPPORTED FORMATS</h2>
            <div class="feature">✅ <span class="highlight">MBOX files</span> (.mbox, .mbx) - Full support</div>
            <div class="feature">✅ <span class="highlight">EML files</span> (.eml) - Individual emails</div>
            <div class="feature">✅ <span class="highlight">MSG files</span> (.msg) - Outlook messages</div>
            <div class="feature">✅ <span class="highlight">PST files</span> (.pst) - Direct support with Win32COM</div>

            <div class="tip">
                <strong>👈 Select an email from the list to start viewing!</strong>
            </div>
        </body>
        </html>
        """

        # Set dark theme welcome content
        if hasattr(self.content_text, 'setHtml'):
            self.content_text.setHtml(welcome_html)
        else:
            self.content_text.setPlainText(welcome_text)

        self.attachments_widget.setVisible(False)
    
    def clear(self):
        """Clear the viewer"""
        self.show_welcome_message()
