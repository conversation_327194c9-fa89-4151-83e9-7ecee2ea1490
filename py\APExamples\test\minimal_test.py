#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minimal test for at isolere CSS problemet
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, <PERSON><PERSON>ush<PERSON>utton
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# Import ScrolledTextEdit
import importlib.util
spec = importlib.util.spec_from_file_location("test_apexamples", os.path.join(os.path.dirname(__file__), "Test-APExamples.py"))
test_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(test_module)
ScrolledTextEdit = test_module.ScrolledTextEdit

class MinimalWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Minimal CSS Test")
        self.setGeometry(100, 100, 600, 400)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Text editor MED CSS fra hovedapp
        self.text_edit_with_css = ScrolledTextEdit()
        self.text_edit_with_css.setObjectName("commandTextBox")
        self.text_edit_with_css.setFont(QFont("Consolas", 12))
        self.text_edit_with_css.powershell_syntax_enabled = True
        self.text_edit_with_css.update_syntax_colors("dark")
        layout.addWidget(self.text_edit_with_css)
        
        # Text editor UDEN CSS
        self.text_edit_no_css = ScrolledTextEdit()
        self.text_edit_no_css.setFont(QFont("Consolas", 12))
        self.text_edit_no_css.powershell_syntax_enabled = True
        self.text_edit_no_css.update_syntax_colors("dark")
        layout.addWidget(self.text_edit_no_css)
        
        # Test knap
        test_btn = QPushButton("Test Begge")
        test_btn.clicked.connect(self.test_both)
        layout.addWidget(test_btn)
        
        self.test_both()
        
    def test_both(self):
        test_code = """Get-Mailbox -Identity "<EMAIL>"
$users = Get-ADUser -Filter *
if ($users.Count -gt 0) {
    Write-Host "Found users" -ForegroundColor Green
}"""
        print("Setting text on both editors...")
        self.text_edit_with_css.setText(test_code)
        self.text_edit_no_css.setText(test_code)
        print("Done")

def main():
    app = QApplication(sys.argv)
    
    # Anvend SAMME CSS som hovedapplikationen (mørkt tema)
    app.setStyleSheet("""
        QMainWindow, QWidget {
            background-color: #2d2d30;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        /* Specifik styling for kommando text box - ingen farve override når syntaksfremhævning er aktiv */
        QTextEdit#commandTextBox {
            background-color: #1E1E1E !important;
            border: 1px solid #3F3F46 !important;
            border-radius: 8px !important;
            padding: 6px 8px !important;
            selection-background-color: #0078D7 !important;
            font-size: 12pt !important;
            /* Eksplicit ingen color property for at tillade syntaksfremhævning */
        }
        /* Andre QTextEdit elementer */
        QTextEdit {
            background-color: #1E1E1E;
            color: #ffffff;
            border: 1px solid #3F3F46;
            border-radius: 8px;
            padding: 6px 8px;
            selection-background-color: #0078D7;
            font-size: 12pt;
        }
        QPushButton {
            background-color: #0e639c;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
        }
    """)
    
    window = MinimalWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
