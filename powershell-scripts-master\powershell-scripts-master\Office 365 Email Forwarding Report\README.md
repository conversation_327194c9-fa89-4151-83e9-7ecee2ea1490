## Export Office 365 Email Forwarding Report Using PowerShell
The email forwarding report lists mailboxes that forwards and redirects email to another mailboxes. It considers SMTP forwarding, inbox rules, transport rule

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below

![Office 365 Email Forwarding Report](https://o365reports.com/wp-content/uploads/2021/06/df-1.png?v=1705576702)

## Microsoft 365 Reporting Tool by AdminDroid 
The [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) provides deep insights with its 1800+ pre-built Microsoft 365 reports and insightful dashboards.

*Export Office 365 Email Forwarding Report using <PERSON>minDroid for detailed insights:<https://demo.admindroid.com/#/1/11/reports/10664/1/20>*
