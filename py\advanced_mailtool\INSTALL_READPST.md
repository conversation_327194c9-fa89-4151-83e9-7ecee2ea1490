# 🛠️ Installing readpst for PST Support

`readpst` is a command-line tool that can convert PST files to MBOX format. Advanced MailTool can automatically use it if available.

## 🪟 Windows Installation

### Method 1: WSL (Windows Subsystem for Linux) - Recommended

1. **Install WSL**:
   ```powershell
   # Run as Administrator
   wsl --install
   ```

2. **Install Ubuntu** (or your preferred Linux distribution)

3. **Install readpst in WSL**:
   ```bash
   sudo apt update
   sudo apt install pst-utils
   ```

4. **Add WSL to Windows PATH**:
   - Add `C:\Windows\System32\wsl.exe` to your PATH
   - Or create a batch file `readpst.bat` in a PATH directory:
   ```batch
   @echo off
   wsl readpst %*
   ```

### Method 2: MSYS2/MinGW

1. **Install MSYS2** from https://www.msys2.org/

2. **Install readpst**:
   ```bash
   pacman -S mingw-w64-x86_64-libpst
   ```

3. **Add to PATH**: Add MSYS2 bin directory to Windows PATH

### Method 3: Cygwin

1. **Install Cygwin** from https://www.cygwin.com/

2. **Select libpst package** during installation

3. **Add to PATH**: Add Cygwin bin directory to Windows PATH

## 🐧 Linux Installation

### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install pst-utils
```

### CentOS/RHEL/Fedora:
```bash
# CentOS/RHEL
sudo yum install libpst

# Fedora
sudo dnf install libpst
```

### Arch Linux:
```bash
sudo pacman -S libpst
```

## 🍎 macOS Installation

### Using Homebrew:
```bash
brew install libpst
```

### Using MacPorts:
```bash
sudo port install libpst
```

## ✅ Verify Installation

Test if readpst is working:

```bash
readpst --version
```

You should see version information if installed correctly.

## 🚀 Using with Advanced MailTool

Once `readpst` is installed and in your PATH:

1. **Start Advanced MailTool**
2. **Open PST file** - it will automatically detect and use readpst
3. **Conversion happens automatically** in the background
4. **MBOX data loads** into the interface

## 🔧 Manual PST Conversion

You can also convert PST files manually:

```bash
# Convert PST to MBOX
readpst -o output_folder -M your_file.pst

# Convert with verbose output
readpst -o output_folder -M -v your_file.pst

# Convert specific folder
readpst -o output_folder -M -f your_file.pst
```

## 📋 Troubleshooting

### "readpst not found"
- Verify installation with `readpst --version`
- Check if readpst is in your PATH
- Restart Advanced MailTool after installation

### "Permission denied"
- Ensure PST file is not open in Outlook
- Copy PST file to a different location
- Run with administrator privileges if needed

### "Conversion failed"
- Check PST file integrity
- Try with a smaller PST file first
- Check available disk space

### WSL Issues
- Ensure WSL is properly installed
- Update WSL: `wsl --update`
- Check Linux distribution is running: `wsl -l -v`

## 💡 Performance Tips

- **Large PST files**: May take several minutes to convert
- **Multiple PST files**: Convert one at a time
- **Disk space**: Ensure 2x PST file size available
- **Memory**: Close other applications during conversion

## 🔗 Alternative Tools

If readpst doesn't work for you:

1. **PST Viewer** (Windows GUI)
2. **MailStore Home** (Free personal use)
3. **Aid4Mail Community** (Limited free version)
4. **Online converters** (For small files)

---

**Note**: readpst is part of the libpst project and is completely free and open source.
