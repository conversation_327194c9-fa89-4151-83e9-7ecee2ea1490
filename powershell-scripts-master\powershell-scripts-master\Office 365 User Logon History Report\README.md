## Export Microsoft 365 Users’ Login History Report using PowerShell
This PowerShell script exports a Microsoft 365 users’ logon history report, enabling administrators to track users’ login activities.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![User Login History Report](https://o365reports.com/wp-content/uploads/2019/12/Export-Office-365-Logon-history-report-1.png?v=1705576842)
## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts.

*View more comprehensive User login history report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/20300/1/20>* 
