# 🔧 PST File Conversion Guide

PST (Personal Storage Table) files are Microsoft's proprietary format for Outlook. Advanced MailTool cannot directly read PST files due to licensing and technical limitations, but here are the best free methods to convert them:

## 🚀 Recommended Free Solutions

### 1️⃣ **Microsoft Outlook (Best Quality)**
If you have Outlook installed:

1. **Open Outlook**
2. **File → Open & Export → Import/Export**
3. **Choose "Export to a file"**
4. **Select format:**
   - For MailTool: Choose "Comma Separated Values" then convert to MBOX
   - Or use third-party MBOX export add-ins

### 2️⃣ **PST Viewer (Free Tool)**
Recommended free tool:

1. **Download PST Viewer** (search "PST Viewer free download")
2. **Open your PST file**
3. **Select emails/folders**
4. **Export → MBOX format**
5. **Open MBOX in Advanced MailTool**

### 3️⃣ **MailStore Home (Free Personal Use)**
Professional tool with free personal license:

1. **Download MailStore Home**
2. **Import PST file**
3. **Export to MBOX or EML format**
4. **Load in Advanced MailTool**

### 4️⃣ **Aid4Mail Community Edition (Free)**
Limited but functional:

1. **Download Aid4Mail Community**
2. **Source: PST file**
3. **Target: MBOX format**
4. **Process and export**

## 🛠️ Command Line Tools

### **readpst (Linux/WSL)**
For technical users:

```bash
# Install on Ubuntu/Debian
sudo apt-get install pst-utils

# Convert PST to MBOX
readpst -o output_folder -M your_file.pst
```

### **libpst (Cross-platform)**
Alternative command line tool:

```bash
# Convert to MBOX format
pst2mbox your_file.pst output.mbox
```

## 🌐 Online Converters

**⚠️ Use with caution for sensitive data**

- **Zamzar.com** - PST to MBOX conversion
- **CloudConvert.com** - Multiple format support
- **Online-Convert.com** - Free with file size limits

## 📋 Step-by-Step: PST Viewer Method

1. **Download PST Viewer**
   - Search "PST Viewer free download"
   - Choose a reputable source
   - Install the software

2. **Open PST File**
   - Launch PST Viewer
   - File → Open PST
   - Select your PST file

3. **Navigate and Select**
   - Browse folder structure
   - Select emails/folders to export
   - Use Ctrl+A to select all

4. **Export to MBOX**
   - Tools → Export
   - Choose MBOX format
   - Select output location
   - Start export process

5. **Open in Advanced MailTool**
   - Launch Advanced MailTool
   - File → Open Files
   - Select the exported MBOX file

## 💡 Tips for Best Results

### **File Size Considerations**
- **Large PST files** (>2GB): Consider splitting before conversion
- **Many emails**: Export in batches to avoid memory issues
- **Attachments**: Ensure they're included in export

### **Format Recommendations**
- **MBOX**: Best for Advanced MailTool compatibility
- **EML**: Good for individual emails
- **MSG**: Outlook-specific, limited support

### **Quality Preservation**
- **Metadata**: Choose tools that preserve date/time
- **Attachments**: Verify attachments are exported
- **Folder structure**: Some tools maintain hierarchy

## 🔍 Troubleshooting

### **"PST file is corrupted"**
1. Use Outlook's built-in repair tool (scanpst.exe)
2. Try different PST viewer tools
3. Consider professional recovery services

### **"Permission denied"**
1. Close Outlook completely
2. Run conversion tool as administrator
3. Copy PST file to different location

### **"Export incomplete"**
1. Check available disk space
2. Try exporting smaller batches
3. Verify PST file integrity

### **"MBOX file won't open"**
1. Check file size (very large files load slowly)
2. Verify MBOX format validity
3. Try opening with text editor to check structure

## 🎯 After Conversion

Once you have MBOX files:

1. **Open Advanced MailTool**
2. **Click "📁 Open Files"**
3. **Select your MBOX files**
4. **Enjoy full functionality:**
   - Advanced search
   - Export capabilities
   - Attachment management
   - Modern interface

## 📞 Need Help?

If you're having trouble with PST conversion:

1. **Check the log files** in conversion tools
2. **Try multiple tools** - different tools work better with different PST files
3. **Consider file age** - very old PST files may need special handling
4. **Backup first** - always keep original PST files safe

---

**Remember**: PST conversion is a one-time process. Once converted to MBOX, you'll have full access to all Advanced MailTool features!
