# Defalut user
Get-Mailbox | ForEach-Object { 

    Get-MailboxFolderPermission (($_.PrimarySmtpAddress.ToString())+":\Calendar") -User Default -ErrorAction SilentlyContinue | Select-Object Identity,User,AccessRights
    Get-MailboxFolderPermission (($_.PrimarySmtpAddress.ToString())+":\Kalender") -User Default -ErrorAction SilentlyContinue | Select-Object Identity,User,AccessRights

} | Out-HtmlView -Title "Default User Calendar Permissions"
