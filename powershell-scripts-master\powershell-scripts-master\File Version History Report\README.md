﻿## **Get File Version History Report**
Learn how to export SharePoint Online file version history report using PowerShell and keep track of all version changes.

***Sample Output***

The exported SharePoint version size report looks like the screenshot below.

![File version history report](https://o365reports.com/wp-content/uploads/2024/06/SharePoint-version-history-report-1024x177.png?v=1718880863)
## **Powerful Microsoft 365 Reporting Tool by AdminDroid**
If this script is helpful, you’ll love [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub)! With over 1800 pre-built reports and dashboards, it’s a fantastic way to dive deeper into your M365 data.

*View more comprehensive SPO file version history reports through AdminDroid:*

[*https://demo.admindroid.com/#/1/11/reports/22601/1/20*](https://demo.admindroid.com/#/1/11/reports/22601/1/20)

