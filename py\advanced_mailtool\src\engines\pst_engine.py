"""
PST format engine with multiple fallback methods
"""

import struct
from pathlib import Path
from typing import Optional, List
import tempfile
import os

from .base_engine import MailEngineBase, ProgressCallback, MailEngineError
from ..core.email_message import EmailMessage, EmailCollection, Attachment
from .portable_pst_handler import PortablePst<PERSON>andler

# Try to import PST libraries
PST_LIBRARIES = {}

# Try win32com (Outlook COM)
try:
    import win32com.client
    PST_LIBRARIES['outlook'] = True
except ImportError:
    PST_LIBRARIES['outlook'] = False

# Try extract-msg for MSG files
try:
    import extract_msg
    PST_LIBRARIES['extract_msg'] = True
except ImportError:
    PST_LIBRARIES['extract_msg'] = False

# PST libraries are limited - focus on conversion guidance


class PstEngine(MailEngineBase):
    """Engine for PST format files with multiple fallback methods"""
    
    def __init__(self):
        super().__init__()
        self.engine_name = "PST Engine"
        self.supported_extensions = ['.pst', '.ost']
        self.available_methods = self._check_available_methods()
    
    def _check_available_methods(self) -> List[str]:
        """Check which PST reading methods are available"""
        methods = []

        # Check for readpst tool
        try:
            import subprocess
            result = subprocess.run(['readpst', '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                methods.append('readpst')
        except (FileNotFoundError, subprocess.TimeoutExpired):
            pass

        if PST_LIBRARIES['outlook']:
            methods.append('outlook')

        methods.append('direct')  # Always available as fallback

        return methods
    
    def can_handle(self, file_path: Path) -> bool:
        """Check if this is a PST file"""
        if file_path.suffix.lower() in self.supported_extensions:
            return True
        
        # Check PST signature
        try:
            with open(file_path, 'rb') as f:
                signature = f.read(4)
                return signature == b'!BDN'
        except Exception:
            return False
    
    def load_messages(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Load messages from PST file using available methods"""
        
        # Validate file
        is_valid, error_msg = self.validate_file(file_path)
        if not is_valid:
            raise MailEngineError(error_msg)
        
        if progress:
            progress.update("Analyzing PST file...", 0, 0)
        
        # Get PST file info
        pst_info = self._analyze_pst_file(file_path)
        self.log_info(f"PST file info: {pst_info}")
        
        # Try different methods in order of preference
        last_error = None

        for method in self.available_methods:
            try:
                if progress:
                    progress.update(f"Trying {method} method...", 0, 0)

                if method == 'readpst':
                    return self._try_readpst_conversion(file_path, progress)
                elif method == 'outlook':
                    return self._load_with_outlook(file_path, progress)
                elif method == 'direct':
                    return self._load_with_direct_analysis(file_path, progress)
                
            except Exception as e:
                self.log_warning(f"Method {method} failed: {e}")
                last_error = e
                continue
        
        # If all methods failed, provide helpful error message
        error_msg = self._create_helpful_error_message(file_path, last_error)
        raise MailEngineError(error_msg)
    
    def save_messages(self, messages: EmailCollection, file_path: Path, 
                     progress: Optional[ProgressCallback] = None) -> bool:
        """Save messages to PST file (not supported - suggest alternatives)"""
        raise MailEngineError(
            "Saving to PST format is not supported. "
            "Please export to MBOX, EML, or MSG format instead."
        )
    
    def _analyze_pst_file(self, file_path: Path) -> dict:
        """Analyze PST file structure"""
        info = {
            'valid_pst': False,
            'version': 'unknown',
            'size_mb': 0.0,
            'encrypted': False,
            'readable': False
        }
        
        try:
            with open(file_path, 'rb') as f:
                # Read PST header
                signature = f.read(4)
                if signature != b'!BDN':
                    return info
                
                info['valid_pst'] = True
                
                # Read version
                f.seek(10)
                version_bytes = f.read(2)
                version = struct.unpack('<H', version_bytes)[0]
                
                if version == 14:
                    info['version'] = 'ANSI (Outlook 97-2002)'
                elif version == 23:
                    info['version'] = 'Unicode (Outlook 2003+)'
                else:
                    info['version'] = f'Unknown ({version})'
                
                # File size
                info['size_mb'] = file_path.stat().st_size / (1024 * 1024)
                
                # Try to read more to check if accessible
                f.seek(0)
                f.read(1024)  # Try to read first KB
                info['readable'] = True
                
        except PermissionError:
            info['readable'] = False
        except Exception as e:
            self.log_warning(f"Error analyzing PST file: {e}")
        
        return info
    
    def _load_with_outlook(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Load PST using Outlook COM interface"""
        if not PST_LIBRARIES['outlook']:
            raise MailEngineError("Outlook COM interface not available")
        
        collection = EmailCollection()
        
        try:
            if progress:
                progress.update("Connecting to Outlook...", 0, 0)
            
            # Connect to Outlook
            outlook = win32com.client.Dispatch("Outlook.Application")
            namespace = outlook.GetNamespace("MAPI")
            
            # Add PST to Outlook
            try:
                namespace.AddStore(str(file_path))
            except Exception:
                pass  # Might already be added
            
            # Find the PST store
            pst_store = None
            for store in namespace.Stores:
                if store.FilePath and Path(store.FilePath).resolve() == file_path.resolve():
                    pst_store = store
                    break
            
            if not pst_store:
                raise MailEngineError("Could not find PST file in Outlook stores")
            
            # Process folders
            root_folder = pst_store.GetRootFolder()
            total_items = self._count_items_recursive(root_folder)
            
            if progress:
                progress.update(f"Processing {total_items} items...", 0, total_items)
            
            processed = [0]  # Use list for mutable reference
            
            def process_folder(folder, depth=0):
                if depth > 20:  # Prevent infinite recursion
                    return
                
                try:
                    for item in folder.Items:
                        if hasattr(item, 'Class') and item.Class == 43:  # MailItem
                            message = self._outlook_item_to_message(item, file_path)
                            if message:
                                collection.add_message(message)
                            
                            processed[0] += 1
                            if progress and processed[0] % 10 == 0:
                                progress.update(f"Processed {processed[0]}/{total_items} items...", 
                                              processed[0], total_items)
                    
                    # Process subfolders
                    for subfolder in folder.Folders:
                        process_folder(subfolder, depth + 1)
                        
                except Exception as e:
                    self.log_warning(f"Error processing folder: {e}")
            
            process_folder(root_folder)
            
            if progress:
                progress.update(f"Completed: {len(collection)} messages loaded", 
                              len(collection), len(collection))
            
            self.log_info(f"Successfully loaded {len(collection)} messages using Outlook")
            return collection
            
        except Exception as e:
            raise MailEngineError(f"Outlook method failed: {e}")
    
    def _count_items_recursive(self, folder, depth=0) -> int:
        """Count total items in folder tree"""
        if depth > 20:
            return 0
        
        count = 0
        try:
            count += len([item for item in folder.Items if hasattr(item, 'Class') and item.Class == 43])
            for subfolder in folder.Folders:
                count += self._count_items_recursive(subfolder, depth + 1)
        except Exception:
            pass
        
        return count
    
    def _outlook_item_to_message(self, item, source_file: Path) -> Optional[EmailMessage]:
        """Convert Outlook item to EmailMessage"""
        try:
            subject = getattr(item, 'Subject', '') or '(No Subject)'
            sender = getattr(item, 'SenderEmailAddress', '') or getattr(item, 'SenderName', '')
            sender_name = getattr(item, 'SenderName', '')
            
            # Recipients
            recipients = []
            if hasattr(item, 'Recipients'):
                for recipient in item.Recipients:
                    if hasattr(recipient, 'Address'):
                        recipients.append(recipient.Address)
            
            # Date
            date = getattr(item, 'ReceivedTime', None)
            
            # Body
            body_text = getattr(item, 'Body', '') or ''
            body_html = getattr(item, 'HTMLBody', '') or ''
            
            # Message ID
            message_id = getattr(item, 'EntryID', '')
            
            # Attachments
            attachments = []
            if hasattr(item, 'Attachments'):
                for att in item.Attachments:
                    try:
                        filename = getattr(att, 'FileName', 'attachment')
                        size = getattr(att, 'Size', 0)
                        
                        attachment = Attachment(
                            filename=filename,
                            size=size,
                            data=None  # Don't load data by default for performance
                        )
                        attachments.append(attachment)
                    except Exception:
                        continue
            
            message = EmailMessage(
                subject=subject,
                sender=sender,
                sender_name=sender_name,
                recipients=recipients,
                body_text=body_text,
                body_html=body_html,
                message_id=message_id,
                date=date,
                source_file=str(source_file),
                source_format='pst',
                attachments=attachments
            )
            
            return message
            
        except Exception as e:
            self.log_warning(f"Error converting Outlook item: {e}")
            return None
    
    def _try_readpst_conversion(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Try to use readpst tool for PST conversion"""
        import subprocess
        import tempfile
        import shutil

        collection = EmailCollection()

        try:
            # Check if readpst is available
            result = subprocess.run(['readpst', '--version'],
                                  capture_output=True, text=True, timeout=5)

            if result.returncode != 0:
                raise FileNotFoundError("readpst not available")

            if progress:
                progress.update("Converting PST with readpst...", 0, 0)

            # Create temporary directory
            with tempfile.TemporaryDirectory() as temp_dir:
                # Convert PST to MBOX
                cmd = ['readpst', '-o', temp_dir, '-M', str(file_path)]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

                if result.returncode != 0:
                    raise RuntimeError(f"readpst failed: {result.stderr}")

                # Look for generated MBOX files
                temp_path = Path(temp_dir)
                mbox_files = list(temp_path.glob("*.mbox"))

                if not mbox_files:
                    raise RuntimeError("No MBOX files generated")

                # Load the MBOX files
                from .mbox_engine import MboxEngine
                mbox_engine = MboxEngine()

                for mbox_file in mbox_files:
                    if progress:
                        progress.update(f"Loading {mbox_file.name}...", 0, 0)

                    mbox_collection = mbox_engine.load_messages(mbox_file, progress)
                    collection.add_messages(mbox_collection.messages)

                if progress:
                    progress.update(f"Loaded {len(collection)} messages from PST",
                                  len(collection), len(collection))

                return collection

        except (FileNotFoundError, subprocess.TimeoutExpired, RuntimeError) as e:
            # Fall back to guidance message
            return self._load_with_direct_analysis(file_path, progress)
        except Exception as e:
            self.log_warning(f"readpst conversion failed: {e}")
            return self._load_with_direct_analysis(file_path, progress)

    def _load_with_direct_analysis(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Direct PST analysis with portable conversion solution"""
        collection = EmailCollection()

        if progress:
            progress.update("Analyzing PST file...", 0, 0)

        # Use portable PST handler
        portable_handler = PortablePstHandler()
        pst_info = portable_handler.analyze_pst_file(file_path)

        # Create conversion package
        try:
            if progress:
                progress.update("Creating portable conversion package...", 0, 1)

            package_dir = portable_handler.create_conversion_package(
                file_path,
                file_path.parent
            )

            conversion_guide = self._create_portable_guide(file_path, pst_info, package_dir)

        except Exception as e:
            self.log_warning(f"Failed to create conversion package: {e}")
            conversion_guide = self._create_simple_guide(file_path, pst_info)

        # Create helpful information message
        info_message = EmailMessage(
            subject="📧 PST File - Portable Conversion Kit Created",
            sender="advanced-mailtool@system",
            sender_name="Advanced MailTool System",
            recipients=["user@system"],
            body_text=conversion_guide,
            source_file=str(file_path),
            source_format='pst'
        )

        collection.add_message(info_message)

        if progress:
            progress.update("Portable conversion kit ready", 1, 1)

        return collection

    def _create_conversion_guide(self, file_path: Path, pst_info: dict) -> str:
        """Create detailed conversion guide"""

        guide = f"""
🔧 PST File Conversion Guide
============================

📁 File: {file_path.name}
📊 Size: {pst_info.get('size_mb', 0):.1f} MB
📋 Format: {pst_info.get('version', 'Unknown')}
✅ Valid PST: {pst_info.get('valid_pst', False)}

⚠️  DIRECT PST READING LIMITATIONS
Advanced MailTool cannot directly read PST files due to Microsoft's proprietary format.

🚀 RECOMMENDED SOLUTIONS

1️⃣ OUTLOOK METHOD (Best Quality)
   • Open Microsoft Outlook
   • File → Open & Export → Import/Export
   • Choose "Export to a file"
   • Select "Outlook Data File (.pst)" or "Comma Separated Values"
   • For MailTool: Choose "Export to MBOX" if available

2️⃣ FREE CONVERSION TOOLS
   • PST Viewer (free download)
   • Aid4Mail Community Edition
   • MailStore Home (free for personal use)

3️⃣ ONLINE CONVERTERS
   • Zamzar.com (PST to MBOX)
   • CloudConvert.com
   • Online-Convert.com

4️⃣ COMMAND LINE TOOLS
   • readpst (Linux/Windows with WSL)
   • libpst utilities

📋 STEP-BY-STEP CONVERSION

Using PST Viewer (Recommended):
1. Download PST Viewer (free)
2. Open your PST file
3. Select all emails
4. Export → MBOX format
5. Open the MBOX file in Advanced MailTool

Using Outlook:
1. Open Outlook
2. File → Open & Export → Open Outlook Data File
3. Select your PST file
4. Right-click folder → Export
5. Choose MBOX or EML format

💡 TIPS FOR BEST RESULTS
• Convert to MBOX for full compatibility
• EML format works for individual emails
• Large PST files may need to be split
• Keep original PST as backup

🔗 HELPFUL LINKS
• PST Viewer: Search "PST Viewer free download"
• Outlook Export Guide: Microsoft Support
• MBOX Format Info: Wikipedia "Mbox"

After conversion, use "📁 Open Files" to load your MBOX files!
        """.strip()

        return guide

    def _create_portable_guide(self, file_path: Path, pst_info: dict, package_dir: Path) -> str:
        """Create guide for portable conversion solution"""

        guide = f"""
🎉 PORTABLE PST CONVERSION KIT CREATED!
=====================================

📁 Your PST file: {file_path.name}
📊 Size: {pst_info.get('size_mb', 0):.1f} MB
📋 Format: {pst_info.get('version', 'Unknown')}
📧 Estimated emails: ~{pst_info.get('estimated_emails', 0)}

🚀 CONVERSION KIT LOCATION
📂 {package_dir}

This portable kit contains:
✅ Your PST file (copy)
✅ Detailed conversion instructions
✅ Tool recommendations
✅ Windows batch script
✅ Troubleshooting guide

🎯 NEXT STEPS

1️⃣ OPEN THE CONVERSION KIT
   • Navigate to: {package_dir}
   • Double-click: convert_pst.bat (Windows)
   • Or read: CONVERSION_INSTRUCTIONS.md

2️⃣ FOLLOW THE GUIDE
   • Download recommended tool (PST Viewer)
   • Convert PST to MBOX format
   • Open MBOX in Advanced MailTool

3️⃣ PORTABLE SOLUTION
   • Copy entire kit to any computer
   • No installation required
   • Works on Windows/Mac/Linux

💡 WHY THIS APPROACH?

✅ **Portable**: Works on any computer
✅ **No dependencies**: No external tools required
✅ **Step-by-step**: Clear instructions included
✅ **Multiple options**: Several conversion methods
✅ **Backup included**: Original PST safely copied

🔧 QUICK START

Windows users:
1. Open: {package_dir}
2. Double-click: convert_pst.bat
3. Follow the instructions

All users:
1. Read: CONVERSION_INSTRUCTIONS.md
2. Download PST Viewer (free)
3. Convert to MBOX format
4. Open MBOX in Advanced MailTool

📞 NEED HELP?

Check the included files:
• CONVERSION_INSTRUCTIONS.md - Detailed guide
• RECOMMENDED_TOOLS.json - Tool information
• convert_pst.bat - Windows helper script

After conversion, you'll have full access to:
• Advanced search and filtering
• Export capabilities
• Attachment management
• Modern interface

🎯 This solution is completely portable and works on any computer!
        """.strip()

        return guide

    def _create_simple_guide(self, file_path: Path, pst_info: dict) -> str:
        """Create simple conversion guide if package creation fails"""

        guide = f"""
📧 PST FILE CONVERSION REQUIRED
==============================

📁 File: {file_path.name}
📊 Size: {pst_info.get('size_mb', 0):.1f} MB

🚀 RECOMMENDED SOLUTION: PST Viewer

1️⃣ DOWNLOAD PST VIEWER (FREE)
   • Search "PST Viewer free download"
   • Choose reputable source
   • Install software

2️⃣ CONVERT TO MBOX
   • Open PST Viewer
   • File → Open → {file_path.name}
   • Select all emails (Ctrl+A)
   • Tools → Export → MBOX format
   • Save as: {file_path.stem}.mbox

3️⃣ OPEN IN ADVANCED MAILTOOL
   • File → Open Files
   • Select the .mbox file
   • Enjoy full functionality!

💡 ALTERNATIVE TOOLS
• MailStore Home (free personal)
• Aid4Mail Community (limited free)
• Online converters (small files only)

This approach ensures maximum compatibility and portability!
        """.strip()

        return guide
    
    def _create_helpful_error_message(self, file_path: Path, last_error: Exception) -> str:
        """Create helpful error message with suggestions"""
        pst_info = self._analyze_pst_file(file_path)
        
        message = f"Could not load PST file: {file_path.name}\n\n"
        
        if not pst_info['readable']:
            message += "❌ File access error - check permissions\n"
        elif not pst_info['valid_pst']:
            message += "❌ Not a valid PST file\n"
        else:
            message += f"✅ Valid PST file ({pst_info['version']})\n"
            message += f"📊 Size: {pst_info['size_mb']:.1f} MB\n\n"
        
        message += "🔧 Solutions:\n"
        
        if not PST_LIBRARIES['outlook']:
            message += "1. Install Microsoft Outlook for full PST support\n"
        
        message += "2. Convert PST to MBOX format:\n"
        message += "   • Use Outlook: File → Export → MBOX\n"
        message += "   • Use PST Viewer (free tool)\n"
        message += "   • Use online converters\n\n"
        
        message += "3. Alternative formats supported:\n"
        message += "   • MBOX files (full support)\n"
        message += "   • EML files (individual emails)\n"
        message += "   • MSG files (Outlook messages)\n"
        
        if last_error:
            message += f"\nTechnical error: {last_error}"
        
        return message
