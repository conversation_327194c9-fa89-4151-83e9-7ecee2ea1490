## Export Microsoft 365 License Cost Report Using PowerShell

Export Microsoft 365 license cost report with PowerShell. Optimize costs, track usage, and manage licenses effectively.

***Sample Output:***

This script exports a report of Microsoft 365 users' license usage and cost to an output CSV file, which looks like the screenshot below.

![Microsoft 365 License Cost Report](https://o365reports.com/wp-content/uploads/2024/06/M365-Users-License-Cost-Report-1024x207.png?v=**********)

## Microsoft 365 Reporting tool by AdminDroid

Get detailed insights into Microsoft 365 users' license usage using [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ pre-built reports and comprehensive dashboards.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com>*
