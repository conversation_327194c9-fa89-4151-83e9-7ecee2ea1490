﻿## Find Inactive Distribution Lists Using PowerShell
Download this handy PowerShell script to identify inactive distribution lists in the organization using message trace report.

***Sample Output:***

The script exports an output CSV file that looks similar to the screenshot below.

![Microsoft 365 Inactive Distribution Lists](https://o365reports.com/wp-content/uploads/2024/07/Microsoft-365-Inactive-Distribution-Lists-565x233.png?v=1720514729)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive Distribution groups reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/70021/1/20](https://demo.admindroid.com/#/1/11/reports/70021/1/20)*



