﻿# How to Export All Risky Users in Microsoft Entra

Use the PowerShell script to list all risky users in Microsoft Entra for early risk detection, proactive remediation, and enhanced identity protection.

***Sample Output:***

The script exports all risky users in your organization to a CSV file. A sample email is shown in the screenshots below.

![Risky Users Report](<https://o365reports.com/wp-content/uploads/2025/05/2025-05-20-12_04_00-M365_Risky_Users_Report2025-May-20-Tue-10-28-55-AM-Excel-1024x258.png?v=1747729210>)
###
### Microsoft 365 Reporting tool by AdminDroid
Stay one step ahead of identity threats with [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub). Gain real-time visibility into every action happening in your organization with 1900+ reports and 30+ interactive dashboards.

*Explore detailed risky sign-in analytics reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/20366/1/20>*


