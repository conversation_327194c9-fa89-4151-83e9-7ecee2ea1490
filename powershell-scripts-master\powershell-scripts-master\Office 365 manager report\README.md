## Export Office 365 User Manager and Direct Reports Using PowerShell
This PowerShell script exports 10+ Office 365 user manager reports and direct reports to CSV file along with the most required attributes.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Office 365 manager report](https://o365reports.com/wp-content/uploads/2021/07/All34.png?v=1705576681)
## Microsoft 365 Reporting Tool by AdminDroid
Looking for comprehensive reports? The [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) features over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts.

*View more comprehensive Office 365 manager report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/20/1/20>* 

