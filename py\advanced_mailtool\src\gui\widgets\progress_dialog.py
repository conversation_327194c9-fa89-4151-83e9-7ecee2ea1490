"""
Progress dialog for long-running operations
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QProgressBar, QPushButton, QTextEdit)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont


class ProgressDialog(QDialog):
    """Modern progress dialog with cancellation support"""
    
    cancelled = Signal()
    
    def __init__(self, title: str = "Processing", parent=None):
        super().__init__(parent)
        
        self.setWindowTitle(title)
        self.setModal(True)
        self.setMinimumSize(400, 200)
        self.resize(500, 300)
        
        self.setup_ui()
        
        # Auto-close timer
        self.auto_close_timer = QTimer()
        self.auto_close_timer.setSingleShot(True)
        self.auto_close_timer.timeout.connect(self.accept)
    
    def setup_ui(self):
        """Setup the user interface"""
        
        layout = QVBoxLayout(self)
        layout.setSpacing(16)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        self.title_label = QLabel("Processing...")
        self.title_label.setProperty("class", "header")
        layout.addWidget(self.title_label)
        
        # Status message
        self.status_label = QLabel("Initializing...")
        self.status_label.setProperty("class", "subheader")
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # Details (expandable)
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(100)
        self.details_text.setVisible(False)
        layout.addWidget(self.details_text)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.details_btn = QPushButton("Show Details")
        self.details_btn.setProperty("class", "secondary")
        self.details_btn.clicked.connect(self.toggle_details)
        button_layout.addWidget(self.details_btn)
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.cancel_operation)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def update_progress(self, message: str, current: int = 0, total: int = 0):
        """Update progress display"""
        
        self.status_label.setText(message)
        
        if total > 0:
            self.progress_bar.setRange(0, total)
            self.progress_bar.setValue(current)
            percentage = int((current / total) * 100)
            self.setWindowTitle(f"Processing... ({percentage}%)")
        else:
            # Indeterminate progress
            self.progress_bar.setRange(0, 0)
            self.setWindowTitle("Processing...")
        
        # Add to details
        self.add_detail(message)
        
        # Process events to keep UI responsive
        self.parent().app.processEvents() if self.parent() else None
    
    def add_detail(self, message: str):
        """Add detail message"""
        
        current_text = self.details_text.toPlainText()
        if current_text:
            current_text += "\n"
        current_text += message
        
        self.details_text.setPlainText(current_text)
        
        # Scroll to bottom
        cursor = self.details_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.details_text.setTextCursor(cursor)
    
    def toggle_details(self):
        """Toggle details visibility"""
        
        if self.details_text.isVisible():
            self.details_text.setVisible(False)
            self.details_btn.setText("Show Details")
            self.resize(self.width(), 200)
        else:
            self.details_text.setVisible(True)
            self.details_btn.setText("Hide Details")
            self.resize(self.width(), 350)
    
    def cancel_operation(self):
        """Cancel the operation"""
        
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.setText("Cancelling...")
        self.status_label.setText("Cancelling operation...")
        
        self.cancelled.emit()
    
    def set_completed(self, message: str = "Operation completed successfully"):
        """Mark operation as completed"""
        
        self.status_label.setText(message)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(100)
        self.setWindowTitle("Completed")
        
        self.cancel_btn.setText("Close")
        self.cancel_btn.clicked.disconnect()
        self.cancel_btn.clicked.connect(self.accept)
        
        # Auto-close after 2 seconds
        self.auto_close_timer.start(2000)
    
    def set_error(self, message: str):
        """Mark operation as failed"""
        
        self.status_label.setText(f"Error: {message}")
        self.setWindowTitle("Error")
        
        self.cancel_btn.setText("Close")
        self.cancel_btn.clicked.disconnect()
        self.cancel_btn.clicked.connect(self.reject)
        
        # Show details by default on error
        if not self.details_text.isVisible():
            self.toggle_details()
    
    def closeEvent(self, event):
        """Handle close event"""
        
        # Stop auto-close timer
        self.auto_close_timer.stop()
        
        # If operation is still running, emit cancelled signal
        if self.cancel_btn.text() == "Cancel":
            self.cancelled.emit()
        
        event.accept()
