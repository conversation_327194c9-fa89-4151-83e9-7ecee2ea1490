﻿## Find All External Members in Teams Channels

Execute this PowerShell script to list all Teams Channels with external members, ensuring secure external collaboration in Microsoft Teams.

***Sample Output***

The script exports two CSV files: a detailed report showing external users in channels and a summary report with guest user counts across all channels. The sample output is shown in the screenshot below.

**Detailed report: Teams Channels and their external user details**

![Team Channels with external users report](<https://o365reports.com/wp-content/uploads/2025/02/teams-channel-with-external-user-800x281.png?v=1738651944>)

**Summary report: Teams Channels and their guest users count**

![Team Channels with external members report](<https://o365reports.com/wp-content/uploads/2025/02/teams-channel-with-guest-users.png?v=1738651946>)

## Microsoft 365 Reporting tool by AdminDroid 
Take your Microsoft 365 data management to the next level with the [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1900+ pre-built reports and dashboards for in-depth analysis and efficient oversight. 



*Unlock comprehensive Microsoft teams channels reports with AdminDroid: [https://demo.admindroid.com/#/1/11/reports/60077/1/20*](https://demo.admindroid.com/#/1/11/reports/60077/1/20)*




