﻿## **List Microsoft 365 Users Direct Membership Using PowerShell**
List Microsoft 365 user’s direct membership, admin roles assigned & administrative units (AU) in Entra ID they are members of using PowerShell.

***Sample Output:***

The script exports 3 different CSV reports detailing the user’s direct memberships in Microsoft 365.

**Users’ direct group membership report:**

![Direct Group Membership Report](https://o365reports.com/wp-content/uploads/2024/08/Users-Direct-Group-Membership-Report-768x247.png?v=1723007981)

**Users with admin roles:**

![Users with admin roles report](https://o365reports.com/wp-content/uploads/2024/08/DirectoryRoles\_Membership\_Report-768x220.png?v=1723008007)

**Users and their administrative units:**

![Users and their administrative report](https://o365reports.com/wp-content/uploads/2024/08/AdministrativeUnits\_Membership\_Report-768x218.png?v=1723008033)

## **Microsoft 365 Reporting Tool by AdminDroid**
Wondering where your users are within Microsoft 365? With [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), you can explore over 1900+ pre-built reports & 20+ interactive dashboards to explore your data in depth. Discover user locations and more with AdminDroid!

*View more comprehensive insights on group memberships through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/1006/1/20>*



