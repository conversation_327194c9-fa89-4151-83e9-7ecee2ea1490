## Export Microsoft 365 Group Report to CSV Using PowerShell
This Microsoft 365 group report script exports all Microsoft 365 groups such as distribution list, security group, unified groups and their members to CSV.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Microsoft 365 Group Report](https://o365reports.com/wp-content/uploads/2021/02/Microsoft-365-group-report.png?v=1705576756)
## Microsoft 365 Reporting Tool by AdminDroid 
For a deeper dive beyond this script, check out [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), featuring over 1800 ready-to-use reports and comprehensive dashboards.

*Export Microsoft 365 Group reports to CSV effortlessly with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/80041/1/20>*