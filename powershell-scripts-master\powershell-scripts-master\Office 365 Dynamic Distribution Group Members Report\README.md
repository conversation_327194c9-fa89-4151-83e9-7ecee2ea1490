## Export Dynamic Distribution Group members to CSV

This PowerShell script gets Office 365 Dynamic Distribution Group members and exports them to CSV. The script generates both DDL group and members report.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below
![Dynamic Distribution Group](https://o365reports.com/wp-content/uploads/2019/03/Dynamic-distribution-group-members-report-2.png?v=1705576661)
## Microsoft 365 Reporting Tool by AdminDroid 

[AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) unlocks deep insights with 1800+ pre-built Microsoft 365 reports and insightful dashboards.

*Generate detailed reports for Office 365 Dynamic distribution group members using AdminDroid:<https://demo.admindroid.com/#/1/11/reports/10721/1/20>*
