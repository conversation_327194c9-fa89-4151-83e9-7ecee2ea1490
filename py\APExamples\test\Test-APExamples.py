# === IMPORTS & INITIAL SETUP ===
import sys
import os
import json
import csv
import datetime
import re
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                              QLabel, QPushButton, QTabWidget, QFrame, QTreeWidget, QTreeWidgetItem,
                              QTextEdit, QLineEdit, QMenu, QDialog, QMessageBox, QInputDialog,
                              QScrollArea, QGroupBox, QSplitter, QFileDialog, QComboBox, QCheckBox,
                              QListWidget, QListWidgetItem, QToolBar, QToolButton)
from PySide6.QtCore import Qt, QTimer, Signal, Slot, QSize, QPoint, QSettings, QEvent, QObject
from PySide6.QtGui import QIcon, QAction, QActionGroup, QColor, QFont, QClipboard, QPixmap, QPainter, QKeySequence, QTextCharFormat, QTextCursor, QShortcut, QSyntaxHighlighter
from PySide6.QtWidgets import QApplication
from PIL import Image

# Bestem sti til json-datafil
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
DATAFILE = os.path.join(SCRIPT_DIR, "commands.json")
HISTORYFILE = os.path.join(SCRIPT_DIR, "command_history.json")
SETTINGSFILE = os.path.join(SCRIPT_DIR, "settings.json")
SEARCH_HISTORY_MAX = 20
COMMAND_HISTORY_MAX = 50
AUTO_SAVE_INTERVAL = 60000  # 60 sekunder

# === DATAHÅNDTERING ===
def load_categories():
    """Indlæs kategorier og kommandoer fra JSON-fil."""
    if os.path.exists(DATAFILE):
        with open(DATAFILE, "r", encoding="utf-8") as f:
            data = json.load(f)
            if isinstance(data, dict):
                # Konverter gamle string-kommandoer til dict-format
                for category, commands in data.items():
                    if commands and isinstance(commands[0], str):
                        data[category] = [{"text": cmd, "favorite": False} for cmd in commands]

                # Hvis der er en gemt rækkefølge, brug den
                if os.path.exists(SETTINGSFILE):
                    try:
                        with open(SETTINGSFILE, "r", encoding="utf-8") as settings_file:
                            settings_data = json.load(settings_file)
                            tab_order = settings_data.get("tab_order", [])
                            if tab_order:
                                # Reorganiser data i den gemte rækkefølge
                                ordered_data = {}
                                # Først kategorier i den gemte rækkefølge
                                for category in tab_order:
                                    if category in data:
                                        ordered_data[category] = data[category]
                                # Så eventuelle nye kategorier
                                for category, commands in data.items():
                                    if category not in ordered_data:
                                        ordered_data[category] = commands
                                return ordered_data
                    except:
                        pass
            return data
    else:
        return {}

def save_categories(categories):
    """Gem kategorier og kommandoer til JSON-fil."""
    with open(DATAFILE, "w", encoding="utf-8") as f:
        json.dump(categories, f, indent=2, ensure_ascii=False)

    # Gem også rækkefølgen af kategorier i settings
    if categories:
        current_settings = load_settings()
        current_settings["tab_order"] = list(categories.keys())
        save_settings(current_settings)

# Indlæs data ved opstart
categories = load_categories()

# Historik til undo-funktionalitet
action_history = []
MAX_HISTORY = 10

# Indlæs søgehistorik
def load_search_history():
    """Indlæs søgehistorik fra JSON-fil."""
    if os.path.exists(HISTORYFILE):
        try:
            with open(HISTORYFILE, "r", encoding="utf-8") as f:
                data = json.load(f)
                return data.get("search_history", []), data.get("command_history", [])
        except:
            return [], []
    return [], []

def save_search_history(search_history, command_history):
    """Gem søgehistorik til JSON-fil."""
    with open(HISTORYFILE, "w", encoding="utf-8") as f:
        json.dump({
            "search_history": search_history,
            "command_history": command_history
        }, f, indent=2, ensure_ascii=False)

# Indlæs indstillinger
def load_settings():
    """Indlæs brugerindstillinger fra JSON-fil."""
    default_settings = {
        "theme": "dark",
        "auto_save": True,
        "highlight_search": True,
        "sort_alphabetically": False,
        "tab_order": [],  # Gem rækkefølgen af faner
        "group_by_verb": True,  # Gruppér kommandoer efter verbs
        "powershell_syntax": True  # PowerShell syntaksfremhævning
    }
    if os.path.exists(SETTINGSFILE):
        try:
            with open(SETTINGSFILE, "r", encoding="utf-8") as f:
                data = json.load(f)
                # Opdater default med gemte indstillinger
                default_settings.update(data)
        except:
            pass
    return default_settings

def save_settings(settings):
    """Gem brugerindstillinger til JSON-fil."""
    with open(SETTINGSFILE, "w", encoding="utf-8") as f:
        json.dump(settings, f, indent=2, ensure_ascii=False)

# Indlæs historik og indstillinger
search_history, command_history = load_search_history()
settings = load_settings()

# === UTILITY FUNCTIONS ===
def extract_verb_from_command(command_text):
    """Udtræk verb fra PowerShell kommando (Get, Set, Add, osv.)"""
    if not command_text:
        return "Other"

    # Fjern eventuelle parenteser i starten som (Get-Mailbox -> Get-Mailbox
    cleaned_text = command_text.strip()
    if cleaned_text.startswith('('):
        cleaned_text = cleaned_text[1:]

    # Find første ord efter eventuelle bindestreger eller mellemrum
    parts = cleaned_text.split()
    if not parts:
        return "Other"

    first_word = parts[0]

    # Fjern eventuelle bindestreger i starten
    if first_word.startswith('-'):
        first_word = first_word[1:]

    # Find verb-del (før første bindestreg)
    if '-' in first_word:
        verb = first_word.split('-')[0]
    else:
        verb = first_word

    # Almindelige PowerShell verbs
    common_verbs = [
        'Get', 'Set', 'Add', 'Remove', 'New', 'Start', 'Stop', 'Restart',
        'Enable', 'Disable', 'Install', 'Uninstall', 'Update', 'Import',
        'Export', 'Copy', 'Move', 'Test', 'Invoke', 'Connect', 'Disconnect',
        'Select', 'Where', 'Sort', 'Group', 'Measure', 'Compare', 'Format',
        'Out', 'Write', 'Read', 'Clear', 'Show', 'Hide', 'Find', 'Search',
        'Restore'  # Tilføjet Restore
    ]

    # Check om det er et kendt verb
    for known_verb in common_verbs:
        if verb.lower() == known_verb.lower():
            return known_verb

    # Hvis ikke kendt, returner første bogstav som stort
    return verb.capitalize() if verb else "Other"

def group_commands_by_verb(commands):
    """Gruppér kommandoer efter deres verb"""
    grouped = {}
    for cmd in commands:
        if isinstance(cmd, dict):
            cmd_text = cmd.get("text", "")
            verb = extract_verb_from_command(cmd_text)
            if verb not in grouped:
                grouped[verb] = []
            grouped[verb].append(cmd)

    # Sortér grupper alfabetisk
    return dict(sorted(grouped.items()))

def create_command_hash(commands):
    """Opret hash for kommandoliste til caching"""
    import hashlib
    command_texts = [cmd.get("text", "") for cmd in commands if isinstance(cmd, dict)]
    return hashlib.md5("|".join(sorted(command_texts)).encode()).hexdigest()

# === SYNTAX HIGHLIGHTER ===
class PowerShellSyntaxHighlighter(QSyntaxHighlighter):
    def __init__(self, parent=None, theme="dark"):
        super().__init__(parent)
        self.theme = theme
        self.setup_formats()

    def setup_formats(self):
        """Opsæt formatering for forskellige syntaks-elementer."""
        if self.theme == "dark":
            # Mørkt tema farver
            colors = {
                'cmdlet': QColor("#4FC1FF"),      # Lys cyan for cmdlets
                'parameter': QColor("#9CDCFE"),   # Lys blå for parametre
                'string': QColor("#FFB86C"),      # Lys orange for strenge
                'variable': QColor("#50FA7B"),    # Lys grøn for variabler
                'comment': QColor("#6272A4"),     # Blå-grå for kommentarer
                'operator': QColor("#FF79C6"),    # Pink for operatorer
                'number': QColor("#BD93F9"),      # Lilla for tal
                'keyword': QColor("#FF5555"),     # Rød for keywords
                'default': QColor("#F8F8F2")      # Lys grå for standard tekst
            }
        else:
            # Lyst tema farver
            colors = {
                'cmdlet': QColor("#0066CC"),      # Mørk blå for cmdlets
                'parameter': QColor("#0080FF"),   # Blå for parametre
                'string': QColor("#D2691E"),      # Orange for strenge
                'variable': QColor("#228B22"),    # Grøn for variabler
                'comment': QColor("#008000"),     # Grøn for kommentarer
                'operator': QColor("#800080"),    # Lilla for operatorer
                'number': QColor("#9932CC"),      # Mørk lilla for tal
                'keyword': QColor("#FF0000"),     # Rød for keywords
                'default': QColor("#000000")      # Sort for standard tekst
            }

        # Opret formatering
        self.formats = {}
        for name, color in colors.items():
            format = QTextCharFormat()
            format.setForeground(color)
            if name in ['cmdlet', 'keyword']:
                format.setFontWeight(QFont.Bold)
            self.formats[name] = format

    def update_theme(self, theme):
        """Opdater tema og genopfrisk formatering."""
        self.theme = theme
        self.setup_formats()
        self.rehighlight()

    def highlightBlock(self, text):
        """Fremhæv en tekstblok (påkrævet af QSyntaxHighlighter)."""
        if not text:
            return

        # Regex patterns for forskellige elementer
        patterns = [
            # Kommentarer (# til slutning af linje)
            (r'#.*$', 'comment'),

            # Strenge (dobbelt og enkelt anførselstegn)
            (r'"[^"]*"', 'string'),
            (r"'[^']*'", 'string'),

            # Variabler ($variabel)
            (r'\$\w+', 'variable'),

            # Parametre (-Parameter)
            (r'-\w+', 'parameter'),

            # Tal
            (r'\b\d+\.?\d*\b', 'number'),

            # PowerShell cmdlets (Verb-Noun pattern)
            (r'\b(?:Get|Set|New|Remove|Add|Clear|Copy|Move|Rename|Start|Stop|Restart|Enable|Disable|Install|Uninstall|Import|Export|Select|Where|Sort|Group|Measure|Compare|Format|Out|Write|Read|Test|Invoke|Connect|Disconnect|Show|Hide|Find|Search|Restore|Backup|Update|Sync)-\w+\b', 'cmdlet'),

            # PowerShell keywords
            (r'\b(?:if|else|elseif|switch|foreach|for|while|do|try|catch|finally|throw|return|break|continue|function|filter|param|begin|process|end|class|enum|using|namespace|workflow|configuration)\b', 'keyword'),

            # PowerShell operatorer
            (r'\b(?:-eq|-ne|-lt|-le|-gt|-ge|-like|-notlike|-match|-notmatch|-contains|-notcontains|-in|-notin|-replace|-split|-join|-is|-isnot|-as|-and|-or|-not|-xor)\b', 'operator'),

            # Andre operatorer
            (r'[=+\-*/%!<>&|^~]', 'operator'),
        ]

        # Anvend patterns
        import re
        for pattern, format_name in patterns:
            for match in re.finditer(pattern, text, re.IGNORECASE):
                start = match.start()
                length = match.end() - match.start()
                self.setFormat(start, length, self.formats[format_name])

# === CUSTOM WIDGETS ===
class ScrolledTextEdit(QTextEdit):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.highlighted_text = ""
        self.powershell_syntax_enabled = True

        # Opret syntax highlighter og sæt den aktiv fra start
        self.syntax_highlighter = PowerShellSyntaxHighlighter(self.document())

        # Sæt til plain text mode for bedre performance
        self.setAcceptRichText(False)

        # Sørg for at syntax highlighter er altid aktiv
        self.syntax_highlighter.setDocument(self.document())

    def update_syntax_colors(self, theme="dark"):
        """Opdater syntaksfarver baseret på tema"""
        if hasattr(self, 'syntax_highlighter'):
            self.syntax_highlighter.update_theme(theme)

    def setText(self, text):
        """Sæt tekst - syntax highlighter håndterer automatisk fremhævning."""
        if self.powershell_syntax_enabled:
            # Sæt tekst og aktiver syntax highlighter
            self.setPlainText(text)
            # Sørg for at syntax highlighter er aktiv
            if hasattr(self, 'syntax_highlighter') and self.syntax_highlighter is not None:
                self.syntax_highlighter.setDocument(self.document())
                # Force rehighlight med delay
                QTimer.singleShot(1, self.syntax_highlighter.rehighlight)
        else:
            # Deaktivér syntax highlighter
            if hasattr(self, 'syntax_highlighter') and self.syntax_highlighter is not None:
                self.syntax_highlighter.setDocument(None)
            self.setPlainText(text)



    def getText(self):
        return self.toPlainText().strip()

    def highlightText(self, text):
        """Fremhæv tekst i editoren (søgefremhævning)."""
        if not text:
            return

        self.highlighted_text = text
        cursor = self.textCursor()
        cursor.setPosition(0)
        self.setTextCursor(cursor)

        # Find og fremhæv alle forekomster af søgetekst
        highlight_format = QTextCharFormat()
        highlight_format.setBackground(QColor("#FFD700"))
        highlight_format.setForeground(QColor("#000000"))

        content = self.toPlainText()
        pattern = re.escape(text)
        for match in re.finditer(pattern, content, re.IGNORECASE):
            cursor.setPosition(match.start())
            cursor.setPosition(match.end(), QTextCursor.KeepAnchor)
            cursor.setCharFormat(highlight_format)

    def clearHighlight(self):
        """Fjern søgefremhævning og gendan syntaksfremhævning."""
        current_text = self.toPlainText()
        self.setText(current_text)  # Dette vil genanvende syntaksfremhævning
        self.highlighted_text = ""

class CommandDialog(QDialog):
    def __init__(self, parent=None, title="", prompt="", initialvalue=""):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.resize(700, 200)
        if parent:
            self.setGeometry(
                parent.x() + (parent.width() // 2) - 350,
                parent.y() + (parent.height() // 2) - 100,
                700, 200
            )
        layout = QVBoxLayout(self)
        label = QLabel(prompt)
        layout.addWidget(label)
        self.text_edit = ScrolledTextEdit()
        self.text_edit.setFont(QFont("Consolas", 10))  # Reduced font size
        self.text_edit.setText(initialvalue)
        layout.addWidget(self.text_edit)
        btn_layout = QHBoxLayout()
        self.ok_btn = QPushButton("OK")
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn = QPushButton("Annuller")
        self.cancel_btn.clicked.connect(self.reject)
        btn_layout.addWidget(self.ok_btn)
        btn_layout.addWidget(self.cancel_btn)
        layout.addLayout(btn_layout)
        self.text_edit.setFocus()
    
    def getText(self):
        return self.text_edit.getText()

class ToolTip(QLabel):
    def __init__(self, parent=None, text=""):
        super().__init__(parent)
        self.setText(text)
        self.setStyleSheet("""
            background-color: #333;
            color: white;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 6px;
        """)
        self.setWindowFlags(Qt.ToolTip)
        self.hide()
        
class CopyNotification(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            background-color: #0078D7;
            color: white;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: bold;
        """)
        self.setAlignment(Qt.AlignCenter)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.hide()
        
    def showMessage(self, text, parent_widget, duration=1500):
        """Vis besked i midten af parent widget."""
        self.setText(text)
        self.adjustSize()
        parent_rect = parent_widget.geometry()
        x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
        y = parent_rect.y() + (parent_rect.height() - self.height()) // 2
        self.move(x, y)
        self.show()
        QTimer.singleShot(duration, self.hide)
        
class SettingsDialog(QDialog):
    def __init__(self, parent=None, current_settings=None):
        super().__init__(parent)
        self.setWindowTitle("Indstillinger")
        self.resize(500, 400)
        self.current_settings = current_settings or {}
        
        layout = QVBoxLayout(self)
        
        # Tema-indstillinger
        theme_group = QGroupBox("Tema")
        theme_layout = QVBoxLayout(theme_group)
        self.theme_dark = QCheckBox("Mørkt tema")
        self.theme_dark.setChecked(self.current_settings.get("theme", "dark") == "dark")
        self.theme_light = QCheckBox("Lyst tema")
        self.theme_light.setChecked(self.current_settings.get("theme", "dark") == "light")
        
        # Forbind checkboxes så kun én kan være valgt
        self.theme_dark.clicked.connect(lambda: self.theme_light.setChecked(not self.theme_dark.isChecked()))
        self.theme_light.clicked.connect(lambda: self.theme_dark.setChecked(not self.theme_light.isChecked()))
        
        theme_layout.addWidget(self.theme_dark)
        theme_layout.addWidget(self.theme_light)
        layout.addWidget(theme_group)
        
        # Generelle indstillinger
        general_group = QGroupBox("Generelle indstillinger")
        general_layout = QVBoxLayout(general_group)
        self.auto_save = QCheckBox("Automatisk gem")
        self.auto_save.setChecked(self.current_settings.get("auto_save", True))
        self.highlight_search = QCheckBox("Fremhæv søgeresultater")
        self.highlight_search.setChecked(self.current_settings.get("highlight_search", True))
        self.sort_alphabetically = QCheckBox("Sortér kommandoer alfabetisk")
        self.sort_alphabetically.setChecked(self.current_settings.get("sort_alphabetically", False))
        self.group_by_verb = QCheckBox("Gruppér kommandoer efter verb (Get, Set, osv.)")
        self.group_by_verb.setChecked(self.current_settings.get("group_by_verb", True))
        self.powershell_syntax = QCheckBox("PowerShell syntaksfremhævning")
        self.powershell_syntax.setChecked(self.current_settings.get("powershell_syntax", True))

        general_layout.addWidget(self.auto_save)
        general_layout.addWidget(self.highlight_search)
        general_layout.addWidget(self.sort_alphabetically)
        general_layout.addWidget(self.group_by_verb)
        general_layout.addWidget(self.powershell_syntax)
        layout.addWidget(general_group)
        
        # Import/Export
        import_export_group = QGroupBox("Import/Export")
        import_export_layout = QVBoxLayout(import_export_group)
        
        export_btn = QPushButton("Eksportér kommandoer til CSV")
        export_btn.clicked.connect(self.export_to_csv)
        import_export_layout.addWidget(export_btn)
        
        import_btn = QPushButton("Importér kommandoer fra CSV")
        import_btn.clicked.connect(self.import_from_csv)
        import_export_layout.addWidget(import_btn)
        
        layout.addWidget(import_export_group)
        
        # Knapper
        button_layout = QHBoxLayout()
        self.ok_btn = QPushButton("OK")
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn = QPushButton("Annuller")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
    
    def get_settings(self):
        """Returner de valgte indstillinger."""
        return {
            "theme": "dark" if self.theme_dark.isChecked() else "light",
            "auto_save": self.auto_save.isChecked(),
            "highlight_search": self.highlight_search.isChecked(),
            "sort_alphabetically": self.sort_alphabetically.isChecked(),
            "group_by_verb": self.group_by_verb.isChecked(),
            "powershell_syntax": self.powershell_syntax.isChecked(),
            "tab_order": settings.get("tab_order", [])  # Bevar eksisterende tab-rækkefølge
        }
    
    def export_to_csv(self):
        """Eksportér kommandoer til CSV-fil."""
        global categories
        filename, _ = QFileDialog.getSaveFileName(
            self, "Eksportér kommandoer", "", "CSV-filer (*.csv);;Alle filer (*.*)"
        )
        if not filename:
            return
            
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['Kategori', 'Kommando', 'Favorit'])
                
                for category, commands in categories.items():
                    for cmd in commands:
                        writer.writerow([
                            category,
                            cmd.get('text', ''),
                            'Ja' if cmd.get('favorite', False) else 'Nej'
                        ])
                        
            QMessageBox.information(self, "Eksport fuldført", 
                                  f"Kommandoer er eksporteret til {filename}")
        except Exception as e:
            QMessageBox.critical(self, "Eksport fejlede", 
                               f"Der opstod en fejl under eksport: {str(e)}")
    
    def import_from_csv(self):
        """Importér kommandoer fra CSV-fil."""
        global categories
        filename, _ = QFileDialog.getOpenFileName(
            self, "Importér kommandoer", "", "CSV-filer (*.csv);;Alle filer (*.*)"
        )
        if not filename:
            return
            
        try:
            imported_data = {}
            with open(filename, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.reader(csvfile)
                headers = next(reader)  # Skip header row
                
                for row in reader:
                    if len(row) >= 3:
                        category, cmd_text, favorite = row
                        if category not in imported_data:
                            imported_data[category] = []
                            
                        imported_data[category].append({
                            'text': cmd_text,
                            'favorite': favorite.lower() in ('ja', 'yes', 'true', '1')
                        })
            
            # Spørg om import-metode
            reply = QMessageBox.question(
                self, "Import metode", 
                "Vil du tilføje til eksisterende kategorier eller erstatte dem?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Cancel:
                return
                
            if reply == QMessageBox.StandardButton.Yes:  # Tilføj
                for category, commands in imported_data.items():
                    if category not in categories:
                        categories[category] = []
                    categories[category].extend(commands)
            else:  # Erstat
                categories = imported_data
                
            save_categories(categories)
            QMessageBox.information(self, "Import fuldført", 
                                  f"Kommandoer er importeret fra {filename}")
                                  
            # Signal til parent at opdatering er nødvendig
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Import fejlede", 
                               f"Der opstod en fejl under import: {str(e)}")
                               
class SearchHistoryDialog(QDialog):
    def __init__(self, parent=None, history=None):
        super().__init__(parent)
        self.setWindowTitle("Søgehistorik")
        self.resize(400, 300)
        self.selected_search = ""
        
        layout = QVBoxLayout(self)
        
        self.list_widget = QListWidget()
        if history:
            for item in history:
                self.list_widget.addItem(item)
        
        self.list_widget.itemDoubleClicked.connect(self.on_item_double_clicked)
        layout.addWidget(self.list_widget)
        
        button_layout = QHBoxLayout()
        self.select_btn = QPushButton("Vælg")
        self.select_btn.clicked.connect(self.on_select)
        self.cancel_btn = QPushButton("Annuller")
        self.cancel_btn.clicked.connect(self.reject)
        self.clear_btn = QPushButton("Ryd historik")
        self.clear_btn.clicked.connect(self.on_clear)
        
        button_layout.addWidget(self.select_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
    
    def on_select(self):
        """Håndter valg af søgning."""
        selected_items = self.list_widget.selectedItems()
        if selected_items:
            self.selected_search = selected_items[0].text()
            self.accept()
    
    def on_item_double_clicked(self, item):
        """Håndter dobbeltklik på søgning."""
        self.selected_search = item.text()
        self.accept()
    
    def on_clear(self):
        """Ryd søgehistorik."""
        reply = QMessageBox.question(
            self, "Ryd historik", 
            "Er du sikker på, at du vil rydde søgehistorikken?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.list_widget.clear()
            self.selected_search = "CLEAR_HISTORY"
            self.accept()
            
class CommandHistoryDialog(QDialog):
    def __init__(self, parent=None, history=None):
        super().__init__(parent)
        self.setWindowTitle("Kommandohistorik")
        self.resize(600, 400)
        self.selected_command = ""
        
        layout = QVBoxLayout(self)
        
        self.list_widget = QListWidget()
        if history:
            for item in history:
                self.list_widget.addItem(item)
        
        self.list_widget.itemDoubleClicked.connect(self.on_item_double_clicked)
        layout.addWidget(self.list_widget)
        
        button_layout = QHBoxLayout()
        self.select_btn = QPushButton("Vælg")
        self.select_btn.clicked.connect(self.on_select)
        self.cancel_btn = QPushButton("Annuller")
        self.cancel_btn.clicked.connect(self.reject)
        self.clear_btn = QPushButton("Ryd historik")
        self.clear_btn.clicked.connect(self.on_clear)
        
        button_layout.addWidget(self.select_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
    
    def on_select(self):
        """Håndter valg af kommando."""
        selected_items = self.list_widget.selectedItems()
        if selected_items:
            self.selected_command = selected_items[0].text()
            self.accept()
    
    def on_item_double_clicked(self, item):
        """Håndter dobbeltklik på kommando."""
        self.selected_command = item.text()
        self.accept()
    
    def on_clear(self):
        """Ryd kommandohistorik."""
        reply = QMessageBox.question(
            self, "Ryd historik", 
            "Er du sikker på, at du vil rydde kommandohistorikken?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.list_widget.clear()
            self.selected_command = "CLEAR_HISTORY"
            self.accept()

class DraggableTreeWidget(QTreeWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSelectionMode(QTreeWidget.SingleSelection)
        self.setIndentation(0)  # Fjern indrykning helt
        # Fjern rammer omkring celler og stjerner
        self.setStyleSheet("""
            QTreeWidget::item {
                border: none;
                padding-left: 0px;
            }
            QTreeWidget::branch {
                border: none;
                background: transparent;
                width: 0px;  /* Fjern bredden af branch-elementet */
            }
            QTreeWidget::indicator {
                width: 0px;  /* Fjern bredden af indikator-elementet */
            }
        """)

# === HOVEDVINDUE ===
class PowerShellOneLiners(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PowerShell OneLiners")
        self.setWindowIcon(QIcon("C:/Users/<USER>/OneDrive - Andersen Partners Advokatpartnerselskab/Scripts/py/APExamples/test/python.png"))
        self.resize(1200, 800)
        self.setMinimumSize(800, 600)
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # Initialiser variabler
        self.treeviews = {}
        self.auto_save_timer = QTimer(self)
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.copy_notification = CopyNotification()
        self.last_search_query = ""
        self._verb_cache = {}  # Cache for verb-gruppering
        self._tab_widgets_cache = {}  # Cache for tab widgets
        
        # Opsæt UI
        self.setup_ui()
        self.setup_shortcuts()
        self.apply_theme(settings.get("theme", "dark"))
        
        # Start auto-save timer hvis aktiveret
        if settings.get("auto_save", True):
            self.auto_save_timer.start(AUTO_SAVE_INTERVAL)
            
        # Opdater UI
        self.refresh_tabs()
        
    def setup_ui(self):
        # Opret toolbar
        toolbar = QToolBar("Hovedværktøjslinje")
        toolbar.setMovable(False)
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)
        
        # Tilføj handlinger til toolbar
        settings_action = QAction(QIcon(), "⚙️ Indstillinger", self)
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
        
        export_action = QAction(QIcon(), "📤 Eksportér", self)
        export_action.triggered.connect(self.export_commands)
        toolbar.addAction(export_action)
        
        import_action = QAction(QIcon(), "📥 Importér", self)
        import_action.triggered.connect(self.import_commands)
        toolbar.addAction(import_action)
        
        toolbar.addSeparator()
        
        history_action = QAction(QIcon(), "🕒 Kommandohistorik", self)
        history_action.triggered.connect(self.show_command_history)
        toolbar.addAction(history_action)
        
        # Søgefelt med historik-knap
        search_frame = QWidget()
        search_layout = QHBoxLayout(search_frame)
        search_layout.setContentsMargins(0, 10, 0, 10)
        search_label = QLabel("🔍 Søg:")
        search_label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        search_layout.addWidget(search_label)
        
        self.search_entry = QLineEdit()
        self.search_entry.setFont(QFont("Segoe UI", 10))
        self.search_entry.setPlaceholderText("Indtast søgeord her...")
        self.search_entry.setClearButtonEnabled(True)
        self.search_entry.setMinimumHeight(32)
        self.search_entry.textChanged.connect(self.search_commands)
        search_layout.addWidget(self.search_entry)
        
        search_history_btn = QPushButton("📋")
        search_history_btn.setToolTip("Søgehistorik")
        search_history_btn.setMaximumWidth(40)
        search_history_btn.clicked.connect(self.show_search_history)
        search_layout.addWidget(search_history_btn)
        
        self.main_layout.addWidget(search_frame)
        
        self.tab_control = QTabWidget()
        self.tab_control.setMovable(True)  # Enable tab reordering
        # Lazy loading: kun indlæs indhold når fane vælges
        self.tab_control.currentChanged.connect(self.on_tab_changed)
        # Lyt efter tab-bevægelser
        self.tab_control.tabBar().tabMoved.connect(self.on_tab_moved)
        self.main_layout.addWidget(self.tab_control)
        
        text_container = QWidget()
        text_layout = QVBoxLayout(text_container)
        text_layout.setContentsMargins(0, 10, 0, 10)
        # Image and text label for "Kommando"
        text_label_container = QWidget()
        text_label_layout = QHBoxLayout(text_label_container)
        text_label_layout.setContentsMargins(0, 0, 0, 0)
        text_label_layout.setSpacing(6)
        text_label_icon = QLabel()
        pixmap = QPixmap("C:/Users/<USER>/OneDrive - Andersen Partners Advokatpartnerselskab/Scripts/py/APExamples/test/ps.png")
        if pixmap.isNull():
            text_label_icon.setText("💻")  # Fallback to emoji if image fails
        else:
            text_label_icon.setPixmap(pixmap.scaled(24, 24, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        text_label_icon.setFont(QFont("Segoe UI", 10, QFont.Bold))
        text_label_layout.addWidget(text_label_icon)
        text_label_text = QLabel("Kommando:")
        text_label_text.setFont(QFont("Segoe UI", 10, QFont.Bold))
        text_label_layout.addWidget(text_label_text)
        text_label_layout.addStretch()  # Align left
        text_layout.addWidget(text_label_container)
        self.text_box = ScrolledTextEdit()
        self.text_box.setObjectName("commandTextBox")  # Sæt objektnavn for CSS styling
        self.text_box.setFont(QFont("Consolas", 12))  # Reduced font size
        self.text_box.setMinimumHeight(120)
        self.text_box.setLineWrapMode(QTextEdit.WidgetWidth)
        # Sæt syntaksfremhævning baseret på indstillinger
        self.text_box.powershell_syntax_enabled = settings.get("powershell_syntax", True)
        # Opdater syntaksfarver baseret på nuværende tema
        self.text_box.update_syntax_colors(settings.get("theme", "dark"))
        text_layout.addWidget(self.text_box)
        # SIMPEL FIX: Overskrid CSS direkte på widget for at tillade syntaksfremhævning
        if settings.get("theme", "dark") == "dark":
            self.text_box.setStyleSheet("background-color: #1E1E1E; border: 1px solid #3F3F46; border-radius: 8px; padding: 6px 8px; color: #F8F8F2;")
        else:
            self.text_box.setStyleSheet("background-color: #FFFFFF; border: 1px solid #CCCCCC; border-radius: 8px; padding: 6px 8px; color: #000000;")
        self.main_layout.addWidget(text_container)



        button_container = QWidget()
        button_layout = QVBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        main_button_frame = QWidget()
        main_button_layout = QHBoxLayout(main_button_frame)
        main_button_layout.setContentsMargins(0, 0, 0, 0)
        
        cmd_group = QGroupBox("Kommandoer")
        cmd_layout = QHBoxLayout(cmd_group)
        cmd_layout.setSpacing(10)
        self.add_cmd_btn = QPushButton("➕ Tilføj")
        self.add_cmd_btn.setToolTip("Tilføj ny kommando")
        self.add_cmd_btn.clicked.connect(self.add_command)
        cmd_layout.addWidget(self.add_cmd_btn)
        self.edit_cmd_btn = QPushButton("✏️ Rediger")
        self.edit_cmd_btn.setToolTip("Rediger valgt kommando")
        self.edit_cmd_btn.clicked.connect(self.edit_command)
        cmd_layout.addWidget(self.edit_cmd_btn)
        self.del_cmd_btn = QPushButton("🗑️ Slet")
        self.del_cmd_btn.setToolTip("Slet valgt kommando")
        self.del_cmd_btn.clicked.connect(self.delete_command)
        cmd_layout.addWidget(self.del_cmd_btn)
        main_button_layout.addWidget(cmd_group)
        
        cat_group = QGroupBox("Kategorier")
        cat_layout = QHBoxLayout(cat_group)
        cat_layout.setSpacing(10)
        self.add_cat_btn = QPushButton("➕ Tilføj")
        self.add_cat_btn.setToolTip("Tilføj ny kategori")
        self.add_cat_btn.clicked.connect(self.add_category)
        cat_layout.addWidget(self.add_cat_btn)
        self.edit_cat_btn = QPushButton("✏️ Omdøb")
        self.edit_cat_btn.setToolTip("Omdøb valgt kategori")
        self.edit_cat_btn.clicked.connect(self.edit_category)
        cat_layout.addWidget(self.edit_cat_btn)
        self.del_cat_btn = QPushButton("🗑️ Slet")
        self.del_cat_btn.setToolTip("Slet valgt kategori")
        self.del_cat_btn.clicked.connect(self.delete_category)
        cat_layout.addWidget(self.del_cat_btn)
        main_button_layout.addWidget(cat_group)
        
        copy_group = QGroupBox("Kopier")
        copy_layout = QHBoxLayout(copy_group)
        copy_layout.setSpacing(10)
        self.copy_button = QPushButton("📋 Kopier")
        self.copy_button.setToolTip("Kopier til udklipsholder")
        self.copy_button.clicked.connect(lambda: self.copy_to_clipboard(self.text_box.getText()))
        copy_layout.addWidget(self.copy_button)
        self.copy_close_button = QPushButton("📋 Kopier og Luk")
        self.copy_close_button.setToolTip("Kopier og luk programmet")
        self.copy_close_button.clicked.connect(lambda: self.copy_and_close(self.text_box.getText()))

        copy_layout.addWidget(self.copy_close_button)
        main_button_layout.addWidget(copy_group)
        
        button_layout.addWidget(main_button_frame)
        
        ok_frame = QWidget()
        ok_layout = QHBoxLayout(ok_frame)
        ok_layout.setContentsMargins(0, 0, 0, 0)
        ok_layout.addStretch()
        self.undo_btn = QPushButton("↩️ Fortryd")
        self.undo_btn.setToolTip("Fortryd sidste handling")
        self.undo_btn.clicked.connect(self.undo_last_action)
        self.undo_btn.setEnabled(False)
        self.undo_btn.setMinimumWidth(120)
        ok_layout.addWidget(self.undo_btn)
        self.ok_btn = QPushButton(" OK ")
        self.ok_btn.setToolTip("Luk programmet")
        self.ok_btn.clicked.connect(self.close)
        self.ok_btn.setMinimumWidth(120)
        self.ok_btn.setStyleSheet("background-color: #0078D7; border-radius: 8px;")
        ok_layout.addWidget(self.ok_btn)
        button_layout.addWidget(ok_frame)
        self.main_layout.addWidget(button_container)
        
        self.statusbar = QLabel()
        self.statusbar.setObjectName("statusbar")
        self.statusbar.setFont(QFont("Segoe UI", 10))
        self.statusbar.setAlignment(Qt.AlignCenter)
        self.statusbar.setMinimumHeight(30)
        self.main_layout.addWidget(self.statusbar)
    
    def add_to_history(self, action_type, data):
        global categories
        action_history.append({
            "type": action_type,
            "data": data,
            "categories": json.loads(json.dumps(categories))
        })
        if len(action_history) > MAX_HISTORY:
            action_history.pop(0)
        self.undo_btn.setEnabled(bool(action_history))
        self.set_status(f"Handling udført: {action_type}")
    
    def set_status(self, message, timeout=2000):
        self.statusbar.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.statusbar.setText(""))
    
    def undo_last_action(self):
        global categories
        if not action_history:
            QMessageBox.information(self, "Ingen historik", "Der er ingen handlinger at fortryde.")
            return
        last_action = action_history.pop()
        action_type = last_action["type"]
        data = last_action["data"]

        if action_type == "move_command":
            cmd_text = data["cmd_text"]
            source_category = data["source_category"]
            target_category = data["target_category"]
            cmd_obj = None
            for cmd in categories.get(target_category, []):
                if cmd.get("text") == cmd_text:
                    cmd_obj = cmd
                    break
            if cmd_obj:
                categories[target_category].remove(cmd_obj)
                if source_category in categories:
                    categories[source_category].append(cmd_obj)
        elif action_type == "reorder_category":
            categories = last_action["categories"]
        else:
            categories = last_action["categories"]

        save_categories(categories)
        self.refresh_tabs()
        self.undo_btn.setEnabled(bool(action_history))
        self.set_status(f"Handling fortrudt: {action_type}")
    
    def copy_to_clipboard(self, text):
        global command_history
        QApplication.clipboard().setText(text)
        
        # Vis notifikation
        self.copy_notification.showMessage("✓ Kopieret til udklipsholder", self)
        
        # Tilføj til kommandohistorik
        if text and text not in command_history:
            command_history.insert(0, text)
            if len(command_history) > COMMAND_HISTORY_MAX:
                command_history.pop()
            save_search_history(search_history, command_history)
            
        self.set_status("Kommando kopieret til clipboard!")
    
    def copy_and_close(self, text):
        self.copy_to_clipboard(text)
        self.auto_save_timer.stop()  # Stop auto-save før luk
        self.close()
        QApplication.quit()  # Explicitly quit the application

        
    def setup_shortcuts(self):
        """Opsæt tastaturgenveje."""
        # Kopier (Ctrl+C)
        copy_shortcut = QShortcut(QKeySequence.Copy, self)
        copy_shortcut.activated.connect(lambda: self.copy_to_clipboard(self.text_box.getText()))
        
        # Søg (Ctrl+F)
        search_shortcut = QShortcut(QKeySequence.Find, self)
        search_shortcut.activated.connect(lambda: self.search_entry.setFocus())
        
        # Næste fane (Ctrl+Tab)
        next_tab_shortcut = QShortcut(QKeySequence("Ctrl+Tab"), self)
        next_tab_shortcut.activated.connect(self.next_tab)
        
        # Forrige fane (Ctrl+Shift+Tab)
        prev_tab_shortcut = QShortcut(QKeySequence("Ctrl+Shift+Tab"), self)
        prev_tab_shortcut.activated.connect(self.prev_tab)
        
        # Tilføj kommando (Ctrl+N)
        add_cmd_shortcut = QShortcut(QKeySequence("Ctrl+N"), self)
        add_cmd_shortcut.activated.connect(self.add_command)
        
        # Rediger kommando (Ctrl+E)
        edit_cmd_shortcut = QShortcut(QKeySequence("Ctrl+E"), self)
        edit_cmd_shortcut.activated.connect(self.edit_command)
        
        # Slet kommando (Delete)
        del_cmd_shortcut = QShortcut(QKeySequence.Delete, self)
        del_cmd_shortcut.activated.connect(self.delete_command)
        
        # Fortryd (Ctrl+Z)
        undo_shortcut = QShortcut(QKeySequence.Undo, self)
        undo_shortcut.activated.connect(self.undo_last_action)
        
        # Indstillinger (Ctrl+,)
        settings_shortcut = QShortcut(QKeySequence("Ctrl+,"), self)
        settings_shortcut.activated.connect(self.show_settings)
        
    def next_tab(self):
        """Skift til næste fane."""
        current = self.tab_control.currentIndex()
        if current < self.tab_control.count() - 1:
            self.tab_control.setCurrentIndex(current + 1)
        else:
            self.tab_control.setCurrentIndex(0)
            
    def prev_tab(self):
        """Skift til forrige fane."""
        current = self.tab_control.currentIndex()
        if current > 0:
            self.tab_control.setCurrentIndex(current - 1)
        else:
            self.tab_control.setCurrentIndex(self.tab_control.count() - 1)

    def on_tab_changed(self, index):
        """Håndter fane-skift for lazy loading"""
        if index < 0 or index >= self.tab_control.count():
            return

        tab_name = self.tab_control.tabText(index)

        # Check om fanen allerede har indhold
        if tab_name in self.treeviews:
            return

        # Lazy load fane-indhold
        if tab_name == "⭐ Favoritter":
            favorites = self.get_favorites()
            if favorites:
                self._populate_tab_content(index, tab_name, favorites, is_favorites_tab=True)
        elif tab_name in categories:
            self._populate_tab_content(index, tab_name, categories[tab_name])

    def _populate_tab_content(self, tab_index, category, commands, is_favorites_tab=False):
        """Fyld fane med indhold (lazy loading)"""
        tab_widget = self.tab_control.widget(tab_index)
        if not tab_widget:
            return

        # Fjern eksisterende layout hvis det findes
        if tab_widget.layout():
            QWidget().setLayout(tab_widget.layout())

        layout = QVBoxLayout(tab_widget)
        treeview = DraggableTreeWidget()
        treeview.setColumnCount(2)
        treeview.setColumnWidth(0, 25)
        treeview.setHeaderHidden(True)
        treeview.setFont(QFont("Arial", 10))
        layout.addWidget(treeview)

        # Fyld treeview med kommandoer (samme logik som add_category_tab)
        self._fill_treeview(treeview, category, commands, is_favorites_tab)

        # Tilføj event handlers
        treeview.itemClicked.connect(lambda item, column: self.on_treeview_click(item, column, is_favorites_tab))
        treeview.setContextMenuPolicy(Qt.CustomContextMenu)
        treeview.customContextMenuRequested.connect(
            lambda pos: self.show_context_menu(treeview, pos, is_favorites_tab)
        )
        self.treeviews[category] = treeview

    def _fill_treeview(self, treeview, category, commands, is_favorites_tab=False):
        """Fyld treeview med kommandoer"""
        # Gruppér kommandoer efter verb hvis indstillingen er aktiveret
        if settings.get("group_by_verb", True) and not is_favorites_tab:
            # Brug cache til verb-gruppering for bedre performance
            cache_key = f"{category}_{create_command_hash(commands)}"
            if cache_key in self._verb_cache:
                grouped_commands = self._verb_cache[cache_key]
            else:
                grouped_commands = group_commands_by_verb(commands)
                self._verb_cache[cache_key] = grouped_commands

            for verb, verb_commands in grouped_commands.items():
                # Opret verb-gruppe (altid udvidet)
                verb_item = QTreeWidgetItem(treeview)
                verb_item.setText(0, "")
                verb_item.setText(1, f"📂 {verb} ({len(verb_commands)})")
                verb_item.setForeground(1, QColor("#87CEEB"))  # Lys blå farve for grupper
                verb_item.setExpanded(True)  # Altid udvidet

                # Sorter kommandoer i gruppen alfabetisk hvis indstillingen er aktiveret
                if settings.get("sort_alphabetically", False):
                    verb_commands = sorted(verb_commands, key=lambda x: x.get("text", "").lower())

                # Tilføj kommandoer til gruppen
                for command in verb_commands:
                    if isinstance(command, dict):
                        cmd_text = command.get("text", "")
                        is_favorite = command.get("favorite", False)

                        item = QTreeWidgetItem(verb_item)
                        item.setText(0, "⭐" if is_favorite else "☆")
                        item.setTextAlignment(0, Qt.AlignCenter)
                        item.setText(1, cmd_text)
                        item.setForeground(0, QColor("#FFD700") if is_favorite else QColor("white"))
                        item.setForeground(1, QColor("white"))
                        item.setData(1, Qt.UserRole, cmd_text)
                        item.setData(1, Qt.UserRole + 1, is_favorite)
                        item.setData(1, Qt.UserRole + 2, category)
        else:
            # Vis kommandoer fladt (som før)
            # Sorter kommandoer alfabetisk hvis indstillingen er aktiveret
            if settings.get("sort_alphabetically", False):
                commands = sorted(commands, key=lambda x: x.get("text", "").lower())

            for command in commands:
                if isinstance(command, dict):
                    cmd_text = command.get("text", "")
                    is_favorite = command.get("favorite", False)

                    item = QTreeWidgetItem()
                    item.setText(0, "⭐" if is_favorite else "☆")
                    item.setTextAlignment(0, Qt.AlignCenter)
                    item.setText(1, cmd_text)
                    item.setForeground(0, QColor("#FFD700") if is_favorite else QColor("white"))
                    item.setForeground(1, QColor("white"))
                    item.setData(1, Qt.UserRole, cmd_text)
                    item.setData(1, Qt.UserRole + 1, is_favorite)
                    if is_favorites_tab and "category" in command:
                        item.setData(1, Qt.UserRole + 2, command["category"])
                    else:
                        item.setData(1, Qt.UserRole + 2, category)
                    treeview.addTopLevelItem(item)

    def auto_save(self):
        """Gem data automatisk uden at crashe ved afbrydelse."""
        try:
            save_categories(categories)
        except KeyboardInterrupt:
            print("Auto-save blev afbrudt manuelt.")
        except Exception as e:
            print(f"Fejl ved auto-save: {e}")

        
    def apply_theme(self, theme):
        """Anvend det valgte tema."""
        if theme == "light":
            app.setStyleSheet("""
                QWidget {
                    background-color: #F0F0F0;
                    color: #000000;
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 10pt;
                }
                /* Specifik styling for kommando text box - ingen farve override når syntaksfremhævning er aktiv */
                QTextEdit#commandTextBox {
                    background-color: #FFFFFF !important;
                    border: 1px solid #CCCCCC !important;
                    border-radius: 8px !important;
                    padding: 6px 8px !important;
                    selection-background-color: #0078D7 !important;
                    font-size: 12pt !important;
                    /* Eksplicit ingen color property for at tillade syntaksfremhævning */
                }
                /* Andre QTextEdit elementer - INGEN farve override for kommando-boksen */
                QTextEdit {
                    background-color: #FFFFFF;
                    border: 1px solid #CCCCCC;
                    border-radius: 8px;
                    padding: 6px 8px;
                    selection-background-color: #0078D7;
                    font-size: 12pt;
                }
                QLineEdit {
                    background-color: #FFFFFF;
                    color: #000000;
                    border: 1px solid #CCCCCC;
                    border-radius: 8px;
                    padding: 6px 8px;
                    selection-background-color: #0078D7;
                    font-size: 10pt;
                }
                QPushButton {
                    background-color: #E1E1E1;
                    color: #000000;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-height: 30px;
                    font-size: 10pt;
                }
                QPushButton:hover {
                    background-color: #D1D1D1;
                }
                QPushButton:pressed {
                    background-color: #0078D7;
                    color: #FFFFFF;
                }
                QPushButton:disabled {
                    background-color: #E1E1E1;
                    color: #999999;
                    border: 1px solid #CCCCCC;
                    opacity: 0.5;
                }
                QTreeWidget {
                    background-color: #FFFFFF;
                    color: #000000;
                    border: 1px solid #CCCCCC;
                    border-radius: 8px;
                    padding: 6px;
                    selection-background-color: #0078D7;
                    font-family: Arial, sans-serif;
                    font-size: 10pt;
                }
                QTreeWidget::item {
                    padding: 4px;
                    border-radius: 6px;
                    height: 24px;
                    text-align: center;
                }
                QTreeWidget::item:selected {
                    background-color: #0078D7;
                    color: #FFFFFF;
                    border-radius: 6px;
                }
                QTreeWidget::item:hover {
                    background-color: #E1E1E1;
                    border-radius: 6px;
                }
                QTabWidget::pane {
                    border: 1px solid #CCCCCC;
                    border-radius: 8px;
                    background-color: #F0F0F0;
                    top: -1px;
                }
                QTabBar::tab {
                    background-color: #E1E1E1;
                    color: #000000;
                    padding: 10px 20px;
                    margin-right: 4px;
                    border-top-left-radius: 8px;
                    border-top-right-radius: 8px;
                    font-size: 10pt;
                }
                QTabBar::tab:selected {
                    background-color: #0078D7;
                    color: #FFFFFF;
                    font-weight: bold;
                    border-bottom: 2px solid #0078D7;
                }
                QTabBar::tab:hover:!selected {
                    background-color: #D1D1D1;
                }
                QGroupBox {
                    border: 1px solid #CCCCCC;
                    border-radius: 8px;
                    margin-top: 18px;
                    font-weight: bold;
                    padding-top: 18px;
                    font-size: 10pt;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 12px;
                    padding: 0 6px;
                    background-color: #F0F0F0;
                }
                QScrollBar:vertical {
                    border: none;
                    background-color: #F0F0F0;
                    width: 14px;
                    margin: 0px;
                    border-radius: 7px;
                }
                QScrollBar::handle:vertical {
                    background-color: #CCCCCC;
                    border-radius: 7px;
                    min-height: 20px;
                    margin: 2px;
                }
                QScrollBar::handle:vertical:hover {
                    background-color: #BBBBBB;
                }
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    height: 0px;
                }
                QScrollBar:horizontal {
                    border: none;
                    background-color: #F0F0F0;
                    height: 14px;
                    margin: 0px;
                    border-radius: 7px;
                }
                QScrollBar::handle:horizontal {
                    background-color: #CCCCCC;
                    border-radius: 7px;
                    min-width: 20px;
                    margin: 2px;
                }
                QScrollBar::handle:horizontal:hover {
                    background-color: #BBBBBB;
                }
                QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                    width: 0px;
                }
                QLabel {
                    padding: 4px;
                    font-size: 10pt;
                    color: #000000;
                }
                QMenu {
                    background-color: #FFFFFF;
                    color: #000000;
                    border: 1px solid #CCCCCC;
                    border-radius: 8px;
                    padding: 6px;
                    font-size: 10pt;
                }
                QMenu::item {
                    padding: 8px 28px 8px 14px;
                    border-radius: 6px;
                }
                QMenu::item:selected {
                    background-color: #0078D7;
                    color: #FFFFFF;
                    border-radius: 6px;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #CCCCCC;
                    margin: 6px 8px;
                }
                QToolBar {
                    background-color: #F0F0F0;
                    border: none;
                    spacing: 6px;
                    padding: 3px;
                }
                QToolButton {
                    background-color: transparent;
                    border-radius: 4px;
                    padding: 4px;
                }
                QToolButton:hover {
                    background-color: #E1E1E1;
                }
                QToolButton:pressed {
                    background-color: #D1D1D1;
                }
            """)
        else:  # Mørkt tema (standard)
            app.setStyleSheet("""
                QWidget {
                    background-color: #2D2D30;
                    color: #ffffff;
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 10pt;
                }
                /* Specifik styling for kommando text box - ingen farve override når syntaksfremhævning er aktiv */
                QTextEdit#commandTextBox {
                    background-color: #1E1E1E !important;
                    border: 1px solid #3F3F46 !important;
                    border-radius: 8px !important;
                    padding: 6px 8px !important;
                    selection-background-color: #0078D7 !important;
                    font-size: 12pt !important;
                    /* Eksplicit ingen color property for at tillade syntaksfremhævning */
                }
                /* Andre QTextEdit elementer - INGEN farve override for kommando-boksen */
                QTextEdit {
                    background-color: #1E1E1E;
                    border: 1px solid #3F3F46;
                    border-radius: 8px;
                    padding: 6px 8px;
                    selection-background-color: #0078D7;
                    font-size: 12pt;
                }
                QLineEdit {
                    background-color: #1E1E1E;
                    color: #ffffff;
                    border: 1px solid #3F3F46;
                    border-radius: 8px;
                    padding: 6px 8px;
                    selection-background-color: #0078D7;
                    font-size: 10pt;
                }
                QPushButton {
                    background-color: #3E3E42;
                    color: #ffffff;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-height: 30px;
                    font-size: 10pt;
                }
                QPushButton:hover {
                    background-color: #505054;
                }
                QPushButton:pressed {
                    background-color: #0078D7;
                }
                QPushButton:disabled {
                    background-color: #3E3E42;
                    color: #6B6B6B;
                    border: 1px solid #3F3F46;
                    opacity: 0.5;
                }
                QTreeWidget {
                    background-color: #1E1E1E;
                    color: #ffffff;
                    border: 1px solid #3F3F46;
                    border-radius: 8px;
                    padding: 6px;
                    selection-background-color: #0078D7;
                    font-family: Arial, sans-serif;
                    font-size: 10pt;
                }
                QTreeWidget::item {
                    padding: 4px;
                    border-radius: 6px;
                    height: 24px;
                    text-align: center;
                }
                QTreeWidget::item:selected {
                    background-color: #0078D7;
                    border-radius: 6px;
                }
                QTreeWidget::item:hover {
                    background-color: #3E3E42;
                    border-radius: 6px;
                }
                QTabWidget::pane {
                    border: 1px solid #3F3F46;
                    border-radius: 8px;
                    background-color: #2D2D30;
                    top: -1px;
                }
                QTabBar::tab {
                    background-color: #252526;
                    color: #ffffff;
                    padding: 10px 20px;
                    margin-right: 4px;
                    border-top-left-radius: 8px;
                    border-top-right-radius: 8px;
                    font-size: 10pt;
                }
                QTabBar::tab:selected {
                    background-color: #0078D7;
                    font-weight: bold;
                    border-bottom: 2px solid #0078D7;
                }
                QTabBar::tab:hover:!selected {
                    background-color: #3E3E42;
                }
                QGroupBox {
                    border: 1px solid #3F3F46;
                    border-radius: 8px;
                    margin-top: 18px;
                    font-weight: bold;
                    padding-top: 18px;
                    font-size: 10pt;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 12px;
                    padding: 0 6px;
                    background-color: #2D2D30;
                }
                QScrollBar:vertical {
                    border: none;
                    background-color: #2D2D30;
                    width: 14px;
                    margin: 0px;
                    border-radius: 7px;
                }
                QScrollBar::handle:vertical {
                    background-color: #3E3E42;
                    border-radius: 7px;
                    min-height: 20px;
                    margin: 2px;
                }
                QScrollBar::handle:vertical:hover {
                    background-color: #505054;
                }
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    height: 0px;
                }
                QScrollBar:horizontal {
                    border: none;
                    background-color: #2D2D30;
                    height: 14px;
                    margin: 0px;
                    border-radius: 7px;
                }
                QScrollBar::handle:horizontal {
                    background-color: #3E3E42;
                    border-radius: 7px;
                    min-width: 20px;
                    margin: 2px;
                }
                QScrollBar::handle:horizontal:hover {
                    background-color: #505054;
                }
                QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                    width: 0px;
                }
                QLabel {
                    padding: 4px;
                    font-size: 10pt;
                }
                QMenu {
                    background-color: #1E1E1E;
                    color: white;
                    border: 1px solid #3F3F46;
                    border-radius: 8px;
                    padding: 6px;
                    font-size: 10pt;
                }
                QMenu::item {
                    padding: 8px 28px 8px 14px;
                    border-radius: 6px;
                }
                QMenu::item:selected {
                    background-color: #0078D7;
                    border-radius: 6px;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #3F3F46;
                    margin: 6px 8px;
                }
                QToolBar {
                    background-color: #2D2D30;
                    border: none;
                    spacing: 6px;
                    padding: 3px;
                }
                QToolButton {
                    background-color: transparent;
                    border-radius: 4px;
                    padding: 4px;
                }
                QToolButton:hover {
                    background-color: #3E3E42;
                }
                QToolButton:pressed {
                    background-color: #505054;
                }
            """)
    
    def clear_cache(self):
        """Ryd cache når data ændres"""
        self._verb_cache.clear()
        self._tab_widgets_cache.clear()

    def refresh_tabs(self, selected_idx=None):
        if selected_idx is None and self.tab_control.count() > 0:
            selected_idx = self.tab_control.currentIndex()

        # Ryd cache ved refresh
        self.clear_cache()

        self.tab_control.clear()
        self.treeviews.clear()

        # Tilføj favoritter først
        favorites = self.get_favorites()
        if favorites:
            self.add_category_tab("⭐ Favoritter", favorites, is_favorites_tab=True)

        # Brug gemt rækkefølge hvis tilgængelig
        tab_order = settings.get("tab_order", [])
        added_categories = set()

        # Tilføj kategorier i den gemte rækkefølge
        for category in tab_order:
            if category in categories:
                self.add_category_tab(category, categories[category])
                added_categories.add(category)

        # Tilføj eventuelle nye kategorier der ikke er i den gemte rækkefølge
        for category, commands in categories.items():
            if category not in added_categories:
                self.add_category_tab(category, commands)

        if selected_idx is not None and self.tab_control.count() > 0:
            if selected_idx < self.tab_control.count():
                self.tab_control.setCurrentIndex(selected_idx)
            else:
                self.tab_control.setCurrentIndex(self.tab_control.count() - 1)
    
    def get_favorites(self):
        favorites = []
        for category, commands in categories.items():
            for cmd in commands:
                if cmd.get("favorite", False):
                    favorites.append({"text": cmd["text"], "favorite": True, "category": category})
        return favorites
    
    def add_category_tab(self, category, commands, is_favorites_tab=False):
        """Tilføj kategori-fane med lazy loading for bedre performance"""
        tab = QWidget()
        self.tab_control.addTab(tab, category)

        # For den første fane (favoritter) eller hvis der er få kategorier, indlæs med det samme
        current_tab_count = self.tab_control.count()
        if current_tab_count <= 2 or category == "⭐ Favoritter":
            # Indlæs indhold med det samme
            self._populate_tab_content(current_tab_count - 1, category, commands, is_favorites_tab)
        # Ellers vil indholdet blive indlæst når fanen vælges (lazy loading)
    
    def on_tab_moved(self, from_index, to_index):
        """Håndter når en fane flyttes"""
        global categories, settings

        # Få navnene på alle faner (ekskl. specielle faner)
        all_tab_names = []
        for i in range(self.tab_control.count()):
            tab_name = self.tab_control.tabText(i)
            all_tab_names.append(tab_name)

        # Filtrer specielle faner ud for at få kun kategori-faner
        category_tabs = []
        for tab_name in all_tab_names:
            if tab_name not in ["⭐ Favoritter", "🔍 Søgeresultater"]:
                category_tabs.append(tab_name)

        # Gem den nye rækkefølge
        settings["tab_order"] = category_tabs
        save_settings(settings)

        # Reorganiser categories dictionary i den nye rækkefølge
        new_categories = {}
        for cat in category_tabs:
            if cat in categories:
                new_categories[cat] = categories[cat]

        # Tilføj eventuelle kategorier der ikke er i rækkefølgen
        for cat, commands in categories.items():
            if cat not in new_categories:
                new_categories[cat] = commands

        categories = new_categories

        # Gem ændringerne (men undgå rekursion ved ikke at kalde save_categories igen)
        with open(DATAFILE, "w", encoding="utf-8") as f:
            json.dump(categories, f, indent=2, ensure_ascii=False)

        # Tilføj til historik
        moved_tab = self.tab_control.tabText(to_index)
        if moved_tab not in ["⭐ Favoritter", "🔍 Søgeresultater"]:
            self.add_to_history("reorder_category", {
                "category": moved_tab,
                "old_index": from_index,
                "new_index": to_index
            })

        self.set_status(f"Fane-rækkefølge gemt: {moved_tab}")
    
    def on_treeview_click(self, item, column, is_favorites_tab):
        if not item:
            return
        if column == 0:
            self.toggle_favorite(item, is_favorites_tab)
            return
        if column == 1:
            cmd_text = item.data(1, Qt.UserRole)
            if cmd_text:
                # Simpel setText - syntax highlighter håndterer automatisk fremhævning
                self.text_box.setText(cmd_text)
                # Force rehighlight efter kort delay for at sikre UI er klar
                QTimer.singleShot(10, self._force_rehighlight)

                # Fremhæv søgeord hvis indstillingen er aktiveret
                if settings.get("highlight_search", True) and self.last_search_query:
                    current_tab = self.tab_control.tabText(self.tab_control.currentIndex())
                    if current_tab == "🔍 Søgeresultater":
                        self.text_box.highlightText(self.last_search_query)
                    else:
                        self.text_box.clearHighlight()

    def _force_rehighlight(self):
        """Force genopfriskning af syntaksfremhævning."""
        if (hasattr(self.text_box, 'syntax_highlighter') and
            self.text_box.syntax_highlighter is not None and
            self.text_box.powershell_syntax_enabled):
            # Sæt focus på text_box for at aktivere syntax highlighter
            self.text_box.setFocus()
            self.text_box.syntax_highlighter.rehighlight()
            # Fjern focus igen
            self.text_box.clearFocus()

    def show_context_menu(self, treeview, pos, is_favorites_tab):
        item = treeview.itemAt(pos)
        if item:
            treeview.setCurrentItem(item)
            cmd_text = item.data(1, Qt.UserRole)
            is_favorite = item.data(1, Qt.UserRole + 1)
            category = self.tab_control.tabText(self.tab_control.currentIndex())
            context_menu = QMenu(self)
            if is_favorite:
                context_menu.addAction("Fjern fra favoritter", 
                                     lambda: self.toggle_favorite(item, is_favorites_tab))
            else:
                context_menu.addAction("Tilføj til favoritter", 
                                     lambda: self.toggle_favorite(item, is_favorites_tab))
            context_menu.addSeparator()
            # Enable "Flyt" for all commands, disable for category headers in Search Results
            if category == "🔍 Søgeresultater" and not item.parent():
                context_menu.addAction("Flyt", 
                                     lambda: self.move_command(treeview, item, is_favorites_tab)).setEnabled(False)
            else:
                context_menu.addAction("Flyt", 
                                     lambda: self.move_command(treeview, item, is_favorites_tab))
            context_menu.addAction("Kopier", 
                                 lambda: self.copy_to_clipboard(cmd_text))
            context_menu.addAction("Rediger", 
                                 self.edit_command)
            context_menu.addAction("Slet", 
                                 self.delete_command)
            context_menu.exec(treeview.mapToGlobal(pos))
    
    def move_command(self, treeview, item, is_favorites_tab):
        global categories
        cmd_text = item.data(1, Qt.UserRole)
        source_category = item.data(1, Qt.UserRole + 2) if is_favorites_tab else self.tab_control.tabText(self.tab_control.currentIndex())
        
        # Get list of categories (excluding Favorites and Search Results)
        available_categories = [cat for cat in categories.keys() if cat not in ["⭐ Favoritter", "🔍 Søgeresultater"]]
        if not available_categories:
            QMessageBox.warning(self, "Ingen kategorier", "Der er ingen tilgængelige kategorier at flytte kommandoen til.")
            return
        
        target_category, ok = QInputDialog.getItem(self, "Flyt kommando", 
                                                 f"Vælg kategori at flytte '{cmd_text[:30] + '...' if len(cmd_text) > 30 else cmd_text}' til:",
                                                 available_categories, 0, False)
        if not ok or not target_category:
            return
        
        if source_category == target_category:
            QMessageBox.information(self, "Samme kategori", "Kommandoen er allerede i denne kategori.")
            return
        
        cmd_obj = None
        for cmd in categories.get(source_category, []):
            if cmd.get("text") == cmd_text:
                cmd_obj = cmd
                break
        if not cmd_obj:
            QMessageBox.warning(self, "Fejl", "Kommandoen blev ikke fundet.")
            return
        
        # Move command
        categories[source_category].remove(cmd_obj)
        categories[target_category].append(cmd_obj)
        
        self.add_to_history("move_command", {
            "cmd_text": cmd_text,
            "source_category": source_category,
            "target_category": target_category
        })
        save_categories(categories)
        self.refresh_tabs(self.tab_control.currentIndex())
        self.set_status(f"Kommando flyttet til '{target_category}'")
    
    def toggle_favorite(self, item, is_favorites_tab=False):
        global categories
        cmd_text = item.data(1, Qt.UserRole)
        is_favorite = item.data(1, Qt.UserRole + 1)
        category = item.data(1, Qt.UserRole + 2) if is_favorites_tab else self.tab_control.tabText(self.tab_control.currentIndex())
        
        if not category:
            return
        
        for cmd in categories.get(category, []):
            if cmd.get("text") == cmd_text:
                cmd["favorite"] = not is_favorite
                break
        
        new_is_favorite = not is_favorite
        item.setText(0, "⭐" if new_is_favorite else "☆")
        item.setTextAlignment(0, Qt.AlignCenter)
        item.setForeground(0, QColor("#FFD700") if new_is_favorite else QColor("white"))
        item.setData(1, Qt.UserRole + 1, new_is_favorite)
        
        save_categories(categories)
        self.add_to_history("toggle_favorite", {"cmd_text": cmd_text, "category": category, "is_favorites_tab": is_favorites_tab})
        self.refresh_tabs(self.tab_control.currentIndex())
    
    def toggle_favorite_in_search(self, item):
        global categories
        cmd_text = item.data(1, Qt.UserRole)
        is_favorite = item.data(1, Qt.UserRole + 1)
        category = item.data(1, Qt.UserRole + 2)
        
        for cmd in categories.get(category, []):
            if cmd.get("text") == cmd_text:
                cmd["favorite"] = not is_favorite
                break
        
        new_is_favorite = not is_favorite
        item.setText(0, "⭐" if new_is_favorite else "☆")
        item.setTextAlignment(0, Qt.AlignCenter)
        item.setForeground(0, QColor("#FFD700") if new_is_favorite else QColor("white"))
        item.setData(1, Qt.UserRole + 1, new_is_favorite)
        
        save_categories(categories)
        self.add_to_history("toggle_favorite_in_search", {"cmd_text": cmd_text, "category": category})
        self.refresh_tabs(self.tab_control.currentIndex())
    
    def show_settings(self):
        """Vis indstillingsdialog."""
        global settings
        dialog = SettingsDialog(self, settings)
        if dialog.exec():
            # Gem nye indstillinger
            new_settings = dialog.get_settings()
            
            # Anvend tema hvis ændret
            if new_settings["theme"] != settings["theme"]:
                self.apply_theme(new_settings["theme"])
                # Opdater syntaksfarver for nyt tema
                self.text_box.update_syntax_colors(new_settings["theme"])
                # Opdater text box styling
                if new_settings["theme"] == "dark":
                    self.text_box.setStyleSheet("background-color: #1E1E1E; border: 1px solid #3F3F46; border-radius: 8px; padding: 6px 8px; color: #F8F8F2;")
                else:
                    self.text_box.setStyleSheet("background-color: #FFFFFF; border: 1px solid #CCCCCC; border-radius: 8px; padding: 6px 8px; color: #000000;")
                # Gendan nuværende tekst med nye farver
                current_text = self.text_box.toPlainText()
                if current_text:
                    self.text_box.setText(current_text)
                
            # Start/stop auto-save timer
            if new_settings["auto_save"] and not settings["auto_save"]:
                self.auto_save_timer.start(AUTO_SAVE_INTERVAL)
            elif not new_settings["auto_save"] and settings["auto_save"]:
                self.auto_save_timer.stop()
                
            # Opdater syntaksfremhævning hvis ændret
            if new_settings["powershell_syntax"] != settings.get("powershell_syntax", True):
                self.text_box.powershell_syntax_enabled = new_settings["powershell_syntax"]
                # Gendan nuværende tekst med/uden syntaksfremhævning
                current_text = self.text_box.toPlainText()
                if current_text:
                    self.text_box.setText(current_text)

            # Opdater indstillinger
            settings = new_settings
            save_settings(settings)

            # Opdater UI hvis nødvendigt
            self.refresh_tabs()
            
    def export_commands(self):
        """Eksportér kommandoer til CSV-fil."""
        dialog = SettingsDialog(self, settings)
        dialog.export_to_csv()
        
    def import_commands(self):
        """Importér kommandoer fra CSV-fil."""
        dialog = SettingsDialog(self, settings)
        if dialog.import_from_csv():
            self.refresh_tabs()
            
    def show_search_history(self):
        """Vis søgehistorik."""
        global search_history
        dialog = SearchHistoryDialog(self, search_history)
        if dialog.exec():
            if dialog.selected_search == "CLEAR_HISTORY":
                search_history = []
                save_search_history(search_history, command_history)
            elif dialog.selected_search:
                self.search_entry.setText(dialog.selected_search)
                
    def show_command_history(self):
        """Vis kommandohistorik."""
        global command_history
        dialog = CommandHistoryDialog(self, command_history)
        if dialog.exec():
            if dialog.selected_command == "CLEAR_HISTORY":
                command_history = []
                save_search_history(search_history, command_history)
            elif dialog.selected_command:
                self.text_box.setText(dialog.selected_command)
                
    def search_commands(self):
        global search_history
        query = self.search_entry.text().strip().lower()
        
        # Fjern eksisterende søgeresultat-fane
        for i in range(self.tab_control.count()):
            if self.tab_control.tabText(i) == "🔍 Søgeresultater":
                self.tab_control.removeTab(i)
                break
                
        if not query:
            return
            
        # Gem søgning i historik hvis den er ny
        if query and query not in search_history:
            search_history.insert(0, query)
            if len(search_history) > SEARCH_HISTORY_MAX:
                search_history.pop()
            save_search_history(search_history, command_history)
            
        # Gem den aktuelle søgning
        self.last_search_query = query
            
        # Søg efter kommandoer
        all_results = []
        for category, commands in categories.items():
            for cmd in commands:
                cmd_text = cmd.get("text", "").lower()
                is_favorite = cmd.get("favorite", False)
                cmd_obj = {"text": cmd["text"], "favorite": is_favorite, "category": category}
                if '-' in query:
                    if query in cmd_text:
                        all_results.append(cmd_obj)
                else:
                    if any(part.startswith(query) for part in cmd_text.split('-')):
                        all_results.append(cmd_obj)
        
        tab = QWidget()
        self.tab_control.addTab(tab, "🔍 Søgeresultater")
        layout = QVBoxLayout(tab)
        if not all_results:
            label = QLabel("Ingen kommandoer matchede søgningen.")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            self.tab_control.setCurrentIndex(self.tab_control.count() - 1)
            return
        
        # Sorter resultater alfabetisk hvis indstillingen er aktiveret
        if settings.get("sort_alphabetically", False):
            all_results.sort(key=lambda x: x.get("text", "").lower())
        
        result_info = QLabel(f"Fandt {len(all_results)} resultater for '{query}'")
        result_info.setFont(QFont("Segoe UI", 10, QFont.Bold))
        layout.addWidget(result_info)
        
        treeview = DraggableTreeWidget()
        treeview.setColumnCount(2)
        treeview.setColumnWidth(0, 30)  # Reduceret bredde fra 50 til 30
        treeview.setHeaderHidden(True)
        treeview.setMouseTracking(True)
        treeview.setToolTip("Klik på en stjerne for at tilføje/fjerne fra favoritter")
        treeview.setFont(QFont("Arial", 10))
        layout.addWidget(treeview)
        
        results_by_category = {}
        for cmd in all_results:
            category = cmd.get("category", "Ukendt")
            if category not in results_by_category:
                results_by_category[category] = []
            results_by_category[category].append(cmd)
        
        for category, commands in results_by_category.items():
            category_item = QTreeWidgetItem(treeview)
            category_item.setText(0, f"📁 {category}")
            category_item.setForeground(0, QColor("#87CEEB"))
            category_item.setExpanded(True)
            
            for command in commands:
                cmd_text = command.get("text", "")
                is_favorite = command.get("favorite", False)
                
                item = QTreeWidgetItem(category_item)
                item.setText(0, "⭐" if is_favorite else "☆")
                item.setTextAlignment(0, Qt.AlignCenter)
                item.setText(1, cmd_text)
                item.setForeground(0, QColor("#FFD700") if is_favorite else QColor("white"))
                item.setForeground(1, QColor("white"))
                item.setData(1, Qt.UserRole, cmd_text)
                item.setData(1, Qt.UserRole + 1, is_favorite)
                item.setData(1, Qt.UserRole + 2, category)
        
        treeview.itemClicked.connect(lambda item, column: self.on_treeview_click(item, column, False))
        treeview.setContextMenuPolicy(Qt.CustomContextMenu)
        treeview.customContextMenuRequested.connect(
            lambda pos: self.show_context_menu(treeview, pos, False)
        )
        self.treeviews["🔍 Søgeresultater"] = treeview
        self.tab_control.setCurrentIndex(self.tab_control.count() - 1)
    
    def add_category(self):
        global categories
        new_name, ok = QInputDialog.getText(self, "Ny kategori", "Indtast navn på ny kategori:")
        if ok and new_name:
            if new_name in categories:
                QMessageBox.warning(self, "Kategori findes", f"Kategorien '{new_name}' findes allerede.")
                return
            self.add_to_history("add_category", {"name": new_name})
            categories[new_name] = []
            save_categories(categories)
            self.refresh_tabs(self.tab_control.count())
    
    def edit_category(self):
        global categories
        idx = self.tab_control.currentIndex()
        if idx == -1:
            QMessageBox.warning(self, "Ingen kategori", "Vælg en kategori først.")
            return
        category = self.tab_control.tabText(idx)
        if category == "⭐ Favoritter":
            QMessageBox.warning(self, "Forkert kategori", "Du kan ikke omdøbe Favoritter.")
            return
        new_name, ok = QInputDialog.getText(self, "Omdøb kategori", "Indtast nyt navn til kategorien:", 
                                          QLineEdit.Normal, category)
        if ok and new_name and new_name != category:
            if new_name in categories:
                QMessageBox.warning(self, "Kategori findes", f"Kategorien '{new_name}' findes allerede.")
                return
            self.add_to_history("edit_category", {
                "old_name": category,
                "new_name": new_name,
                "commands": categories[category].copy()
            })
            categories[new_name] = categories[category]
            del categories[category]
            save_categories(categories)
            self.refresh_tabs(idx)
    
    def delete_category(self):
        global categories
        idx = self.tab_control.currentIndex()
        if idx == -1:
            QMessageBox.warning(self, "Ingen kategori", "Vælg en kategori først.")
            return
        category = self.tab_control.tabText(idx)
        if category == "⭐ Favoritter":
            QMessageBox.warning(self, "Forkert kategori", "Du kan ikke slette Favoritter.")
            return
        reply = QMessageBox.question(self, "Bekræft sletning", 
                                   f"Er du sikker på, at du vil slette kategorien '{category}' og alle dens kommandoer?",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.add_to_history("delete_category", {
                "name": category,
                "commands": categories[category].copy()
            })
            del categories[category]
            save_categories(categories)
            self.refresh_tabs()
    
    def add_command(self):
        global categories
        idx = self.tab_control.currentIndex()
        if idx == -1:
            QMessageBox.warning(self, "Ingen kategori", "Vælg en kategori først.")
            return
        category = self.tab_control.tabText(idx)
        if category == "⭐ Favoritter":
            QMessageBox.warning(self, "Forkert kategori", 
                              "Du kan ikke tilføje kommandoer direkte til Favoritter.\nVælg en anden kategori.")
            return
        dialog = CommandDialog(self, "Ny kommando", "Indtast PowerShell-kommando:")
        if dialog.exec():
            cmd = dialog.getText()
            if cmd:
                self.add_to_history("add_command", {"category": category, "command": cmd})
                categories[category].append({"text": cmd, "favorite": False})
                save_categories(categories)
                self.refresh_tabs(idx)
    
    def edit_command(self):
        global categories
        idx = self.tab_control.currentIndex()
        if idx == -1:
            QMessageBox.warning(self, "Ingen kategori", "Vælg en kategori først.")
            return
        category = self.tab_control.tabText(idx)
        if category not in self.treeviews:
            QMessageBox.warning(self, "Fejl", "Kunde ikke finde treeview for kategorien.")
            return
        tree = self.treeviews[category]
        selected_items = tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Ingen valgt", "Vælg en kommando.")
            return
        item = selected_items[0]
        if category == "🔍 Søgeresultater" and not item.parent():
            QMessageBox.warning(self, "Forkert valg", "Du kan ikke redigere en kategori-header.")
            return
        old_cmd_text = item.data(1, Qt.UserRole)
        dialog = CommandDialog(self, "Rediger kommando", "Ret PowerShell-kommando:", old_cmd_text)
        if dialog.exec():
            new_cmd_text = dialog.getText()
            if new_cmd_text and new_cmd_text != old_cmd_text:
                orig_category = category
                if category == "⭐ Favoritter" or category == "🔍 Søgeresultater":
                    orig_category = item.data(1, Qt.UserRole + 2)
                self.add_to_history("edit_command", {
                    "category": orig_category,
                    "old_text": old_cmd_text,
                    "new_text": new_cmd_text
                })
                for cmd in categories.get(orig_category, []):
                    if cmd.get("text") == old_cmd_text:
                        cmd["text"] = new_cmd_text
                        break
                save_categories(categories)
                self.refresh_tabs(idx)
    
    def delete_command(self):
        global categories
        idx = self.tab_control.currentIndex()
        if idx == -1:
            QMessageBox.warning(self, "Ingen kategori", "Vælg en kategori først.")
            return
        category = self.tab_control.tabText(idx)
        if category not in self.treeviews:
            QMessageBox.warning(self, "Fejl", "Kunde ikke finde treeview for kategorien.")
            return
        tree = self.treeviews[category]
        selected_items = tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Ingen valgt", "Vælg en kommando.")
            return
        item = selected_items[0]
        if category == "🔍 Søgeresultater" and not item.parent():
            QMessageBox.warning(self, "Forkert valg", "Du kan ikke slette en kategori-header.")
            return
        cmd_text = item.data(1, Qt.UserRole)
        reply = QMessageBox.question(self, "Slet", f"Slet denne kommando?\n\n{cmd_text}",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            orig_category = category
            cmd_obj = None
            if category == "⭐ Favoritter" or category == "🔍 Søgeresultater":
                orig_category = item.data(1, Qt.UserRole + 2)
                if orig_category:
                    for cmd in categories.get(orig_category, []):
                        if cmd.get("text") == cmd_text:
                            cmd_obj = cmd.copy()
                            break
                    self.add_to_history("delete_command", {
                        "category": orig_category,
                        "command": cmd_obj
                    })
                    for i, cmd in enumerate(categories.get(orig_category, [])):
                        if cmd.get("text") == cmd_text:
                            del categories[orig_category][i]
                            break
            else:
                for i, cmd in enumerate(categories[category]):
                    if cmd.get("text") == cmd_text:
                        cmd_obj = cmd.copy()
                        self.add_to_history("delete_command", {
                            "category": category,
                            "command": cmd_obj
                        })
                        del categories[category][i]
                        break
            save_categories(categories)
            self.refresh_tabs(idx)

# === MAIN ===
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    app.setQuitOnLastWindowClosed(True)
    app.setStyleSheet("""
        QWidget {
            background-color: #2D2D30;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 10pt;
        }
        /* Specifik styling for kommando text box - ingen farve override når syntaksfremhævning er aktiv */
        QTextEdit#commandTextBox {
            background-color: #1E1E1E !important;
            border: 1px solid #3F3F46 !important;
            border-radius: 8px !important;
            padding: 6px 8px !important;
            selection-background-color: #0078D7 !important;
            font-size: 12pt !important;
            /* Eksplicit ingen color property for at tillade syntaksfremhævning */
        }
        /* Andre QTextEdit elementer - INGEN farve override for kommando-boksen */
        QTextEdit {
            background-color: #1E1E1E;
            border: 1px solid #3F3F46;
            border-radius: 8px;
            padding: 6px 8px;
            selection-background-color: #0078D7;
            font-size: 12pt;
        }
        QLineEdit {
            background-color: #1E1E1E;
            color: #ffffff;
            border: 1px solid #3F3F46;
            border-radius: 8px;
            padding: 6px 8px;
            selection-background-color: #0078D7;
            font-size: 10pt;
        }
        QPushButton {
            background-color: #3E3E42;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: bold;
            min-height: 30px;
            font-size: 10pt;
        }
        QPushButton:hover {
            background-color: #505054;
        }
        QPushButton:pressed {
            background-color: #0078D7;
        }
        QPushButton:disabled {
            background-color: #3E3E42;
            color: #6B6B6B;
            border: 1px solid #3F3F46;
            opacity: 0.5;
        }
        QTreeWidget {
            background-color: #1E1E1E;
            color: #ffffff;
            border: 1px solid #3F3F46;
            border-radius: 8px;
            padding: 6px;
            selection-background-color: #0078D7;
            font-family: Arial, sans-serif;
            font-size: 10pt;
        }
        QTreeWidget::item {
            padding: 4px;
            border-radius: 6px;
            height: 24px;
            text-align: center;
        }
        QTreeWidget::item:selected {
            background-color: #0078D7;
            border-radius: 6px;
        }
        QTreeWidget::item:hover {
            background-color: #3E3E42;
            border-radius: 6px;
        }
        QTabWidget::pane {
            border: 1px solid #3F3F46;
            border-radius: 8px;
            background-color: #2D2D30;
            top: -1px;
        }
        QTabBar::tab {
            background-color: #252526;
            color: #ffffff;
            padding: 10px 20px;
            margin-right: 4px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            font-size: 10pt;
        }
        QTabBar::tab:selected {
            background-color: #0078D7;
            font-weight: bold;
            border-bottom: 2px solid #0078D7;
        }
        QTabBar::tab:hover:!selected {
            background-color: #3E3E42;
        }
        QGroupBox {
            border: 1px solid #3F3F46;
            border-radius: 8px;
            margin-top: 18px;
            font-weight: bold;
            padding-top: 18px;
            font-size: 10pt;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 6px;
            background-color: #2D2D30;
        }
        QScrollBar:vertical {
            border: none;
            background-color: #2D2D30;
            width: 14px;
            margin: 0px;
            border-radius: 7px;
        }
        QScrollBar::handle:vertical {
            background-color: #3E3E42;
            border-radius: 7px;
            min-height: 20px;
            margin: 2px;
        }
        QScrollBar::handle:vertical:hover {
            background-color: #505054;
        }
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }
        QScrollBar:horizontal {
            border: none;
            background-color: #2D2D30;
            height: 14px;
            margin: 0px;
            border-radius: 7px;
        }
        QScrollBar::handle:horizontal {
            background-color: #3E3E42;
            border-radius: 7px;
            min-width: 20px;
            margin: 2px;
        }
        QScrollBar::handle:horizontal:hover {
            background-color: #505054;
        }
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
            width: 0px;
        }
        QLabel {
            padding: 4px;
            font-size: 10pt;
        }
        QFrame#hero_frame {
            background-color: #0078D7;
            color: white;
            border-radius: 8px;
            padding: 8px;
        }
        QStatusBar {
            background-color: #0078D7;
            color: white;
            border-top-left-radius: 0px;
            border-top-right-radius: 0px;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            font-size: 10pt;
        }
        QMenu {
            background-color: #1E1E1E;
            color: white;
            border: 1px solid #3F3F46;
            border-radius: 8px;
            padding: 6px;
            font-size: 10pt;
        }
        QMenu::item {
            padding: 8px 28px 8px 14px;
            border-radius: 6px;
        }
        QMenu::item:selected {
            background-color: #0078D7;
            border-radius: 6px;
        }
        QMenu::separator {
            height: 1px;
            background-color: #3F3F46;
            margin: 6px 8px;
        }
    """)
    window = PowerShellOneLiners()
    window.show()
    sys.exit(app.exec())