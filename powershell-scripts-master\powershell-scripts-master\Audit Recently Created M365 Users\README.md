﻿## Find Who Created a User Account in Microsoft 365 | Audit User Creations
This PowerShell script helps to find who created a user account in Microsoft 365 with ease. Download the script now and identify the suspicious accounts.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![Audit User Creations Report](https://o365reports.com/wp-content/uploads/2023/08/Find-who-created-a-user-account-in-Microosft-365-using-PowerShell-1024x286.png?v=**********)
## Microsoft 365 Reporting Tool by AdminDroid
For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*AdminDroid: Export, Schedule, and Receive Alerts – All for Free! <https://demo.admindroid.com/#/1/11/reports/20001/1/20>*

