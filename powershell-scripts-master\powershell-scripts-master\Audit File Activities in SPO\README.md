﻿# Track File Activities in SharePoint Online Using PowerShell
Audit file activities in SharePoint Online and OneDrive to identify suspicious activities, excessive privileges, and safeguard data.

***Sample Output:***

The script generates all the file and folder activities in SharePoint Online and OneDrive for the past 180 days.

![Audit File Usage Report](https://o365reports.com/wp-content/uploads/2024/07/2024-07-30-16\_41\_17-FileUsageAuditReport\_2024-Jul-30-Tue-10-42-AM.csv.png?v=1722413801)
## Microsoft 365 Auditing Tool by AdminDroid
Looking for more comprehensive auditing? [AdminDroid Microsoft 365 auditing tool](https://admindroid.com/?src=GitHub) contains 1900+ reports and 30+ stunning dashboards along with more valuable features to make your Microsoft 365 management a cakewalk.

*View detailed SharePoint Online file activities and sharing reports in AdminDroid:*

[*https://demo.admindroid.com/#/1/11/reports/22099/1/20*](https://demo.admindroid.com/#/1/11/reports/22099/1/20)

