## Export Microsoft Teams Shared Channel Members Report

This PowerShell script helps admins to export Microsoft Teams shared channel report and it’s membership details to CSV file.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Microsoft Teams Shared Channel Report](https://o365reports.com/wp-content/uploads/2023/02/export-shared-channel-report-using-PowerShell.png?v=1705576313)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for detailed reporting? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, it seamlessly complements your PowerShell scripts.

*View more comprehensive shared channel members report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/60102/1/20>*  



