#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test af QSyntaxHighlighter implementering
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# Import klasser
import importlib.util
spec = importlib.util.spec_from_file_location("test_apexamples", os.path.join(os.path.dirname(__file__), "Test-APExamples.py"))
test_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(test_module)
ScrolledTextEdit = test_module.ScrolledTextEdit
PowerShellSyntaxHighlighter = test_module.PowerShellSyntaxHighlighter

class SyntaxHighlighterTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("QSyntaxHighlighter Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Info label
        info_label = QLabel("QSyntaxHighlighter PowerShell Test:")
        info_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        layout.addWidget(info_label)
        
        # Text editor med QSyntaxHighlighter
        self.text_edit = ScrolledTextEdit()
        self.text_edit.setFont(QFont("Consolas", 12))
        self.text_edit.powershell_syntax_enabled = True
        self.text_edit.update_syntax_colors("dark")
        layout.addWidget(self.text_edit)
        
        # Test knapper
        test1_btn = QPushButton("Test 1: Exchange Online Kommandoer")
        test1_btn.clicked.connect(self.test_exchange)
        layout.addWidget(test1_btn)
        
        test2_btn = QPushButton("Test 2: PowerShell Script")
        test2_btn.clicked.connect(self.test_script)
        layout.addWidget(test2_btn)
        
        test3_btn = QPushButton("Test 3: Kompleks Kommando")
        test3_btn.clicked.connect(self.test_complex)
        layout.addWidget(test3_btn)
        
        toggle_btn = QPushButton("Toggle Syntaksfremhævning")
        toggle_btn.clicked.connect(self.toggle_syntax)
        layout.addWidget(toggle_btn)
        
        # Indlæs første test
        self.test_exchange()
        
    def test_exchange(self):
        test_code = """# Exchange Online kommandoer
Get-Mailbox -Identity "<EMAIL>" -RecipientTypeDetails UserMailbox
Set-MailboxPermission -Identity "SharedMailbox" -User "<EMAIL>" -AccessRights FullAccess
New-DistributionGroup -Name "TestGroup" -DisplayName "Test Distribution Group"
Get-MessageTrace -SenderAddress "<EMAIL>" -StartDate (Get-Date).AddDays(-7)"""
        self.text_edit.setText(test_code)
        
    def test_script(self):
        test_code = """# PowerShell script med variabler og kontrol strukturer
$users = Get-ADUser -Filter * -Properties Department
$count = 0

foreach ($user in $users) {
    if ($user.Department -eq "IT" -and $user.Enabled -eq $true) {
        Write-Host "Processing user: $($user.Name)" -ForegroundColor Green
        $count++
    }
    elseif ($user.Department -like "*Sales*") {
        Write-Warning "Sales user found: $($user.Name)"
    }
}

if ($count -gt 10) {
    Write-Output "Found $count IT users"
}"""
        self.text_edit.setText(test_code)
        
    def test_complex(self):
        test_code = """# Kompleks PowerShell kommando med pipes og operatorer
Get-Process | Where-Object {$_.CPU -gt 100 -and $_.WorkingSet -gt 50MB} | 
    Sort-Object CPU -Descending | 
    Select-Object Name, CPU, WorkingSet, @{Name="MemoryMB";Expression={[math]::Round($_.WorkingSet/1MB,2)}} |
    Export-Csv -Path "C:\\temp\\processes.csv" -NoTypeInformation"""
        self.text_edit.setText(test_code)
        
    def toggle_syntax(self):
        self.text_edit.powershell_syntax_enabled = not self.text_edit.powershell_syntax_enabled
        current_text = self.text_edit.toPlainText()
        self.text_edit.setText(current_text)
        print(f"Syntax highlighting: {'ON' if self.text_edit.powershell_syntax_enabled else 'OFF'}")

def main():
    app = QApplication(sys.argv)
    
    # Mørkt tema styling
    app.setStyleSheet("""
        QMainWindow, QWidget {
            background-color: #2d2d30;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        QPushButton {
            background-color: #0e639c;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
            min-height: 30px;
        }
        QPushButton:hover {
            background-color: #1177bb;
        }
        QTextEdit {
            background-color: #1E1E1E;
            border: 1px solid #3F3F46;
            border-radius: 8px;
            padding: 6px 8px;
            selection-background-color: #0078D7;
            font-size: 12pt;
        }
        QLabel {
            color: #ffffff;
            padding: 8px;
        }
    """)
    
    window = SyntaxHighlighterTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
