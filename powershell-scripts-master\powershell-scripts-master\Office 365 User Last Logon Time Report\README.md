## Export Office 365 Users’ Last Logon Time to CSV Using PowerShell
`     `Export Office 365 Last Logon Time report to CSV with UserPrincipalName, DisplayName, LastLogonTime, InactiveDays, MailboxType, Licenses, Admin Roles.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below

![Last Logon Time Report](https://o365reports.com/wp-content/uploads/2019/03/Export-office-365-users-last-logon-time-to-CSV.png?v=**********).

## Microsoft 365 Reporting Tool by AdminDroid 
Explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), featuring a diverse range of over 1800 reports for gaining detailed insights and a comprehensive understanding of your M365 environment.

*Export Office 365 Users’ Last Logon Time Report to CSV using AdminDroid to obtain detailed and actionable insights:<https://demo.admindroid.com/#/1/11/reportboard/2/1/1/2/reports/80002/1/20?source=%7B%22boardId%22%3A2%7D>*

