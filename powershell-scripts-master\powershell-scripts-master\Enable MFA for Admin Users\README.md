﻿## Enabling MFA for Microsoft 365 Admins Using PowerShell
This PowerShell script identifies Office 365 admins without MFA and enables MFA for them. Also, you can enable MFA considering license status.

***Sample Output:***

This script lets you filter based on license status and enable MFA to them. This script exports an output CSV file that resembles the screenshot below. 

![Enabling MFA for Admins]( https://m365scripts.com/wp-content/uploads/2022/03/Exported-CSV-file-for-all-admins-1.png)

## Microsoft 365 Reporting tool by AdminDroid
Looking for a powerful Microsoft 365 reporting solution? [AdminDroid](https://admindroid.com/?src=GitHub) offers over 1800 pre-built reports and intuitive dashboards, giving you a deeper understanding and better control of your data.

*Utilize detailed MFA data insights with AdminDroid's comprehensive Admin MFA reports: [https://demo.admindroid.com/#/1/11/dashboards/59](https://demo.admindroid.com/#/1/11/dashboards/59)*

