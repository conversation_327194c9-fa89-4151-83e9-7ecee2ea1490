## Get Mailbox Folder Permission Report Using PowerShell

Get detailed insights on mailbox folder permissions in Exchange Online to prevent excessive folder privileges and improve mailbox security.

***Sample Output:***

This script exports an output CSV file that looks like the screenshot below.

![Mailbox Folder Permission Report](https://o365reports.com/wp-content/uploads/2024/06/MailboxFolderPermissionReport-Output.png?v=1717565256)

## Microsoft 365 Reporting tool by AdminDroid

Take your mailbox folder management to the next level with the [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1800+ pre-built reports and dashboards for in-depth analysis and efficient oversight.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/0/reports/21016/1/20>*



