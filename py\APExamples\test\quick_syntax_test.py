#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hurtig test af PowerShell syntaksfremhævning
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# Import ScrolledTextEdit
import importlib.util
spec = importlib.util.spec_from_file_location("test_apexamples", os.path.join(os.path.dirname(__file__), "Test-APExamples.py"))
test_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(test_module)
ScrolledTextEdit = test_module.ScrolledTextEdit

class QuickTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Hurtig Syntaks Test")
        self.setGeometry(100, 100, 600, 400)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Text editor
        self.text_edit = ScrolledTextEdit()
        self.text_edit.setFont(QFont("Consolas", 12))
        self.text_edit.powershell_syntax_enabled = True
        self.text_edit.update_syntax_colors("dark")
        layout.addWidget(self.text_edit)
        
        # Test knap
        test_btn = QPushButton("Test PowerShell Kommando")
        test_btn.clicked.connect(self.load_test)
        layout.addWidget(test_btn)
        
        # Indlæs test
        self.load_test()
        
    def load_test(self):
        test_code = """# Test PowerShell kommando
Get-Mailbox -Identity "<EMAIL>" | Set-Mailbox -ProhibitSendQuota "2GB"
$users = Get-ADUser -Filter {Department -eq "IT"}
if ($users.Count -gt 0) {
    Write-Host "Found $($users.Count) users" -ForegroundColor Green
}"""
        self.text_edit.setText(test_code)

def main():
    app = QApplication(sys.argv)
    
    # Mørkt tema
    app.setStyleSheet("""
        QMainWindow, QWidget {
            background-color: #2d2d30;
            color: #ffffff;
        }
        QPushButton {
            background-color: #0e639c;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #1177bb;
        }
        QTextEdit {
            background-color: #1E1E1E;
            border: 1px solid #3F3F46;
            border-radius: 8px;
            padding: 6px 8px;
        }
    """)
    
    window = QuickTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
