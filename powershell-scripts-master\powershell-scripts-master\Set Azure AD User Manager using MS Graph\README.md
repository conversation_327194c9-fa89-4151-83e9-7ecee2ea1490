## Set up Manager for Office 365 Users Based on the User’s Property
Set up manager for Office 365 users by Set-AzureADUserManager. This PowerShell script sets up for multiple users based on 10+user properties.

***Sample Output:***

Check out the below screenshot to see the script in action.

![Set Azure AD User Manager using MS Graph](https://m365scripts.com/wp-content/uploads/2022/02/Assign-Manager-to-Office-365-using-specific-property.png?v=1701520119)
## Microsoft 365 Reporting Tool by AdminDroid
Seeking in-depth analysis? [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, perfectly complementing your PowerShell scripts.

*View more comprehensive Microsoft 365 manager report through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/20/1/20>*
