**********************
Windows PowerShell transcript start
Start time: 20220216154550
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Heic_Converter\Scripts\Bulk.ps1
Process ID: 31716
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Heic_Converter\Scripts\log.txt
C:\Heic_Converter\Input\IMG_1085.HEIC
 -> IMG_1085.HEIC.jpg
C:\Heic_Converter\Input\IMG_1086.HEIC
 -> IMG_1086.HEIC.jpg
C:\Heic_Converter\Input\IMG_1087.HEIC
 -> IMG_1087.HEIC.jpg
C:\Heic_Converter\Input\IMG_1088.HEIC
 -> IMG_1088.HEIC.jpg
C:\Heic_Converter\Input\IMG_1089.HEIC
 -> IMG_1089.HEIC.jpg
C:\Heic_Converter\Input\IMG_1090.HEIC
 -> IMG_1090.HEIC.jpg
C:\Heic_Converter\Input\IMG_1091.HEIC
 -> IMG_1091.HEIC.jpg
C:\Heic_Converter\Input\IMG_1092.HEIC
 -> IMG_1092.HEIC.jpg
C:\Heic_Converter\Input\IMG_1093.HEIC
 -> IMG_1093.HEIC.jpg
C:\Heic_Converter\Input\IMG_1094.HEIC
 -> IMG_1094.HEIC.jpg
C:\Heic_Converter\Input\IMG_1095.HEIC
 -> IMG_1095.HEIC.jpg
C:\Heic_Converter\Input\IMG_1096.HEIC
 -> IMG_1096.HEIC.jpg
C:\Heic_Converter\Input\IMG_1097.HEIC
 -> IMG_1097.HEIC.jpg
C:\Heic_Converter\Input\IMG_1098.HEIC
 -> IMG_1098.HEIC.jpg
C:\Heic_Converter\Input\IMG_1099.HEIC
 -> IMG_1099.HEIC.jpg
C:\Heic_Converter\Input\IMG_1100.HEIC
 -> IMG_1100.HEIC.jpg
C:\Heic_Converter\Input\IMG_1101.HEIC
 -> IMG_1101.HEIC.jpg
C:\Heic_Converter\Input\IMG_1102.HEIC
 -> IMG_1102.HEIC.jpg
C:\Heic_Converter\Input\IMG_1103.HEIC
 -> IMG_1103.HEIC.jpg
C:\Heic_Converter\Input\IMG_1104.HEIC
 -> IMG_1104.HEIC.jpg
C:\Heic_Converter\Input\IMG_1105.HEIC
 -> IMG_1105.HEIC.jpg
C:\Heic_Converter\Input\IMG_1106.HEIC
 -> IMG_1106.HEIC.jpg
C:\Heic_Converter\Input\IMG_1107.HEIC
 -> IMG_1107.HEIC.jpg
C:\Heic_Converter\Input\IMG_1108.HEIC
 -> IMG_1108.HEIC.jpg
C:\Heic_Converter\Input\IMG_1109.HEIC
 -> IMG_1109.HEIC.jpg
C:\Heic_Converter\Input\IMG_1110.HEIC
 -> IMG_1110.HEIC.jpg
C:\Heic_Converter\Input\IMG_1111.HEIC
 -> IMG_1111.HEIC.jpg
C:\Heic_Converter\Input\IMG_1112.HEIC
 -> IMG_1112.HEIC.jpg
C:\Heic_Converter\Input\IMG_1113.HEIC
 -> IMG_1113.HEIC.jpg
C:\Heic_Converter\Input\IMG_1114.HEIC
 -> IMG_1114.HEIC.jpg
C:\Heic_Converter\Input\IMG_1115.HEIC
 -> IMG_1115.HEIC.jpg
C:\Heic_Converter\Input\IMG_1116.HEIC
 -> IMG_1116.HEIC.jpg
C:\Heic_Converter\Input\IMG_1117.HEIC
 -> IMG_1117.HEIC.jpg
C:\Heic_Converter\Input\IMG_1118.HEIC
 -> IMG_1118.HEIC.jpg
C:\Heic_Converter\Input\IMG_1119.HEIC
 -> IMG_1119.HEIC.jpg
C:\Heic_Converter\Input\IMG_1120.HEIC
 -> IMG_1120.HEIC.jpg
C:\Heic_Converter\Input\IMG_1121.HEIC
 -> IMG_1121.HEIC.jpg
C:\Heic_Converter\Input\IMG_1122.HEIC
 -> IMG_1122.HEIC.jpg
C:\Heic_Converter\Input\IMG_1123.HEIC
 -> IMG_1123.HEIC.jpg
C:\Heic_Converter\Input\IMG_1124.HEIC
 -> IMG_1124.HEIC.jpg
C:\Heic_Converter\Input\IMG_1125.HEIC
 -> IMG_1125.HEIC.jpg
C:\Heic_Converter\Input\IMG_1126.HEIC
 -> IMG_1126.HEIC.jpg
C:\Heic_Converter\Input\IMG_1127.HEIC
 -> IMG_1127.HEIC.jpg
C:\Heic_Converter\Input\IMG_1128.HEIC
 -> IMG_1128.HEIC.jpg
C:\Heic_Converter\Input\IMG_1129.HEIC
 -> IMG_1129.HEIC.jpg
C:\Heic_Converter\Input\IMG_1130.HEIC
 -> IMG_1130.HEIC.jpg
C:\Heic_Converter\Input\IMG_1131.HEIC
 -> IMG_1131.HEIC.jpg
C:\Heic_Converter\Input\IMG_1132.HEIC
 -> IMG_1132.HEIC.jpg
C:\Heic_Converter\Input\IMG_1133.HEIC
 -> IMG_1133.HEIC.jpg
C:\Heic_Converter\Input\IMG_1134.HEIC
 -> IMG_1134.HEIC.jpg
C:\Heic_Converter\Input\IMG_1135.HEIC
 -> IMG_1135.HEIC.jpg
C:\Heic_Converter\Input\IMG_1136.HEIC
 -> IMG_1136.HEIC.jpg
C:\Heic_Converter\Input\IMG_1137.HEIC
 -> IMG_1137.HEIC.jpg
C:\Heic_Converter\Input\IMG_1138.HEIC
 -> IMG_1138.HEIC.jpg
C:\Heic_Converter\Input\IMG_1139.HEIC
 -> IMG_1139.HEIC.jpg
C:\Heic_Converter\Input\IMG_1140.HEIC
 -> IMG_1140.HEIC.jpg
C:\Heic_Converter\Input\IMG_1141.HEIC
 -> IMG_1141.HEIC.jpg
C:\Heic_Converter\Input\IMG_1142.HEIC
 -> IMG_1142.HEIC.jpg
C:\Heic_Converter\Input\IMG_1143.HEIC
 -> IMG_1143.HEIC.jpg
C:\Heic_Converter\Input\IMG_1144.HEIC
 -> IMG_1144.HEIC.jpg
C:\Heic_Converter\Input\IMG_1145.HEIC
 -> IMG_1145.HEIC.jpg
C:\Heic_Converter\Input\IMG_1146.HEIC
 -> IMG_1146.HEIC.jpg
C:\Heic_Converter\Input\IMG_1147.HEIC
 -> IMG_1147.HEIC.jpg
C:\Heic_Converter\Input\IMG_1148.HEIC
 -> IMG_1148.HEIC.jpg
C:\Heic_Converter\Input\IMG_1149.HEIC
 -> IMG_1149.HEIC.jpg
C:\Heic_Converter\Input\IMG_1150.HEIC
 -> IMG_1150.HEIC.jpg
C:\Heic_Converter\Input\IMG_1151.HEIC
 -> IMG_1151.HEIC.jpg
C:\Heic_Converter\Input\IMG_1152.HEIC
 -> IMG_1152.HEIC.jpg
C:\Heic_Converter\Input\IMG_1153.HEIC
 -> IMG_1153.HEIC.jpg
C:\Heic_Converter\Input\IMG_1154.HEIC
 -> IMG_1154.HEIC.jpg
C:\Heic_Converter\Input\IMG_1155.HEIC
 -> IMG_1155.HEIC.jpg
C:\Heic_Converter\Input\IMG_1156.HEIC
 -> IMG_1156.HEIC.jpg
C:\Heic_Converter\Input\IMG_1157.HEIC
 -> IMG_1157.HEIC.jpg
C:\Heic_Converter\Input\IMG_1158.HEIC
 -> IMG_1158.HEIC.jpg
C:\Heic_Converter\Input\IMG_1159.HEIC
 -> IMG_1159.HEIC.jpg
C:\Heic_Converter\Input\IMG_1160.HEIC
 -> IMG_1160.HEIC.jpg
C:\Heic_Converter\Input\IMG_1161.HEIC
 -> IMG_1161.HEIC.jpg
C:\Heic_Converter\Input\IMG_1162.HEIC
 -> IMG_1162.HEIC.jpg
C:\Heic_Converter\Input\IMG_1163.HEIC
 -> IMG_1163.HEIC.jpg
C:\Heic_Converter\Input\IMG_1164.HEIC
 -> IMG_1164.HEIC.jpg
C:\Heic_Converter\Input\IMG_1165.HEIC
 -> IMG_1165.HEIC.jpg
C:\Heic_Converter\Input\IMG_1166.HEIC
 -> IMG_1166.HEIC.jpg
C:\Heic_Converter\Input\IMG_1167.HEIC
 -> IMG_1167.HEIC.jpg
C:\Heic_Converter\Input\IMG_1168.HEIC
 -> IMG_1168.HEIC.jpg
C:\Heic_Converter\Input\IMG_1169.HEIC
 -> IMG_1169.HEIC.jpg
C:\Heic_Converter\Input\IMG_1170.HEIC
 -> IMG_1170.HEIC.jpg
C:\Heic_Converter\Input\IMG_1171.HEIC
 -> IMG_1171.HEIC.jpg
C:\Heic_Converter\Input\IMG_1172.HEIC
 -> IMG_1172.HEIC.jpg
C:\Heic_Converter\Input\IMG_1173.HEIC
 -> IMG_1173.HEIC.jpg
C:\Heic_Converter\Input\IMG_1174.HEIC
 -> IMG_1174.HEIC.jpg
C:\Heic_Converter\Input\IMG_1175.HEIC
 -> IMG_1175.HEIC.jpg
C:\Heic_Converter\Input\IMG_1176.HEIC
 -> IMG_1176.HEIC.jpg
C:\Heic_Converter\Input\IMG_1177.HEIC
 -> IMG_1177.HEIC.jpg
C:\Heic_Converter\Input\IMG_1178.HEIC
 -> IMG_1178.HEIC.jpg
C:\Heic_Converter\Input\IMG_1179.HEIC
 -> IMG_1179.HEIC.jpg
C:\Heic_Converter\Input\IMG_1180.HEIC
 -> IMG_1180.HEIC.jpg
C:\Heic_Converter\Input\IMG_1181.HEIC
 -> IMG_1181.HEIC.jpg
C:\Heic_Converter\Input\IMG_1182.HEIC
 -> IMG_1182.HEIC.jpg
C:\Heic_Converter\Input\IMG_1183.HEIC
 -> IMG_1183.HEIC.jpg
C:\Heic_Converter\Input\IMG_1184.HEIC
 -> IMG_1184.HEIC.jpg
C:\Heic_Converter\Input\IMG_1185.HEIC
 -> IMG_1185.HEIC.jpg
C:\Heic_Converter\Input\IMG_1186.HEIC
 -> IMG_1186.HEIC.jpg
C:\Heic_Converter\Input\IMG_1187.HEIC
 -> IMG_1187.HEIC.jpg
C:\Heic_Converter\Input\IMG_1188.HEIC
 -> IMG_1188.HEIC.jpg
C:\Heic_Converter\Input\IMG_1189.HEIC
 -> IMG_1189.HEIC.jpg
C:\Heic_Converter\Input\IMG_1215.HEIC
 -> IMG_1215.HEIC.jpg
C:\Heic_Converter\Input\IMG_1216.HEIC
 -> IMG_1216.HEIC.jpg
C:\Heic_Converter\Input\IMG_1217.HEIC
 -> IMG_1217.HEIC.jpg
C:\Heic_Converter\Input\IMG_1218.HEIC
 -> IMG_1218.HEIC.jpg
C:\Heic_Converter\Input\IMG_1219.HEIC
 -> IMG_1219.HEIC.jpg
C:\Heic_Converter\Input\IMG_1220.HEIC
 -> IMG_1220.HEIC.jpg
C:\Heic_Converter\Input\IMG_1221.HEIC
 -> IMG_1221.HEIC.jpg
C:\Heic_Converter\Input\IMG_1222.HEIC
 -> IMG_1222.HEIC.jpg
C:\Heic_Converter\Input\IMG_1223.HEIC
 -> IMG_1223.HEIC.jpg
C:\Heic_Converter\Input\IMG_1224.HEIC
 -> IMG_1224.HEIC.jpg
C:\Heic_Converter\Input\IMG_1225.HEIC
 -> IMG_1225.HEIC.jpg
C:\Heic_Converter\Input\IMG_1226.HEIC
 -> IMG_1226.HEIC.jpg
C:\Heic_Converter\Input\IMG_1227.HEIC
 -> IMG_1227.HEIC.jpg
16-02-2022 15:47:24 Convertion completed
**********************
Windows PowerShell transcript end
End time: 20220216154807
**********************
**********************
Windows PowerShell transcript start
Start time: 20220216155424
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Heic_Converter\Scripts\Bulk.ps1
Process ID: 15788
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Heic_Converter\Scripts\log.txt
C:\Heic_Converter\Input\IMG_1085.HEIC
 -> IMG_1085.HEIC.jpg
C:\Heic_Converter\Input\IMG_1086.HEIC
 -> IMG_1086.HEIC.jpg
C:\Heic_Converter\Input\IMG_1087.HEIC
 -> IMG_1087.HEIC.jpg
C:\Heic_Converter\Input\IMG_1088.HEIC
 -> IMG_1088.HEIC.jpg
C:\Heic_Converter\Input\IMG_1089.HEIC
 -> IMG_1089.HEIC.jpg
C:\Heic_Converter\Input\IMG_1090.HEIC
 -> IMG_1090.HEIC.jpg
C:\Heic_Converter\Input\IMG_1091.HEIC
 -> IMG_1091.HEIC.jpg
C:\Heic_Converter\Input\IMG_1092.HEIC
 -> IMG_1092.HEIC.jpg
C:\Heic_Converter\Input\IMG_1093.HEIC
 -> IMG_1093.HEIC.jpg
C:\Heic_Converter\Input\IMG_1094.HEIC
 -> IMG_1094.HEIC.jpg
C:\Heic_Converter\Input\IMG_1095.HEIC
 -> IMG_1095.HEIC.jpg
C:\Heic_Converter\Input\IMG_1096.HEIC
 -> IMG_1096.HEIC.jpg
C:\Heic_Converter\Input\IMG_1097.HEIC
 -> IMG_1097.HEIC.jpg
C:\Heic_Converter\Input\IMG_1098.HEIC
 -> IMG_1098.HEIC.jpg
C:\Heic_Converter\Input\IMG_1099.HEIC
 -> IMG_1099.HEIC.jpg
C:\Heic_Converter\Input\IMG_1100.HEIC
 -> IMG_1100.HEIC.jpg
C:\Heic_Converter\Input\IMG_1101.HEIC
 -> IMG_1101.HEIC.jpg
C:\Heic_Converter\Input\IMG_1102.HEIC
 -> IMG_1102.HEIC.jpg
C:\Heic_Converter\Input\IMG_1103.HEIC
 -> IMG_1103.HEIC.jpg
C:\Heic_Converter\Input\IMG_1104.HEIC
 -> IMG_1104.HEIC.jpg
C:\Heic_Converter\Input\IMG_1105.HEIC
 -> IMG_1105.HEIC.jpg
C:\Heic_Converter\Input\IMG_1106.HEIC
 -> IMG_1106.HEIC.jpg
C:\Heic_Converter\Input\IMG_1107.HEIC
 -> IMG_1107.HEIC.jpg
C:\Heic_Converter\Input\IMG_1108.HEIC
 -> IMG_1108.HEIC.jpg
C:\Heic_Converter\Input\IMG_1109.HEIC
 -> IMG_1109.HEIC.jpg
C:\Heic_Converter\Input\IMG_1110.HEIC
 -> IMG_1110.HEIC.jpg
C:\Heic_Converter\Input\IMG_1111.HEIC
 -> IMG_1111.HEIC.jpg
C:\Heic_Converter\Input\IMG_1112.HEIC
 -> IMG_1112.HEIC.jpg
C:\Heic_Converter\Input\IMG_1113.HEIC
 -> IMG_1113.HEIC.jpg
C:\Heic_Converter\Input\IMG_1114.HEIC
 -> IMG_1114.HEIC.jpg
C:\Heic_Converter\Input\IMG_1115.HEIC
 -> IMG_1115.HEIC.jpg
C:\Heic_Converter\Input\IMG_1116.HEIC
 -> IMG_1116.HEIC.jpg
C:\Heic_Converter\Input\IMG_1117.HEIC
 -> IMG_1117.HEIC.jpg
C:\Heic_Converter\Input\IMG_1118.HEIC
 -> IMG_1118.HEIC.jpg
C:\Heic_Converter\Input\IMG_1119.HEIC
 -> IMG_1119.HEIC.jpg
C:\Heic_Converter\Input\IMG_1120.HEIC
 -> IMG_1120.HEIC.jpg
C:\Heic_Converter\Input\IMG_1121.HEIC
 -> IMG_1121.HEIC.jpg
C:\Heic_Converter\Input\IMG_1122.HEIC
 -> IMG_1122.HEIC.jpg
C:\Heic_Converter\Input\IMG_1123.HEIC
 -> IMG_1123.HEIC.jpg
C:\Heic_Converter\Input\IMG_1124.HEIC
 -> IMG_1124.HEIC.jpg
C:\Heic_Converter\Input\IMG_1125.HEIC
 -> IMG_1125.HEIC.jpg
C:\Heic_Converter\Input\IMG_1126.HEIC
 -> IMG_1126.HEIC.jpg
C:\Heic_Converter\Input\IMG_1127.HEIC
 -> IMG_1127.HEIC.jpg
C:\Heic_Converter\Input\IMG_1128.HEIC
 -> IMG_1128.HEIC.jpg
C:\Heic_Converter\Input\IMG_1129.HEIC
 -> IMG_1129.HEIC.jpg
C:\Heic_Converter\Input\IMG_1130.HEIC
 -> IMG_1130.HEIC.jpg
C:\Heic_Converter\Input\IMG_1131.HEIC
 -> IMG_1131.HEIC.jpg
C:\Heic_Converter\Input\IMG_1132.HEIC
 -> IMG_1132.HEIC.jpg
C:\Heic_Converter\Input\IMG_1133.HEIC
 -> IMG_1133.HEIC.jpg
C:\Heic_Converter\Input\IMG_1134.HEIC
 -> IMG_1134.HEIC.jpg
C:\Heic_Converter\Input\IMG_1135.HEIC
 -> IMG_1135.HEIC.jpg
C:\Heic_Converter\Input\IMG_1136.HEIC
 -> IMG_1136.HEIC.jpg
C:\Heic_Converter\Input\IMG_1137.HEIC
 -> IMG_1137.HEIC.jpg
C:\Heic_Converter\Input\IMG_1138.HEIC
 -> IMG_1138.HEIC.jpg
C:\Heic_Converter\Input\IMG_1139.HEIC
 -> IMG_1139.HEIC.jpg
C:\Heic_Converter\Input\IMG_1140.HEIC
 -> IMG_1140.HEIC.jpg
C:\Heic_Converter\Input\IMG_1141.HEIC
 -> IMG_1141.HEIC.jpg
C:\Heic_Converter\Input\IMG_1142.HEIC
 -> IMG_1142.HEIC.jpg
C:\Heic_Converter\Input\IMG_1143.HEIC
 -> IMG_1143.HEIC.jpg
C:\Heic_Converter\Input\IMG_1144.HEIC
 -> IMG_1144.HEIC.jpg
C:\Heic_Converter\Input\IMG_1145.HEIC
 -> IMG_1145.HEIC.jpg
C:\Heic_Converter\Input\IMG_1146.HEIC
 -> IMG_1146.HEIC.jpg
C:\Heic_Converter\Input\IMG_1147.HEIC
 -> IMG_1147.HEIC.jpg
C:\Heic_Converter\Input\IMG_1148.HEIC
 -> IMG_1148.HEIC.jpg
C:\Heic_Converter\Input\IMG_1149.HEIC
 -> IMG_1149.HEIC.jpg
C:\Heic_Converter\Input\IMG_1150.HEIC
 -> IMG_1150.HEIC.jpg
C:\Heic_Converter\Input\IMG_1151.HEIC
 -> IMG_1151.HEIC.jpg
C:\Heic_Converter\Input\IMG_1152.HEIC
 -> IMG_1152.HEIC.jpg
C:\Heic_Converter\Input\IMG_1153.HEIC
 -> IMG_1153.HEIC.jpg
C:\Heic_Converter\Input\IMG_1154.HEIC
 -> IMG_1154.HEIC.jpg
C:\Heic_Converter\Input\IMG_1155.HEIC
 -> IMG_1155.HEIC.jpg
C:\Heic_Converter\Input\IMG_1156.HEIC
 -> IMG_1156.HEIC.jpg
C:\Heic_Converter\Input\IMG_1157.HEIC
 -> IMG_1157.HEIC.jpg
C:\Heic_Converter\Input\IMG_1158.HEIC
 -> IMG_1158.HEIC.jpg
C:\Heic_Converter\Input\IMG_1159.HEIC
 -> IMG_1159.HEIC.jpg
C:\Heic_Converter\Input\IMG_1160.HEIC
 -> IMG_1160.HEIC.jpg
C:\Heic_Converter\Input\IMG_1161.HEIC
 -> IMG_1161.HEIC.jpg
C:\Heic_Converter\Input\IMG_1162.HEIC
 -> IMG_1162.HEIC.jpg
C:\Heic_Converter\Input\IMG_1163.HEIC
 -> IMG_1163.HEIC.jpg
C:\Heic_Converter\Input\IMG_1164.HEIC
 -> IMG_1164.HEIC.jpg
C:\Heic_Converter\Input\IMG_1165.HEIC
 -> IMG_1165.HEIC.jpg
C:\Heic_Converter\Input\IMG_1166.HEIC
 -> IMG_1166.HEIC.jpg
C:\Heic_Converter\Input\IMG_1167.HEIC
 -> IMG_1167.HEIC.jpg
C:\Heic_Converter\Input\IMG_1168.HEIC
 -> IMG_1168.HEIC.jpg
C:\Heic_Converter\Input\IMG_1169.HEIC
 -> IMG_1169.HEIC.jpg
C:\Heic_Converter\Input\IMG_1170.HEIC
 -> IMG_1170.HEIC.jpg
C:\Heic_Converter\Input\IMG_1171.HEIC
 -> IMG_1171.HEIC.jpg
C:\Heic_Converter\Input\IMG_1172.HEIC
 -> IMG_1172.HEIC.jpg
C:\Heic_Converter\Input\IMG_1173.HEIC
 -> IMG_1173.HEIC.jpg
C:\Heic_Converter\Input\IMG_1174.HEIC
 -> IMG_1174.HEIC.jpg
C:\Heic_Converter\Input\IMG_1175.HEIC
 -> IMG_1175.HEIC.jpg
C:\Heic_Converter\Input\IMG_1176.HEIC
 -> IMG_1176.HEIC.jpg
C:\Heic_Converter\Input\IMG_1177.HEIC
 -> IMG_1177.HEIC.jpg
C:\Heic_Converter\Input\IMG_1178.HEIC
 -> IMG_1178.HEIC.jpg
C:\Heic_Converter\Input\IMG_1179.HEIC
 -> IMG_1179.HEIC.jpg
C:\Heic_Converter\Input\IMG_1180.HEIC
 -> IMG_1180.HEIC.jpg
C:\Heic_Converter\Input\IMG_1181.HEIC
 -> IMG_1181.HEIC.jpg
C:\Heic_Converter\Input\IMG_1182.HEIC
 -> IMG_1182.HEIC.jpg
C:\Heic_Converter\Input\IMG_1183.HEIC
 -> IMG_1183.HEIC.jpg
C:\Heic_Converter\Input\IMG_1184.HEIC
 -> IMG_1184.HEIC.jpg
C:\Heic_Converter\Input\IMG_1185.HEIC
 -> IMG_1185.HEIC.jpg
C:\Heic_Converter\Input\IMG_1186.HEIC
 -> IMG_1186.HEIC.jpg
C:\Heic_Converter\Input\IMG_1187.HEIC
 -> IMG_1187.HEIC.jpg
C:\Heic_Converter\Input\IMG_1188.HEIC
 -> IMG_1188.HEIC.jpg
C:\Heic_Converter\Input\IMG_1189.HEIC
 -> IMG_1189.HEIC.jpg
C:\Heic_Converter\Input\IMG_1215.HEIC
 -> IMG_1215.HEIC.jpg
C:\Heic_Converter\Input\IMG_1216.HEIC
 -> IMG_1216.HEIC.jpg
C:\Heic_Converter\Input\IMG_1217.HEIC
 -> IMG_1217.HEIC.jpg
C:\Heic_Converter\Input\IMG_1218.HEIC
 -> IMG_1218.HEIC.jpg
C:\Heic_Converter\Input\IMG_1219.HEIC
 -> IMG_1219.HEIC.jpg
C:\Heic_Converter\Input\IMG_1220.HEIC
 -> IMG_1220.HEIC.jpg
C:\Heic_Converter\Input\IMG_1221.HEIC
 -> IMG_1221.HEIC.jpg
C:\Heic_Converter\Input\IMG_1222.HEIC
 -> IMG_1222.HEIC.jpg
C:\Heic_Converter\Input\IMG_1223.HEIC
 -> IMG_1223.HEIC.jpg
C:\Heic_Converter\Input\IMG_1224.HEIC
 -> IMG_1224.HEIC.jpg
C:\Heic_Converter\Input\IMG_1225.HEIC
 -> IMG_1225.HEIC.jpg
C:\Heic_Converter\Input\IMG_1226.HEIC
 -> IMG_1226.HEIC.jpg
C:\Heic_Converter\Input\IMG_1227.HEIC
 -> IMG_1227.HEIC.jpg
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1085.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1085.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1086.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1086.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1087.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1087.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1088.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1088.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1089.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1089.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1090.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1090.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1091.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1091.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1092.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1092.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1093.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1093.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1094.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1094.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1095.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1095.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1096.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1096.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1097.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1097.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1098.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1098.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1099.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1099.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1100.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1100.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1101.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1101.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1102.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1102.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1103.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1103.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1104.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1104.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1105.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1105.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1106.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1106.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1107.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1107.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1108.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1108.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1109.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1109.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1110.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1110.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1111.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1111.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1112.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1112.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1113.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1113.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1114.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1114.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1115.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1115.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1116.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1116.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1117.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1117.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1118.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1118.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1119.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1119.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1120.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1120.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1121.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1121.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1122.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1122.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1123.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1123.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1124.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1124.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1125.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1125.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1126.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1126.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1127.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1127.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1128.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1128.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1129.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1129.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1130.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1130.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1131.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1131.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1132.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1132.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1133.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1133.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1134.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1134.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1135.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1135.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1136.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1136.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1137.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1137.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1138.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1138.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1139.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1139.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1140.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1140.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1141.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1141.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1142.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1142.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1143.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1143.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1144.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1144.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1145.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1145.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1146.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1146.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1147.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1147.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1148.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1148.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1149.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1149.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1150.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1150.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1151.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1151.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1152.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1152.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1153.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1153.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1154.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1154.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1155.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1155.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1156.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1156.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1157.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1157.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1158.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1158.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1159.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1159.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1160.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1160.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1161.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1161.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1162.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1162.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1163.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1163.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1164.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1164.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1165.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1165.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1166.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1166.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1167.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1167.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1168.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1168.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1169.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1169.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1170.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1170.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1171.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1171.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1172.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1172.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1173.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1173.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1174.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1174.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1175.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1175.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1176.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1176.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1177.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1177.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1178.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1178.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1179.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1179.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1180.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1180.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1181.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1181.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1182.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1182.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1183.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1183.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1184.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1184.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1185.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1185.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1186.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1186.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1187.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1187.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1188.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1188.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1189.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1189.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1215.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1215.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1216.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1216.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1217.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1217.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1218.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1218.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1219.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1219.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1220.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1220.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1221.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1221.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1222.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1222.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1223.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1223.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1224.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1224.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1225.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1225.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1226.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1226.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1227.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:6 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1227.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

16-02-2022 15:55:59 Convertion completed
16-02-2022 15:56:06 CleenUp completed
**********************
Windows PowerShell transcript start
Start time: 20220216160117
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Heic_Converter\Scripts\Bulk.ps1
Process ID: 20416
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Heic_Converter\Scripts\log.txt
16-02-2022 16:01:19 Convertion completed
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> $global:?
True
**********************
Windows PowerShell transcript end
End time: 20220216160127
**********************
**********************
Windows PowerShell transcript start
Start time: 20220216160300
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Heic_Converter\Scripts\Bulk.ps1
Process ID: 30904
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Heic_Converter\Scripts\log.txt
C:\Heic_Converter\Input\IMG_1065.HEIC
 -> IMG_1065.HEIC.jpg
C:\Heic_Converter\Input\IMG_1066.HEIC
 -> IMG_1066.HEIC.jpg
C:\Heic_Converter\Input\IMG_1067.HEIC
 -> IMG_1067.HEIC.jpg
C:\Heic_Converter\Input\IMG_1068.HEIC
 -> IMG_1068.HEIC.jpg
C:\Heic_Converter\Input\IMG_1069.HEIC
 -> IMG_1069.HEIC.jpg
C:\Heic_Converter\Input\IMG_1070.HEIC
 -> IMG_1070.HEIC.jpg
16-02-2022 16:03:06 Convertion completed
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> $global:?
True
**********************
Windows PowerShell transcript end
End time: 20220216160331
**********************
**********************
Windows PowerShell transcript start
Start time: 20220216160335
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Heic_Converter\Scripts\Bulk.ps1
Process ID: 1452
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Heic_Converter\Scripts\log.txt
C:\Heic_Converter\Input\IMG_1065.HEIC
 -> IMG_1065.HEIC.jpg
C:\Heic_Converter\Input\IMG_1066.HEIC
 -> IMG_1066.HEIC.jpg
C:\Heic_Converter\Input\IMG_1067.HEIC
 -> IMG_1067.HEIC.jpg
C:\Heic_Converter\Input\IMG_1068.HEIC
 -> IMG_1068.HEIC.jpg
C:\Heic_Converter\Input\IMG_1069.HEIC
 -> IMG_1069.HEIC.jpg
C:\Heic_Converter\Input\IMG_1070.HEIC
 -> IMG_1070.HEIC.jpg
16-02-2022 16:03:41 Convertion completed
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> $global:?
True
**********************
Windows PowerShell transcript end
End time: 20220216160428
**********************
**********************
Windows PowerShell transcript start
Start time: 20220216160610
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Heic_Converter\Scripts\Bulk.ps1
Process ID: 28332
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Heic_Converter\Scripts\log.txt
C:\Heic_Converter\Input\IMG_1065.HEIC
 -> IMG_1065.HEIC.jpg
C:\Heic_Converter\Input\IMG_1066.HEIC
 -> IMG_1066.HEIC.jpg
C:\Heic_Converter\Input\IMG_1067.HEIC
 -> IMG_1067.HEIC.jpg
C:\Heic_Converter\Input\IMG_1068.HEIC
 -> IMG_1068.HEIC.jpg
C:\Heic_Converter\Input\IMG_1069.HEIC
 -> IMG_1069.HEIC.jpg
C:\Heic_Converter\Input\IMG_1070.HEIC
 -> IMG_1070.HEIC.jpg
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1065.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1065.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1066.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1066.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1067.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1067.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1068.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1068.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1069.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1069.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1070.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1070.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

16-02-2022 16:06:16 Convertion completed
16-02-2022 16:06:20  Empty Input completed
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> $global:?
True
**********************
Windows PowerShell transcript start
Start time: 20220217075529
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Heic_Converter\Scripts\Bulk.ps1
Process ID: 22332
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Heic_Converter\Scripts\log.txt
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
catch : The term 'catch' is not recognized as the name of a cmdlet, function, script file, or operable program. Check th
e spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:1
+ catch [MoveFileInfoItemIOError, Microsoft.PowerShell.Commands.MoveIte ...
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (catch:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
catch : The term 'catch' is not recognized as the name of a cmdlet, function, script file, or operable program. Check t
he spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:1
+ catch [MoveFileInfoItemIOError, Microsoft.PowerShell.Commands.MoveIte ...
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (catch:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

17-02-2022 07:55:35 Convertion completed
**********************
Windows PowerShell transcript start
Start time: 20220217080145
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -NoProfile -NonInteractive -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2021.12.0' -AdditionalModules @('PowerShellEditorServices.VSCode') -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules' -EnableConsoleRepl -StartupBanner '=====> PowerShell Integrated Console v2021.12.0 <=====
' -LogLevel 'Normal' -LogPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\logs\1645080247-0e4a8308-0346-4380-8a9d-4a057803d8561645080244320\EditorServices.log' -SessionDetailsPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\sessions\PSES-VSCode-10100-407113' -FeatureFlags @() 
Process ID: 11724
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      11724
computerName                   DESKTOP-P2EDDUO
instanceId                     98b2e62d-2710-46ef-9581-705a127c236e


PS C:\Users\<USER>\Users\mpe> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      11724
computerName                   DESKTOP-P2EDDUO
instanceId                     98b2e62d-2710-46ef-9581-705a127c236e


PS C:\Users\<USER>\Users\mpe> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      11724
computerName                   DESKTOP-P2EDDUO
instanceId                     98b2e62d-2710-46ef-9581-705a127c236e


PS C:\Users\<USER>\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
PS C:\Users\<USER>\Users\mpe> $Get_Heic
PS C:\Users\<USER>\Users\mpe> $Get_Heic = (get-childitem $Input_Path -Filter *.HEIC)
PS C:\Users\<USER>\Users\mpe> $Get_Heic


    Directory: C:\Heic_Converter\Input


Mode                 LastWriteTime         Length Name
----                 -------------         ------ ----
-a----        16-02-2022     13:44        2228480 IMG_1076.HEIC
-a----        16-02-2022     13:44        2106902 IMG_1077.HEIC
-a----        16-02-2022     13:44        1638518 IMG_1078.HEIC
-a----        16-02-2022     13:44        2330122 IMG_1079.HEIC
-a----        16-02-2022     13:44        2543500 IMG_1081.HEIC
-a----        16-02-2022     13:44        3837711 IMG_1082.HEIC


PS C:\Users\<USER>\Users\mpe> $Get_Heic = (get-childitem $Input_Path -Filter *.HEIC)
$Get_Heic | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
PS C:\Users\<USER>\Users\mpe> $Get_Heic = (get-childitem $Input_Path -Filter *.HEIC)
PS C:\Users\<USER>\Users\mpe> $Conv
PS C:\Users\<USER>\Users\mpe> get-childitem $Input_Path -Filter *.ico | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
C:\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
PS C:\Users\<USER>\Users\mpe> $date = Get-Date -Format "dd/MM/yyyy HH:mm:ss"
PS C:\Users\<USER>\Users\mpe> $date
17-02-2022 09:20:08
PS C:\Users\<USER>\Users\mpe> $StartDate = Get-Date -Format "dd/MM/yyyy HH:mm:ss"
    Write-Host "Conversation Started at $date"
    get-childitem $Input_Path -Filter *.HEIC | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    $EndDate = Get-Date -Format "dd/MM/yyyy HH:mm:ss"
    Write-Host "Conversation completed at $date"
    NEW-TIMESPAN ?Start $StartDate ?End $EndDate -Format "dd/MM/yyyy HH:mm:ss"
Conversation Started at 17-02-2022 09:20:08
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
Conversation completed at 17-02-2022 09:20:08
PS C:\Users\<USER>\Users\mpe> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      11724
computerName                   DESKTOP-P2EDDUO
instanceId                     98b2e62d-2710-46ef-9581-705a127c236e


PS C:\Users\<USER>\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    $EndDate = Get-Date -Format "dd/MM/yyyy HH:mm:ss"
    Write-Host "Conversation completed at $date"
    NEW-TIMESPAN ?Start $StartDate ?End $EndDate
}
At line:8 char:1
+ }
+ ~
Unexpected token '}' in expression or statement.
>> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      11724
computerName                   DESKTOP-P2EDDUO
instanceId                     98b2e62d-2710-46ef-9581-705a127c236e


At line:8 char:1
+ }
+ ~
Unexpected token '}' in expression or statement.
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId : UnexpectedToken

PS C:\Users\<USER>\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    $EndDate = Get-Date -Format "dd/MM/yyyy HH:mm:ss"
    Write-Host "Conversation completed at $date"
    NEW-TIMESPAN ?Start $StartDate ?End $EndDate
Conversation Started at 17-02-2022 09:20:08
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
Conversation completed at 17-02-2022 09:20:08


Days              : 0
Hours             : 0
Minutes           : 0
Seconds           : 5
Milliseconds      : 0
Ticks             : 50000000
TotalDays         : 5,78703703703704E-05
TotalHours        : 0,00138888888888889
TotalMinutes      : 0,0833333333333333
TotalSeconds      : 5
TotalMilliseconds : 5000



PS C:\Users\<USER>\Users\mpe> $StartDate = Get-Date -Format "dd/MM/yyyy HH:mm:ss"
    Write-Host "Conversation Started at $date"
    get-childitem $Input_Path -Filter *.HEIC | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    $EndDate = Get-Date -Format "dd/MM/yyyy HH:mm:ss"
    Write-Host "Conversation completed at $date"
    $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString("dd\.hh\:mm\:ss")
Conversation Started at 17-02-2022 09:20:08
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1076.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1076.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1077.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1077.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1078.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1078.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1079.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1079.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1081.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1081.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1082.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1082.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Conversation completed at 17-02-2022 09:20:08
PS C:\Users\<USER>\Users\mpe> $time
00.00:00:04
PS C:\Users\<USER>\Users\mpe> Write-Host "Conversation Started at $date"
    get-childitem $Input_Path -Filter *.HEIC | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    Write-Host "Conversation completed at $date"
    $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString("'hh'hr:'mm'min:'ss'sec'")
Conversation Started at 17-02-2022 09:20:08
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
Conversation completed at 17-02-2022 09:20:08
Exception calling "ToString" with "1" argument(s): "Input string was not in a correct format."
At line:5 char:5
+     $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString(" ...
+     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (:) [], MethodInvocationException
    + FullyQualifiedErrorId : FormatException
Exception calling "ToString" with "1" argument(s): "Input string was not in a correct format."
At line:5 char:5
+     $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString(" ...
+     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (:) [], MethodInvocationException
    + FullyQualifiedErrorId : FormatException

PS C:\Users\<USER>\Users\mpe> $time
00.00:00:04
PS C:\Users\<USER>\Users\mpe> $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString("dd' days 'hh' hours 'mm' minutes 'ss' seconds'")
PS C:\Users\<USER>\Users\mpe> $time
00 days 00 hours 00 minutes 04 seconds
PS C:\Users\<USER>\Users\mpe> get-childitem $Input_Path -Filter *.HEIC | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    Write-Host "Conversation completed at $date"
    $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString("hh' hours 'mm' minutes 'ss' seconds'")
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
Conversation completed at 17-02-2022 09:20:08
PS C:\Users\<USER>\Users\mpe> $StartDate = time
    get-childitem $Input_Path -Filter *.HEIC | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    $EndDate = time
    $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString("hh' hours 'mm' minutes 'ss' seconds'")
    Write-Host "
    Conversation completed
    Duration time $time
    "
time : The term 'time' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the 
spelling of the name, or if a path was included, verify that the path is correct and try again.
At line:1 char:14
+ $StartDate = time
+              ~~~~
    + CategoryInfo          : ObjectNotFound: (time:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
time : The term 'time' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
At line:1 char:14
+ $StartDate = time
+              ~~~~
    + CategoryInfo          : ObjectNotFound: (time:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
time : The term 'time' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the 
spelling of the name, or if a path was included, verify that the path is correct and try again.
At line:4 char:16
+     $EndDate = time
+                ~~~~
    + CategoryInfo          : ObjectNotFound: (time:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
time : The term 'time' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
At line:4 char:16
+     $EndDate = time
+                ~~~~
    + CategoryInfo          : ObjectNotFound: (time:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException


    Conversation completed
    Duration time 00 hours 00 minutes 04 seconds
PS C:\Users\<USER>\Users\mpe> $StartDate = Get-Date
    get-childitem $Input_Path -Filter *.HEIC | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    $EndDate = Get-Date
    $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString("hh' hours 'mm' minutes 'ss' seconds'")
    Write-Host "
    Conversation completed
    Duration time $time
    "
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg

    Conversation completed
    Duration time 00 hours 00 minutes 04 seconds
PS C:\Users\<USER>\Users\mpe> $StartDate = Get-Date
    get-childitem $Input_Path -Filter *.HEIC | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    $EndDate = Get-Date
    $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString("hh' hours 'mm' minutes 'ss' seconds'")
    Write-Host "
    Conversation completed
    Duration time $time
The string is missing the terminator: ".
>> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      11724
computerName                   DESKTOP-P2EDDUO
instanceId                     98b2e62d-2710-46ef-9581-705a127c236e


The string is missing the terminator: ".
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId : TerminatorExpectedAtEndOfString

PS C:\Users\<USER>\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
    $EndDate = Get-Date
    $time = (NEW-TIMESPAN ?Start $StartDate ?End $EndDate).ToString("hh' hours 'mm' minutes 'ss' seconds'")
    Write-Host "
    Conversation completed
    Duration time $time
    "
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg

    Conversation completed
    Duration time 00 hours 00 minutes 12 seconds
PS C:\Users\<USER>\Users\mpe> TerminatingError(Get-ChildItem): "Missing an argument for parameter 'Filter'. Specify a parameter of type 'System.String' and try again."
Get-ChildItem : Missing an argument for parameter 'Filter'. Specify a parameter of type 'System.String' and try again.
At C:\Heic_Converter\Scripts\Bulk.ps1:9 char:31
+     get-childitem $Input_Path -Filter | C:\Heic_Converter\Scripts\Con ...
+                               ~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Get-ChildItem], ParameterBindingException
    + FullyQualifiedErrorId : MissingArgument,Microsoft.PowerShell.Commands.GetChildItemCommand
Get-ChildItem : Missing an argument for parameter 'Filter'. Specify a parameter of type 'System.String' and try again.
At C:\Heic_Converter\Scripts\Bulk.ps1:9 char:31
+     get-childitem $Input_Path -Filter | C:\Heic_Converter\Scripts\Con ...
+                               ~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Get-ChildItem], ParameterBindingException
    + FullyQualifiedErrorId : MissingArgument,Microsoft.PowerShell.Commands.GetChildItemCommand

PS C:\Users\<USER>\Heic_Converter\Scripts\Bulk.ps1:12 char:14
+     $time = (NEW-TIMESPAN ???Start $StartDate ???End $EndDate).ToStri ...
+              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [New-TimeSpan], ParameterBindingException
    + FullyQualifiedErrorId : PositionalParameterNotFound,Microsoft.PowerShell.Commands.NewTimeSpanCommand
New-TimeSpan : A positional parameter cannot be found that accepts argument '??Start 02/17/2022 09:57:47 ??End'.
At C:\Heic_Converter\Scripts\Bulk.ps1:12 char:14
+     $time = (NEW-TIMESPAN ???Start $StartDate ???End $EndDate).ToStri ...
+              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [New-TimeSpan], ParameterBindingException
    + FullyQualifiedErrorId : PositionalParameterNotFound,Microsoft.PowerShell.Commands.NewTimeSpanCommand


    Conversation completed
    Duration time:
PS C:\Users\<USER>\Users\mpe> CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Convert"
>> TerminatingError(): "The pipeline has been stopped."
>> CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Clear_InputFolder"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Function        Clear_InputFolder


PS C:\Users\<USER>\Users\mpe> CommandInvocation(Get-Help): "Get-Help"
>> ParameterBinding(Get-Help): name="Name"; value="Clear_InputFolder"
>> ParameterBinding(Get-Help): name="Online"; value="False"
>> ParameterBinding(Get-Help): name="ErrorAction"; value="Ignore"

NAME
    Clear_InputFolder

SYNTAX
    Clear_InputFolder


ALIASES
    None


REMARKS
    None



PS C:\Users\<USER>\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
    get-childitem $Input_Path | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    Get-ChildItem -Path "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
}
C:\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Converter\Input\badge-7.ico.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Converter\Input\badge-7.ico.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1076.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1076.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1077.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1077.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1078.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1078.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1079.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1079.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1081.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1081.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1082.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At line:4 char:56
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1082.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand



Days              : 0
Hours             : 0
Minutes           : 0
Seconds           : 14
Milliseconds      : 113
Ticks             : 141136872
TotalDays         : 0,000163352861111111
TotalHours        : 0,00392046866666667
TotalMinutes      : 0,23522812
TotalSeconds      : 14,1136872
TotalMilliseconds : 14113,6872



PS C:\Users\<USER>\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg


Days              : 0
Hours             : 0
Minutes           : 0
Seconds           : 13
Milliseconds      : 405
Ticks             : 134055609
TotalDays         : 0,000155156954861111
TotalHours        : 0,00372376691666667
TotalMinutes      : 0,223426015
TotalSeconds      : 13,4055609
TotalMilliseconds : 13405,5609


    Conversation completed
    Duration time: 00 hours 00 minutes 12 seconds
PS C:\Users\<USER>\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Converter\Input\badge-7.ico.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Converter\Input\badge-7.ico.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1076.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1076.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1077.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1077.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1078.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1078.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1079.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1079.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1081.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1081.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1082.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:10 char:52
+ ...  "$Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Output_Path"
+                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1082.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Duration: 00 min 14 sec

    Conversation completed
    Duration time: 00 hours 00 minutes 12 seconds
PS C:\Users\<USER>\Users\mpe> $ElapsedTime
>> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      11724
computerName                   DESKTOP-P2EDDUO
instanceId                     98b2e62d-2710-46ef-9581-705a127c236e


PS C:\Users\<USER>\Users\mpe> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      11724
computerName                   DESKTOP-P2EDDUO
instanceId                     98b2e62d-2710-46ef-9581-705a127c236e


PS C:\Users\<USER>\Users\mpe> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      11724
computerName                   DESKTOP-P2EDDUO
instanceId                     98b2e62d-2710-46ef-9581-705a127c236e


**********************
Windows PowerShell transcript start
Start time: 20220217103523
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -NoProfile -NonInteractive -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2021.12.0' -AdditionalModules @('PowerShellEditorServices.VSCode') -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules' -EnableConsoleRepl -StartupBanner '=====> PowerShell Integrated Console v2021.12.0 <=====
' -LogLevel 'Normal' -LogPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\logs\1645090487-cecd34fe-d643-4e32-b1fd-bd6e600495581645090477141\EditorServices.log' -SessionDetailsPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\sessions\PSES-VSCode-17148-764997' -FeatureFlags @() 
Process ID: 22696
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
C:\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg


    Conversation completed
    Duration time:
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      22696
computerName                   DESKTOP-P2EDDUO
instanceId                     68ff6d7a-20ab-47cb-a69b-2c7bdf437622


Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Converter\Input\badge-7.ico.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Converter\Input\badge-7.ico.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1076.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1076.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1077.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1077.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1078.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1078.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1079.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1079.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1081.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1081.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...py (2).HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...- Copy.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1082.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand
Move-Item : Cannot create a file when that file already exists.

At C:\Heic_Converter\Scripts\Bulk.ps1:7 char:52
+ ... $Input_Path\*.jpg" -Recurse | Move-Item -Destination "$Overflow_path"
+                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : WriteError: (C:\Heic_Convert...G_1082.HEIC.jpg:FileInfo) [Move-Item], IOException
    + FullyQualifiedErrorId : MoveFileInfoItemIOError,Microsoft.PowerShell.Commands.MoveItemCommand

C:\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\badge-7.ico.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC.jpg
 [Already JPEG]
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC.jpg
 [Already JPEG]


    Conversation completed
    Duration time:
PS C:\Users\<USER>\Users\mpe> CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1"
>> TerminatingError(): "The pipeline has been stopped."
>> CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
ExternalScript  ConvertTo-Jpeg.ps1                                            C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1


C:\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg


    Conversation completed
    Duration time:
PS C:\Users\<USER>\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg


    Conversation completed
    Duration time:
PS C:\Users\<USER>\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg


    Conversation completed
    Duration time:
PS C:\Users\<USER>\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg


    Conversation completed
    Duration time:
PS C:\Users\<USER>\Heic_Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Heic_Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Heic_Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg

PS C:\Users\<USER>\Users\mpe> CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="robocopy"
>> TerminatingError(): "The pipeline has been stopped."
>> CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="robocopy"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Application     Robocopy.exe                                       10.0.22... C:\WINDOWS\system32\Robocopy.exe


PS C:\Users\<USER>\WINDOWS\system32\Robocopy.exe


**********************
Windows PowerShell transcript start
Start time: 20220217105919
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -NoProfile -NonInteractive -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2021.12.0' -AdditionalModules @('PowerShellEditorServices.VSCode') -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules' -EnableConsoleRepl -StartupBanner '=====> PowerShell Integrated Console v2021.12.0 <=====
' -LogLevel 'Normal' -LogPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\logs\1645091472-acfd688e-f0f7-46d7-a1d6-c06d65c33c9c1645091458278\EditorServices.log' -SessionDetailsPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\sessions\PSES-VSCode-25560-698859' -FeatureFlags @() 
Process ID: 6836
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Caution already existing items in 'Output' will be overwritten
C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 : The term 'C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1' is not recognized
 as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was i
ncluded, verify that the path is correct and try again.
At C:\Converter\Scripts\Bulk.ps1:10 char:33
+ ... em $Input_Path | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExt ...
+                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (C:\Heic_Convert...vertTo-Jpeg.ps1:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 : The term 'C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the n
ame, or if a path was included, verify that the path is correct and try again.
At C:\Converter\Scripts\Bulk.ps1:10 char:33
+ ... em $Input_Path | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExt ...
+                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (C:\Heic_Convert...vertTo-Jpeg.ps1:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

       Simple Usage :: ROBOCOPY source destination /MIR

             source :: Source Directory (drive:\path or \\server\share\path).
        destination :: Destination Dir  (drive:\path or \\server\share\path).
               /MIR :: Mirror a complete directory tree.

    For more usage information run ROBOCOPY /?


****  /MIR can DELETE files as well as copy them !
jpg conversion completed
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      6836
computerName                   DESKTOP-P2EDDUO
instanceId                     a7270e98-41ec-48b3-be5c-5feb2c8d1878


**********************
Windows PowerShell transcript start
Start time: 20220217110021
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -NoProfile -NonInteractive -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2021.12.0' -AdditionalModules @('PowerShellEditorServices.VSCode') -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules' -EnableConsoleRepl -StartupBanner '=====> PowerShell Integrated Console v2021.12.0 <=====
' -LogLevel 'Normal' -LogPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\logs\1645092015-4ea03de0-f9dd-40c7-9d75-ce7f86ccd9a91645092013595\EditorServices.log' -SessionDetailsPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\sessions\PSES-VSCode-2772-281609' -FeatureFlags @() 
Process ID: 23624
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Caution already existing items in 'Output' will be overwritten
C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 : The term 'C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1' is not recognized
 as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was i
ncluded, verify that the path is correct and try again.
At C:\Converter\Scripts\Bulk.ps1:10 char:33
+ ... em $Input_Path | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExt ...
+                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (C:\Heic_Convert...vertTo-Jpeg.ps1:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 : The term 'C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of
 the name, or if a path was included, verify that the path is correct and try again.
At C:\Converter\Scripts\Bulk.ps1:10 char:33
+ ... em $Input_Path | C:\Heic_Converter\Scripts\ConvertTo-Jpeg.ps1 -FixExt ...
+                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (C:\Heic_Convert...vertTo-Jpeg.ps1:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException


jpg conversion completed
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      23624
computerName                   DESKTOP-P2EDDUO
instanceId                     84f846ed-8008-45f0-935d-ca4d1d9dcc56


**********************
Windows PowerShell transcript start
Start time: 20220217110131
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -NoProfile -NonInteractive -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2021.12.0' -AdditionalModules @('PowerShellEditorServices.VSCode') -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules' -EnableConsoleRepl -StartupBanner '=====> PowerShell Integrated Console v2021.12.0 <=====
' -LogLevel 'Normal' -LogPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\logs\1645092082-1286f2f6-cd27-4d58-8325-79efd4a914d51645092080679\EditorServices.log' -SessionDetailsPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\sessions\PSES-VSCode-2656-828270' -FeatureFlags @() 
Process ID: 26676
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Caution already existing items in 'Output' will be overwritten
C:\Converter\Input\badge-7.ico
 -> badge-7.ico.jpg
C:\Converter\Input\IMG_1076 - Copy (2).HEIC
 -> IMG_1076 - Copy (2).HEIC.jpg
C:\Converter\Input\IMG_1076 - Copy.HEIC
 -> IMG_1076 - Copy.HEIC.jpg
C:\Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Converter\Input\IMG_1077 - Copy (2).HEIC
 -> IMG_1077 - Copy (2).HEIC.jpg
C:\Converter\Input\IMG_1077 - Copy.HEIC
 -> IMG_1077 - Copy.HEIC.jpg
C:\Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Converter\Input\IMG_1078 - Copy (2).HEIC
 -> IMG_1078 - Copy (2).HEIC.jpg
C:\Converter\Input\IMG_1078 - Copy.HEIC
 -> IMG_1078 - Copy.HEIC.jpg
C:\Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Converter\Input\IMG_1079 - Copy (2).HEIC
 -> IMG_1079 - Copy (2).HEIC.jpg
C:\Converter\Input\IMG_1079 - Copy.HEIC
 -> IMG_1079 - Copy.HEIC.jpg
C:\Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Converter\Input\IMG_1081 - Copy (2).HEIC
 -> IMG_1081 - Copy (2).HEIC.jpg
C:\Converter\Input\IMG_1081 - Copy.HEIC
 -> IMG_1081 - Copy.HEIC.jpg
C:\Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Converter\Input\IMG_1082 - Copy (2).HEIC
 -> IMG_1082 - Copy (2).HEIC.jpg
C:\Converter\Input\IMG_1082 - Copy.HEIC
 -> IMG_1082 - Copy.HEIC.jpg
C:\Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg

jpg conversion completed
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> @{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
processId                      26676
computerName                   DESKTOP-P2EDDUO
instanceId                     b338d2ae-7912-4d3b-afd3-747ce7f11a9b



Convert To JPG

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP
Caution already existing items in 'Output' will be overwritten
PS C:\Users\<USER>\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 11880
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP
Caution already existing items in 'Output' will be overwritten

.JPG Conversion completed
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> $global:?
True
**********************
Windows PowerShell transcript end
End time: 20220217114401
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217114823
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 26140
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP
Eror 'Input'is empty
**********************
Windows PowerShell transcript end
End time: 20220217114833
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217114848
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 11444
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP
Eror 'Input'is empty
**********************
Windows PowerShell transcript end
End time: 20220217114859
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217114958
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 26324
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP
Error 'Input'is empty
**********************
Windows PowerShell transcript end
End time: 20220217115008
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217115037
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 24456
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP
Caution already existing items in 'Output' will be overwritten
C:\Converter\Input\New Microsoft Excel Worksheet.xlsx
PS>TerminatingError(): "Exception calling "Wait" with "0" argument(s): "One or more errors occurred.""
 [Unsupported]

.JPG Conversion completed
PS>$global:?
True
**********************
Windows PowerShell transcript end
End time: 20220217115124
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217115206
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 24116
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP
Caution already existing items in 'Output' will be overwritten
C:\Converter\Input\New Microsoft Excel Worksheet.xlsx
PS>TerminatingError(): "Exception calling "Wait" with "0" argument(s): "One or more errors occurred.""
 [Unsupported]

.JPG Conversion completed
  'Input' is now empty
PS>$global:?
True
**********************
Windows PowerShell transcript end
End time: 20220217115250
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217115726
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 20604
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Error 'Input'is empty
**********************
Windows PowerShell transcript end
End time: 20220217115736
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217123010
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 28884
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Caution already existing items in 'Output' will be overwritten
C:\Converter\Input\IMG_1065.HEIC
 -> IMG_1065.HEIC.jpg
C:\Converter\Input\IMG_1066.HEIC
 -> IMG_1066.HEIC.jpg
C:\Converter\Input\IMG_1067.HEIC
 -> IMG_1067.HEIC.jpg
C:\Converter\Input\IMG_1068.HEIC
 -> IMG_1068.HEIC.jpg
C:\Converter\Input\IMG_1069.HEIC
 -> IMG_1069.HEIC.jpg
C:\Converter\Input\IMG_1070.HEIC
 -> IMG_1070.HEIC.jpg
C:\Converter\Input\IMG_1071.HEIC
 -> IMG_1071.HEIC.jpg
C:\Converter\Input\IMG_1072.HEIC
 -> IMG_1072.HEIC.jpg
C:\Converter\Input\IMG_1073.HEIC
 -> IMG_1073.HEIC.jpg
C:\Converter\Input\IMG_1074.HEIC
 -> IMG_1074.HEIC.jpg
C:\Converter\Input\IMG_1075.HEIC
 -> IMG_1075.HEIC.jpg
C:\Converter\Input\IMG_1076.HEIC
 -> IMG_1076.HEIC.jpg
C:\Converter\Input\IMG_1077.HEIC
 -> IMG_1077.HEIC.jpg
C:\Converter\Input\IMG_1078.HEIC
 -> IMG_1078.HEIC.jpg
C:\Converter\Input\IMG_1079.HEIC
 -> IMG_1079.HEIC.jpg
C:\Converter\Input\IMG_1080.HEIC
 -> IMG_1080.HEIC.jpg
C:\Converter\Input\IMG_1081.HEIC
 -> IMG_1081.HEIC.jpg
C:\Converter\Input\IMG_1082.HEIC
 -> IMG_1082.HEIC.jpg
C:\Converter\Input\IMG_1083.HEIC
 -> IMG_1083.HEIC.jpg
C:\Converter\Input\IMG_1084.HEIC
 -> IMG_1084.HEIC.jpg
C:\Converter\Input\IMG_1085.HEIC
 -> IMG_1085.HEIC.jpg
C:\Converter\Input\IMG_1086.HEIC
 -> IMG_1086.HEIC.jpg
C:\Converter\Input\IMG_1087.HEIC
 -> IMG_1087.HEIC.jpg
C:\Converter\Input\IMG_1088.HEIC
 -> IMG_1088.HEIC.jpg
C:\Converter\Input\IMG_1089.HEIC
 -> IMG_1089.HEIC.jpg
C:\Converter\Input\IMG_1090.HEIC
 -> IMG_1090.HEIC.jpg
C:\Converter\Input\IMG_1091.HEIC
 -> IMG_1091.HEIC.jpg
C:\Converter\Input\IMG_1092.HEIC
 -> IMG_1092.HEIC.jpg
C:\Converter\Input\IMG_1093.HEIC
 -> IMG_1093.HEIC.jpg
C:\Converter\Input\IMG_1094.HEIC
 -> IMG_1094.HEIC.jpg
C:\Converter\Input\IMG_1095.HEIC
 -> IMG_1095.HEIC.jpg
C:\Converter\Input\IMG_1096.HEIC
 -> IMG_1096.HEIC.jpg
C:\Converter\Input\IMG_1097.HEIC
 -> IMG_1097.HEIC.jpg
C:\Converter\Input\IMG_1098.HEIC
 -> IMG_1098.HEIC.jpg
C:\Converter\Input\IMG_1099.HEIC
 -> IMG_1099.HEIC.jpg
C:\Converter\Input\IMG_1100.HEIC
 -> IMG_1100.HEIC.jpg
C:\Converter\Input\IMG_1101.HEIC
 -> IMG_1101.HEIC.jpg
C:\Converter\Input\IMG_1102.HEIC
 -> IMG_1102.HEIC.jpg
C:\Converter\Input\IMG_1103.HEIC
 -> IMG_1103.HEIC.jpg
C:\Converter\Input\IMG_1104.HEIC
 -> IMG_1104.HEIC.jpg
C:\Converter\Input\IMG_1105.HEIC
 -> IMG_1105.HEIC.jpg
C:\Converter\Input\IMG_1106.HEIC
 -> IMG_1106.HEIC.jpg
C:\Converter\Input\IMG_1107.HEIC
 -> IMG_1107.HEIC.jpg
C:\Converter\Input\IMG_1108.HEIC
 -> IMG_1108.HEIC.jpg
C:\Converter\Input\IMG_1109.HEIC
 -> IMG_1109.HEIC.jpg
C:\Converter\Input\IMG_1110.HEIC
 -> IMG_1110.HEIC.jpg
C:\Converter\Input\IMG_1111.HEIC
 -> IMG_1111.HEIC.jpg
C:\Converter\Input\IMG_1112.HEIC
 -> IMG_1112.HEIC.jpg
C:\Converter\Input\IMG_1113.HEIC
 -> IMG_1113.HEIC.jpg
C:\Converter\Input\IMG_1114.HEIC
 -> IMG_1114.HEIC.jpg
C:\Converter\Input\IMG_1115.HEIC
 -> IMG_1115.HEIC.jpg
C:\Converter\Input\IMG_1116.HEIC
 -> IMG_1116.HEIC.jpg
C:\Converter\Input\IMG_1117.HEIC
 -> IMG_1117.HEIC.jpg
C:\Converter\Input\IMG_1118.HEIC
 -> IMG_1118.HEIC.jpg
C:\Converter\Input\IMG_1119.HEIC
 -> IMG_1119.HEIC.jpg
C:\Converter\Input\IMG_1120.HEIC
 -> IMG_1120.HEIC.jpg
C:\Converter\Input\IMG_1121.HEIC
 -> IMG_1121.HEIC.jpg
C:\Converter\Input\IMG_1122.HEIC
 -> IMG_1122.HEIC.jpg
C:\Converter\Input\IMG_1123.HEIC
 -> IMG_1123.HEIC.jpg
C:\Converter\Input\IMG_1124.HEIC
 -> IMG_1124.HEIC.jpg
C:\Converter\Input\IMG_1125.HEIC
 -> IMG_1125.HEIC.jpg
C:\Converter\Input\IMG_1126.HEIC
 -> IMG_1126.HEIC.jpg
C:\Converter\Input\IMG_1127.HEIC
 -> IMG_1127.HEIC.jpg
C:\Converter\Input\IMG_1128.HEIC
 -> IMG_1128.HEIC.jpg
C:\Converter\Input\IMG_1129.HEIC
 -> IMG_1129.HEIC.jpg
C:\Converter\Input\IMG_1130.HEIC
 -> IMG_1130.HEIC.jpg
C:\Converter\Input\IMG_1131.HEIC
 -> IMG_1131.HEIC.jpg
C:\Converter\Input\IMG_1132.HEIC
 -> IMG_1132.HEIC.jpg
C:\Converter\Input\IMG_1133.HEIC
 -> IMG_1133.HEIC.jpg
C:\Converter\Input\IMG_1134.HEIC
 -> IMG_1134.HEIC.jpg
C:\Converter\Input\IMG_1135.HEIC
 -> IMG_1135.HEIC.jpg
C:\Converter\Input\IMG_1136.HEIC
 -> IMG_1136.HEIC.jpg
C:\Converter\Input\IMG_1137.HEIC
 -> IMG_1137.HEIC.jpg
C:\Converter\Input\IMG_1138.HEIC
 -> IMG_1138.HEIC.jpg
C:\Converter\Input\IMG_1139.HEIC
 -> IMG_1139.HEIC.jpg
C:\Converter\Input\IMG_1140.HEIC
 -> IMG_1140.HEIC.jpg
C:\Converter\Input\IMG_1141.HEIC
 -> IMG_1141.HEIC.jpg
C:\Converter\Input\IMG_1142.HEIC
 -> IMG_1142.HEIC.jpg
C:\Converter\Input\IMG_1143.HEIC
 -> IMG_1143.HEIC.jpg
C:\Converter\Input\IMG_1144.HEIC
 -> IMG_1144.HEIC.jpg
C:\Converter\Input\IMG_1145.HEIC
 -> IMG_1145.HEIC.jpg
C:\Converter\Input\IMG_1146.HEIC
 -> IMG_1146.HEIC.jpg
C:\Converter\Input\IMG_1147.HEIC
 -> IMG_1147.HEIC.jpg
C:\Converter\Input\IMG_1148.HEIC
 -> IMG_1148.HEIC.jpg
C:\Converter\Input\IMG_1149.HEIC
 -> IMG_1149.HEIC.jpg
C:\Converter\Input\IMG_1150.HEIC
 -> IMG_1150.HEIC.jpg
C:\Converter\Input\IMG_1151.HEIC
 -> IMG_1151.HEIC.jpg
C:\Converter\Input\IMG_1152.HEIC
 -> IMG_1152.HEIC.jpg
C:\Converter\Input\IMG_1153.HEIC
 -> IMG_1153.HEIC.jpg
C:\Converter\Input\IMG_1154.HEIC
 -> IMG_1154.HEIC.jpg
C:\Converter\Input\IMG_1155.HEIC
 -> IMG_1155.HEIC.jpg
C:\Converter\Input\IMG_1156.HEIC
 -> IMG_1156.HEIC.jpg
C:\Converter\Input\IMG_1157.HEIC
 -> IMG_1157.HEIC.jpg
C:\Converter\Input\IMG_1158.HEIC
 -> IMG_1158.HEIC.jpg
C:\Converter\Input\IMG_1159.HEIC
 -> IMG_1159.HEIC.jpg
C:\Converter\Input\IMG_1160.HEIC
 -> IMG_1160.HEIC.jpg
C:\Converter\Input\IMG_1161.HEIC
 -> IMG_1161.HEIC.jpg
C:\Converter\Input\IMG_1162.HEIC
 -> IMG_1162.HEIC.jpg
C:\Converter\Input\IMG_1163.HEIC
 -> IMG_1163.HEIC.jpg
C:\Converter\Input\IMG_1164.HEIC
 -> IMG_1164.HEIC.jpg
C:\Converter\Input\IMG_1165.HEIC
 -> IMG_1165.HEIC.jpg
C:\Converter\Input\IMG_1166.HEIC
 -> IMG_1166.HEIC.jpg
C:\Converter\Input\IMG_1167.HEIC
 -> IMG_1167.HEIC.jpg
C:\Converter\Input\IMG_1168.HEIC
 -> IMG_1168.HEIC.jpg
C:\Converter\Input\IMG_1169.HEIC
 -> IMG_1169.HEIC.jpg
C:\Converter\Input\IMG_1170.HEIC
 -> IMG_1170.HEIC.jpg
C:\Converter\Input\IMG_1171.HEIC
 -> IMG_1171.HEIC.jpg
C:\Converter\Input\IMG_1172.HEIC
 -> IMG_1172.HEIC.jpg
C:\Converter\Input\IMG_1173.HEIC
 -> IMG_1173.HEIC.jpg
C:\Converter\Input\IMG_1174.HEIC
 -> IMG_1174.HEIC.jpg
C:\Converter\Input\IMG_1175.HEIC
 -> IMG_1175.HEIC.jpg
C:\Converter\Input\IMG_1176.HEIC
 -> IMG_1176.HEIC.jpg
C:\Converter\Input\IMG_1177.HEIC
 -> IMG_1177.HEIC.jpg
C:\Converter\Input\IMG_1178.HEIC
 -> IMG_1178.HEIC.jpg
C:\Converter\Input\IMG_1179.HEIC
 -> IMG_1179.HEIC.jpg
C:\Converter\Input\IMG_1180.HEIC
 -> IMG_1180.HEIC.jpg
C:\Converter\Input\IMG_1181.HEIC
 -> IMG_1181.HEIC.jpg
C:\Converter\Input\IMG_1182.HEIC
 -> IMG_1182.HEIC.jpg
C:\Converter\Input\IMG_1183.HEIC
 -> IMG_1183.HEIC.jpg
C:\Converter\Input\IMG_1184.HEIC
 -> IMG_1184.HEIC.jpg
C:\Converter\Input\IMG_1185.HEIC
 -> IMG_1185.HEIC.jpg
C:\Converter\Input\IMG_1186.HEIC
 -> IMG_1186.HEIC.jpg
C:\Converter\Input\IMG_1187.HEIC
 -> IMG_1187.HEIC.jpg
C:\Converter\Input\IMG_1188.HEIC
 -> IMG_1188.HEIC.jpg
C:\Converter\Input\IMG_1189.HEIC
 -> IMG_1189.HEIC.jpg
C:\Converter\Input\IMG_1190.HEIC
 -> IMG_1190.HEIC.jpg
C:\Converter\Input\IMG_1191.HEIC
 -> IMG_1191.HEIC.jpg
C:\Converter\Input\IMG_1192.HEIC
 -> IMG_1192.HEIC.jpg
C:\Converter\Input\IMG_1193.HEIC
 -> IMG_1193.HEIC.jpg
C:\Converter\Input\IMG_1194.HEIC
 -> IMG_1194.HEIC.jpg
C:\Converter\Input\IMG_1195.HEIC
 -> IMG_1195.HEIC.jpg
C:\Converter\Input\IMG_1196.HEIC
 -> IMG_1196.HEIC.jpg
C:\Converter\Input\IMG_1197.HEIC
 -> IMG_1197.HEIC.jpg
C:\Converter\Input\IMG_1198.HEIC
 -> IMG_1198.HEIC.jpg
C:\Converter\Input\IMG_1199.HEIC
 -> IMG_1199.HEIC.jpg
C:\Converter\Input\IMG_1200.HEIC
 -> IMG_1200.HEIC.jpg
C:\Converter\Input\IMG_1201.HEIC
 -> IMG_1201.HEIC.jpg
C:\Converter\Input\IMG_1202.HEIC
 -> IMG_1202.HEIC.jpg
C:\Converter\Input\IMG_1203.HEIC
 -> IMG_1203.HEIC.jpg
C:\Converter\Input\IMG_1204.HEIC
 -> IMG_1204.HEIC.jpg
C:\Converter\Input\IMG_1205.HEIC
 -> IMG_1205.HEIC.jpg
C:\Converter\Input\IMG_1206.HEIC
 -> IMG_1206.HEIC.jpg
C:\Converter\Input\IMG_1207.HEIC
 -> IMG_1207.HEIC.jpg
C:\Converter\Input\IMG_1208.HEIC
 -> IMG_1208.HEIC.jpg
C:\Converter\Input\IMG_1209.HEIC
 -> IMG_1209.HEIC.jpg
C:\Converter\Input\IMG_1210.HEIC
 -> IMG_1210.HEIC.jpg
C:\Converter\Input\IMG_1211.HEIC
 -> IMG_1211.HEIC.jpg
C:\Converter\Input\IMG_1212.HEIC
 -> IMG_1212.HEIC.jpg
C:\Converter\Input\IMG_1213.HEIC
 -> IMG_1213.HEIC.jpg
C:\Converter\Input\IMG_1214.HEIC
 -> IMG_1214.HEIC.jpg
C:\Converter\Input\IMG_1215.HEIC
 -> IMG_1215.HEIC.jpg
C:\Converter\Input\IMG_1216.HEIC
 -> IMG_1216.HEIC.jpg
C:\Converter\Input\IMG_1217.HEIC
 -> IMG_1217.HEIC.jpg
C:\Converter\Input\IMG_1218.HEIC
 -> IMG_1218.HEIC.jpg
C:\Converter\Input\IMG_1219.HEIC
 -> IMG_1219.HEIC.jpg
C:\Converter\Input\IMG_1220.HEIC
 -> IMG_1220.HEIC.jpg
C:\Converter\Input\IMG_1221.HEIC
 -> IMG_1221.HEIC.jpg
C:\Converter\Input\IMG_1222.HEIC
 -> IMG_1222.HEIC.jpg
C:\Converter\Input\IMG_1223.HEIC
 -> IMG_1223.HEIC.jpg
C:\Converter\Input\IMG_1224.HEIC
 -> IMG_1224.HEIC.jpg
C:\Converter\Input\IMG_1225.HEIC
 -> IMG_1225.HEIC.jpg
C:\Converter\Input\IMG_1226.HEIC
 -> IMG_1226.HEIC.jpg
C:\Converter\Input\IMG_1227.HEIC
 -> IMG_1227.HEIC.jpg
C:\Converter\Input\IMG_1228.HEIC
 -> IMG_1228.HEIC.jpg
C:\Converter\Input\IMG_1229.HEIC
 -> IMG_1229.HEIC.jpg
C:\Converter\Input\IMG_1230.HEIC
 -> IMG_1230.HEIC.jpg
C:\Converter\Input\IMG_1231.HEIC
 -> IMG_1231.HEIC.jpg
C:\Converter\Input\IMG_1232.HEIC
 -> IMG_1232.HEIC.jpg
C:\Converter\Input\IMG_1233.HEIC
 -> IMG_1233.HEIC.jpg
C:\Converter\Input\IMG_1234.HEIC
 -> IMG_1234.HEIC.jpg
C:\Converter\Input\IMG_1235.HEIC
 -> IMG_1235.HEIC.jpg
C:\Converter\Input\IMG_1236.HEIC
 -> IMG_1236.HEIC.jpg
C:\Converter\Input\IMG_1237.HEIC
 -> IMG_1237.HEIC.jpg
C:\Converter\Input\IMG_1238.HEIC
 -> IMG_1238.HEIC.jpg
C:\Converter\Input\IMG_1239.HEIC
 -> IMG_1239.HEIC.jpg
C:\Converter\Input\IMG_1240.HEIC
 -> IMG_1240.HEIC.jpg
C:\Converter\Input\IMG_1241.HEIC
 -> IMG_1241.HEIC.jpg
C:\Converter\Input\IMG_1242.HEIC
 -> IMG_1242.HEIC.jpg
C:\Converter\Input\IMG_1243.HEIC
 -> IMG_1243.HEIC.jpg
C:\Converter\Input\IMG_1244.HEIC
 -> IMG_1244.HEIC.jpg
C:\Converter\Input\IMG_1245.HEIC
 -> IMG_1245.HEIC.jpg
C:\Converter\Input\IMG_1246.HEIC
 -> IMG_1246.HEIC.jpg
C:\Converter\Input\IMG_1247.HEIC
 -> IMG_1247.HEIC.jpg
C:\Converter\Input\IMG_1248.HEIC
 -> IMG_1248.HEIC.jpg
C:\Converter\Input\IMG_1249.HEIC
 -> IMG_1249.HEIC.jpg
C:\Converter\Input\IMG_1250.HEIC
 -> IMG_1250.HEIC.jpg
C:\Converter\Input\IMG_1251.HEIC
 -> IMG_1251.HEIC.jpg
C:\Converter\Input\IMG_1252.HEIC
 -> IMG_1252.HEIC.jpg
C:\Converter\Input\IMG_1253.HEIC
 -> IMG_1253.HEIC.jpg
C:\Converter\Input\IMG_1254.HEIC
 -> IMG_1254.HEIC.jpg
C:\Converter\Input\IMG_1255.HEIC
 -> IMG_1255.HEIC.jpg
C:\Converter\Input\IMG_1256.HEIC
 -> IMG_1256.HEIC.jpg
C:\Converter\Input\IMG_1257.HEIC
 -> IMG_1257.HEIC.jpg
C:\Converter\Input\IMG_1258.HEIC
 -> IMG_1258.HEIC.jpg
C:\Converter\Input\IMG_1259.HEIC
 -> IMG_1259.HEIC.jpg
C:\Converter\Input\IMG_1260.HEIC
 -> IMG_1260.HEIC.jpg
C:\Converter\Input\IMG_1261.HEIC
 -> IMG_1261.HEIC.jpg
C:\Converter\Input\IMG_1262.HEIC
 -> IMG_1262.HEIC.jpg
C:\Converter\Input\IMG_1263.HEIC
 -> IMG_1263.HEIC.jpg
C:\Converter\Input\IMG_1264.HEIC
 -> IMG_1264.HEIC.jpg
C:\Converter\Input\IMG_1265.HEIC
 -> IMG_1265.HEIC.jpg
C:\Converter\Input\IMG_1266.HEIC
 -> IMG_1266.HEIC.jpg
C:\Converter\Input\IMG_1267.HEIC
 -> IMG_1267.HEIC.jpg
C:\Converter\Input\IMG_1268.HEIC
 -> IMG_1268.HEIC.jpg
C:\Converter\Input\IMG_1269.HEIC
 -> IMG_1269.HEIC.jpg
C:\Converter\Input\IMG_1270.HEIC
 -> IMG_1270.HEIC.jpg
C:\Converter\Input\IMG_1272.HEIC
 -> IMG_1272.HEIC.jpg
C:\Converter\Input\IMG_1273.HEIC
 -> IMG_1273.HEIC.jpg

.JPG Conversion completed
'Input' is now empty
PS>$global:?
True
**********************
Windows PowerShell transcript end
End time: 20220217123408
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217124412
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 27100
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Error 'Input'is empty
**********************
Windows PowerShell transcript end
End time: 20220217124422
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217124444
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 17216
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Error 'Input'is empty
**********************
Windows PowerShell transcript end
End time: 20220217124454
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217124635
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 13008
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Error 'Input'is empty
**********************
Windows PowerShell transcript end
End time: 20220217124645
**********************
**********************
Windows PowerShell transcript start
Start time: 20220217124917
Username: a-p\mpe
RunAs User: a-p\mpe
Configuration Name: 
Machine: DESKTOP-P2EDDUO (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Converter\Scripts\Bulk.ps1
Process ID: 5208
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Converter\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Error 'Input'is empty
**********************
Windows PowerShell transcript end
End time: 20220217124927
**********************
**********************
PowerShell transcript start
Start time: 20220222114336
Username: a-p\jdh
RunAs User: a-p\jdh
Configuration Name: 
Machine: DESKTOP-J78P59J (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -NonInteractive -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2021.12.0' -AdditionalModules @('PowerShellEditorServices.VSCode') -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules' -EnableConsoleRepl -StartupBanner '=====> PowerShell Integrated Console v2021.12.0 <=====
' -LogLevel 'Normal' -LogPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\logs\1645526131-0dc38a22-ef4c-4be3-bb63-b4e60064401f1645526129544\EditorServices.log' -SessionDetailsPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\sessions\PSES-VSCode-21752-649478' -FeatureFlags @() 
Process ID: 2472
PSVersion: 7.2.1
PSEdition: Core
GitCommitId: 7.2.1
OS: Microsoft Windows 10.0.22000
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.10032.0, 6.0.0, 6.1.0, 6.2.0, 7.0.0, 7.1.0, 7.2.1
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).

Get-ChildItem: C:\Scripts\ConverterImages\Scripts\Bulk.ps1:28:18
Line |
  28 |  $directoryInfo = Get-ChildItem C:\Converter\Input | Measure-Object
     |                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\Converter\Input' because it does not exist.

Get-ChildItem: C:\Scripts\ConverterImages\Scripts\Bulk.ps1:28:18
Line |
  28 |  $directoryInfo = Get-ChildItem C:\Converter\Input | Measure-Object
     |                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\Converter\Input' because it does not exist.

Error 'Input'is empty
PS>@{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
computerName                   DESKTOP-J78P59J
instanceId                     b219f29c-3c6c-4907-ade1-62900289e0a9
processId                      2472

Just Fire It Off ~/jdh>
**********************
PowerShell transcript start
Start time: 20220222114436
Username: a-p\jdh
RunAs User: a-p\jdh
Configuration Name: 
Machine: DESKTOP-J78P59J (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -NonInteractive -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2021.12.0' -AdditionalModules @('PowerShellEditorServices.VSCode') -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\modules' -EnableConsoleRepl -StartupBanner '=====> PowerShell Integrated Console v2021.12.0 <=====
' -LogLevel 'Normal' -LogPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\logs\1645526655-1bdccca3-fe12-41ae-a70a-5821eaa8fc1f1645526653774\EditorServices.log' -SessionDetailsPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2021.12.0\sessions\PSES-VSCode-23056-169212' -FeatureFlags @() 
Process ID: 22596
PSVersion: 7.2.1
PSEdition: Core
GitCommitId: 7.2.1
OS: Microsoft Windows 10.0.22000
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.10032.0, 6.0.0, 6.1.0, 6.2.0, 7.0.0, 7.1.0, 7.2.1
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).

Get-ChildItem: C:\Scripts\ConverterImages\Scripts\Bulk.ps1:28:18
Line |
  28 |  $directoryInfo = Get-ChildItem C:\Converter\Input | Measure-Object
     |                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\Converter\Input' because it does not exist.

Get-ChildItem: C:\Scripts\ConverterImages\Scripts\Bulk.ps1:28:18
Line |
  28 |  $directoryInfo = Get-ChildItem C:\Converter\Input | Measure-Object
     |                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\Converter\Input' because it does not exist.

Error 'Input'is empty
PS>@{ 'computerName' = if ([Environment]::MachineName) {[Environment]::MachineName}  else {'localhost'}; 'processId' = $PID; 'instanceId' = $host.InstanceId }

Name                           Value
----                           -----
computerName                   DESKTOP-J78P59J
processId                      22596
instanceId                     5d29d346-c019-450e-b09d-a47d63900bb4

Just Fire It Off ~/jdh>

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Caution already existing items in 'Output' will be overwritten

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:45
Line |
  27 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


InvalidOperation: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:5
Line |
  27 |      $asTaskGeneric = ($runtimeMethods | ? { $_.Name -eq 'AsTask' -and …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot index into a null array.

InvalidOperation: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:27:5
Line |
  27 |      $asTaskGeneric = ($runtimeMethods | ? { $_.Name -eq 'AsTask' -and …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot index into a null array.


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"

MethodInvocationException: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:38
Line |
  35 |  … thods | ? { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Exception calling "GetParameters" with "0" argument(s): "Operation is not supported on this platform. (0x80131539)"


InvalidOperation: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:5
Line |
  35 |      $asTask = ($runtimeMethods | ? { $_.Name -eq 'AsTask' -and $_.Get …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot index into a null array.

InvalidOperation: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:35:5
Line |
  35 |      $asTask = ($runtimeMethods | ? { $_.Name -eq 'AsTask' -and $_.Get …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot index into a null array.


InvalidOperation: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:43:5
Line |
  43 |      [Windows.Storage.StorageFile, Windows.Storage, ContentType=Window …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Unable to find type [Windows.Storage.StorageFile,Windows.Storage, ContentType=WindowsRuntime].

InvalidOperation: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:43:5
Line |
  43 |      [Windows.Storage.StorageFile, Windows.Storage, ContentType=Window …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Unable to find type [Windows.Storage.StorageFile,Windows.Storage, ContentType=WindowsRuntime].


InvalidOperation: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:44:5
Line |
  44 |      [Windows.Graphics.Imaging.BitmapDecoder, Windows.Graphics, Conten …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Unable to find type [Windows.Graphics.Imaging.BitmapDecoder,Windows.Graphics, ContentType=WindowsRuntime].

InvalidOperation: C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1:44:5
Line |
  44 |      [Windows.Graphics.Imaging.BitmapDecoder, Windows.Graphics, Conten …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Unable to find type [Windows.Graphics.Imaging.BitmapDecoder,Windows.Graphics, ContentType=WindowsRuntime].

C:\Scripts\ConverterImages\Input\IMG_1065.HEIC
 [Unsupported]
C:\Scripts\ConverterImages\Input\IMG_1069.HEIC
 [Unsupported]
C:\Scripts\ConverterImages\Input\IMG_1070.HEIC
 [Unsupported]

.JPG Conversion completed
**********************
Windows PowerShell transcript start
Start time: 20220222114810
Username: a-p\jdh
RunAs User: a-p\jdh
Configuration Name: 
Machine: DESKTOP-J78P59J (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -Command if((Get-ExecutionPolicy ) -ne 'AllSigned') { Set-ExecutionPolicy -Scope Process Bypass }; & 'C:\Scripts\ConverterImages\Scripts\Bulk.ps1'
Process ID: 19772
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Scripts\ConverterImages\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Caution already existing items in 'Output' will be overwritten
C:\Scripts\ConverterImages\Input\IMG_1065.HEIC
 -> IMG_1065.HEIC.jpg
C:\Scripts\ConverterImages\Input\IMG_1069.HEIC
 -> IMG_1069.HEIC.jpg
C:\Scripts\ConverterImages\Input\IMG_1070.HEIC
 -> IMG_1070.HEIC.jpg
****  /MIR can DELETE files as well as copy them !
.JPG Conversion completed
**********************
Windows PowerShell transcript start
Start time: 20220222115019
Username: a-p\jdh
RunAs User: a-p\jdh
Configuration Name: 
Machine: DESKTOP-J78P59J (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Scripts\ConverterImages\Scripts\Bulk.ps1
Process ID: 20372
PSVersion: 5.1.22000.282
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.282
BuildVersion: 10.0.22000.282
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Scripts\ConverterImages\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Caution already existing items in 'Output' will be overwritten
C:\Scripts\ConverterImages\Input\IMG_1065.HEIC
 -> IMG_1065.HEIC.jpg
C:\Scripts\ConverterImages\Input\IMG_1069.HEIC
 -> IMG_1069.HEIC.jpg
C:\Scripts\ConverterImages\Input\IMG_1070.HEIC
 -> IMG_1070.HEIC.jpg

.JPG Conversion completed
**********************
Windows PowerShell transcript start
Start time: 20220826133554
Username: a-p\jdh
RunAs User: a-p\jdh
Configuration Name: 
Machine: DESKTOP-UJVV72M (Microsoft Windows NT 10.0.22000.0)
Host Application: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -file C:\Scripts\ConverterImages\Scripts\Bulk.ps1
Process ID: 31520
PSVersion: 5.1.22000.832
PSEdition: Desktop
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1.22000.832
BuildVersion: 10.0.22000.832
CLRVersion: 4.0.30319.42000
WSManStackVersion: 3.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
**********************
Transcript started, output file is C:\Scripts\ConverterImages\Scripts\log.txt

Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
Error 'Input'is empty
