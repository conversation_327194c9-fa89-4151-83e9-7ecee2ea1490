## Audit File Downloads in SharePoint Online Using PowerShell
Audit file downloads in SharePoint Online and OneDrive using PowerShell to pinpoint who downloaded what file and when.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![SharePoint Online File Download Report](https://o365reports.com/wp-content/uploads/2024/02/Audit-file-downloads-in-SharePoint-Online-Sample-output.png?v=**********)

## Microsoft 365 Reporting Tool by AdminDroid

Take your Office 365 license management to the next level with the AdminDroid [Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1800+ pre-built reports and dashboards for in-depth analysis and efficient oversight.

*Check Who Downloaded Files from SharePoint Using AdminDroid: <https://demo.admindroid.com/#/1/11/reports/22085/1/20>*
