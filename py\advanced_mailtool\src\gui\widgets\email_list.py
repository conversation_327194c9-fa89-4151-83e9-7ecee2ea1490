"""
Email list widget with modern interface and proper dark mode support
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget,
                              QTreeWidgetItem, QLabel, QLineEdit, QPushButton,
                              QComboBox, QFrame, QHeaderView, QMessageBox)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont

from ...core.email_message import EmailMessage, EmailCollection


class EmailListWidget(QWidget):
    """Modern email list widget with search and filtering"""
    
    email_selected = Signal(object)  # EmailMessage
    selection_changed = Signal()
    
    def __init__(self):
        super().__init__()
        
        self.messages: List[EmailMessage] = []
        self.filtered_messages: List[EmailMessage] = []
        self.current_collection: Optional[EmailCollection] = None
        
        # Search timer for delayed search
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """Setup the user interface"""
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # Header
        header_frame = QFrame()
        header_frame.setProperty("class", "card")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(12, 12, 12, 12)
        header_layout.setSpacing(8)
        
        # Title
        title_label = QLabel("📧 Email Messages")
        title_label.setProperty("class", "header")
        header_layout.addWidget(title_label)
        
        # Modern search and filter row
        search_layout = QHBoxLayout()
        search_layout.setSpacing(12)

        # Modern search box with frame
        search_frame = QFrame()
        search_frame.setProperty("class", "search-frame")
        search_frame_layout = QHBoxLayout(search_frame)
        search_frame_layout.setContentsMargins(12, 8, 12, 8)
        search_frame_layout.setSpacing(8)

        # Search icon
        search_icon = QLabel("🔍")
        search_icon.setProperty("class", "search-icon")
        search_frame_layout.addWidget(search_icon)

        # Search input
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search messages...")
        self.search_box.setProperty("class", "search-input")
        self.search_box.setStyleSheet("border: none; background: transparent;")
        search_frame_layout.addWidget(self.search_box)

        search_layout.addWidget(search_frame)
        
        # Filter combo
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "All Messages",
            "With Attachments",
            "Today",
            "This Week",
            "This Month"
        ])
        self.filter_combo.setMinimumWidth(120)
        search_layout.addWidget(self.filter_combo)
        
        header_layout.addLayout(search_layout)
        
        # Message count
        self.count_label = QLabel("0 messages")
        self.count_label.setProperty("class", "muted")
        header_layout.addWidget(self.count_label)
        
        layout.addWidget(header_frame)
        
        # Email tree
        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(["Subject", "From", "Date", "Size"])
        self.tree.setAlternatingRowColors(True)
        self.tree.setRootIsDecorated(False)  # No expand/collapse icons
        self.tree.setSortingEnabled(True)
        self.tree.sortByColumn(2, Qt.SortOrder.DescendingOrder)  # Sort by date, newest first

        # Enable multi-selection
        self.tree.setSelectionMode(QTreeWidget.SelectionMode.ExtendedSelection)

        # Disable tree indentation to prevent weird icons
        self.tree.setIndentation(0)
        
        # Configure columns
        header = self.tree.header()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # Subject
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # From
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Date
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Size
        
        layout.addWidget(self.tree)
        
        # Status frame
        status_frame = QFrame()
        status_frame.setProperty("class", "card")
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(12, 8, 12, 8)
        
        # Main status info
        self.status_label = QLabel("📧 Ready - Click 'Open Files' to load emails")
        self.status_label.setProperty("class", "muted")
        status_layout.addWidget(self.status_label)

        # Spacer
        status_layout.addStretch()

        # Selection info (right side)
        self.selection_info = QLabel("")
        self.selection_info.setProperty("class", "info")
        self.selection_info.setVisible(False)
        status_layout.addWidget(self.selection_info)
        
        status_layout.addStretch()
        
        # Quick actions - fixed width export button
        self.export_btn = QPushButton("📤 Export")
        self.export_btn.setProperty("class", "secondary")
        self.export_btn.setEnabled(False)
        self.export_btn.setFixedWidth(100)  # Fixed width to prevent resizing
        self.export_btn.clicked.connect(self.export_selected)
        status_layout.addWidget(self.export_btn)
        
        layout.addWidget(status_frame)
    
    def setup_connections(self):
        """Setup signal connections"""
        
        # Tree selection
        self.tree.itemSelectionChanged.connect(self.on_selection_changed)
        self.tree.itemDoubleClicked.connect(self.on_item_double_clicked)
        
        # Search
        self.search_box.textChanged.connect(self.on_search_text_changed)
        
        # Filter
        self.filter_combo.currentTextChanged.connect(self.apply_filters)
    
    def set_messages(self, collection: EmailCollection):
        """Set messages to display"""
        
        self.current_collection = collection
        self.messages = list(collection.messages)
        self.filtered_messages = self.messages.copy()

        # Show source file statistics
        self.show_source_stats()

        self.populate_tree()
        self.update_status()

    def show_source_stats(self):
        """Show statistics about source files"""
        if not self.current_collection:
            return

        stats = self.current_collection.get_source_file_stats()
        if len(stats) > 1:
            # Multiple source files - show breakdown
            stats_text = "📁 Loaded Files:\n"
            for source, info in stats.items():
                file_name = source.split('\\')[-1] if '\\' in source else source.split('/')[-1]
                stats_text += f"  • {file_name}: {info['count']} emails ({info['file_type']})\n"

            print(stats_text)  # For now, print to console
    
    def populate_tree(self):
        """Populate tree with filtered messages"""
        
        self.tree.clear()
        
        for message in self.filtered_messages:
            item = self.create_tree_item(message)
            self.tree.addTopLevelItem(item)
        
        # Update count
        self.count_label.setText(f"{len(self.filtered_messages)} messages")
        
        # Auto-select first item if available
        if self.tree.topLevelItemCount() > 0:
            self.tree.setCurrentItem(self.tree.topLevelItem(0))
    
    def create_tree_item(self, message: EmailMessage) -> QTreeWidgetItem:
        """Create tree item for message"""
        
        # Format data
        subject = message.subject or "(No Subject)"
        sender = message.sender_name or message.sender or "(Unknown)"
        date_str = message.date_display
        size_str = f"{message.size_mb:.1f} MB" if message.size > 0 else ""
        
        # Add attachment indicator
        if message.has_attachments:
            subject = f"📎 {subject}"
        
        # Create item
        item = QTreeWidgetItem([subject, sender, date_str, size_str])
        
        # Store message reference
        item.setData(0, Qt.ItemDataRole.UserRole, message)
        
        # Set font weight for unread messages
        if not message.is_read:
            font = QFont()
            font.setBold(True)
            item.setFont(0, font)
            item.setFont(1, font)
        
        # Set tooltip
        tooltip = f"""Subject: {message.subject}
From: {message.sender_display}
Date: {message.date_display}
Attachments: {message.attachment_count}
Size: {message.size_mb:.1f} MB"""
        
        item.setToolTip(0, tooltip)
        
        return item
    
    def on_selection_changed(self):
        """Handle selection change"""

        selected_items = self.tree.selectedItems()

        # Always emit the first selected message for viewing
        if selected_items:
            # Get the first selected item for viewing
            first_item = selected_items[0]
            message = first_item.data(0, Qt.ItemDataRole.UserRole)
            if message:
                self.email_selected.emit(message)

                # Mark as read
                if not message.is_read:
                    message.is_read = True
                    self.update_item_appearance(first_item, message)

        # Update export button - simple enable/disable
        selection_count = len(selected_items)
        self.export_btn.setEnabled(selection_count > 0)

        # Update selection info
        if selection_count == 0:
            self.selection_info.setVisible(False)
        else:
            self.selection_info.setVisible(True)
            if selection_count == 1:
                self.selection_info.setText("1 selected")
            else:
                self.selection_info.setText(f"{selection_count} selected")

        self.selection_changed.emit()
    
    def on_item_double_clicked(self, item: QTreeWidgetItem, column: int):
        """Handle item double click"""
        
        message = item.data(0, Qt.ItemDataRole.UserRole)
        if message:
            # Could open in separate window or perform other action
            pass
    
    def update_item_appearance(self, item: QTreeWidgetItem, message: EmailMessage):
        """Update item appearance based on message state"""
        
        font = QFont()
        font.setBold(not message.is_read)
        
        for col in range(item.columnCount()):
            item.setFont(col, font)
    
    def on_search_text_changed(self, text: str):
        """Handle search text change with delay"""
        
        self.search_timer.stop()
        self.search_timer.start(300)  # 300ms delay
    
    def perform_search(self):
        """Perform the actual search"""
        
        search_text = self.search_box.text().strip().lower()
        
        if not search_text:
            self.filtered_messages = self.messages.copy()
        else:
            self.filtered_messages = []
            for message in self.messages:
                if (search_text in message.subject.lower() or
                    search_text in message.sender.lower() or
                    search_text in message.body_text.lower() or
                    search_text in message.body_html.lower()):
                    self.filtered_messages.append(message)
        
        self.apply_filters()
    
    def apply_filters(self):
        """Apply additional filters"""
        
        filter_type = self.filter_combo.currentText()
        
        if filter_type == "All Messages":
            # No additional filtering
            pass
        elif filter_type == "With Attachments":
            self.filtered_messages = [msg for msg in self.filtered_messages if msg.has_attachments]
        elif filter_type in ["Today", "This Week", "This Month"]:
            now = datetime.now()
            
            if filter_type == "Today":
                self.filtered_messages = [msg for msg in self.filtered_messages 
                                        if msg.date and msg.date.date() == now.date()]
            elif filter_type == "This Week":
                from datetime import timedelta
                week_start = now - timedelta(days=now.weekday())
                self.filtered_messages = [msg for msg in self.filtered_messages
                                        if msg.date and msg.date >= week_start]
            elif filter_type == "This Month":
                month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                self.filtered_messages = [msg for msg in self.filtered_messages 
                                        if msg.date and msg.date >= month_start]
        
        self.populate_tree()
        self.update_status()
    
    def get_selected_messages(self) -> List[EmailMessage]:
        """Get currently selected messages"""
        
        selected_items = self.tree.selectedItems()
        messages = []
        
        for item in selected_items:
            message = item.data(0, Qt.ItemDataRole.UserRole)
            if message:
                messages.append(message)
        
        return messages
    
    def update_status(self):
        """Update status display"""

        total = len(self.messages)
        filtered = len(self.filtered_messages)
        selected = len(self.tree.selectedItems())

        # Update main status label
        if total == 0:
            self.status_label.setText("📧 Ready - Click 'Open Files' to load emails")
        elif filtered == total:
            self.status_label.setText(f"📧 {total} messages loaded")
        else:
            self.status_label.setText(f"🔍 Showing {filtered} of {total} messages")

        # Update selection info
        if selected == 0:
            self.selection_info.setVisible(False)
        else:
            self.selection_info.setVisible(True)
            if selected == 1:
                self.selection_info.setText("1 selected")
            else:
                self.selection_info.setText(f"{selected} selected")
    
    def export_selected(self):
        """Export selected messages"""
        selected_messages = self.get_selected_messages()

        if not selected_messages:
            QMessageBox.information(self, "No Selection", "Please select one or more messages to export.")
            return

        # Show info about what will be exported
        count = len(selected_messages)
        subjects = [msg.subject[:50] + "..." if len(msg.subject) > 50 else msg.subject
                   for msg in selected_messages[:3]]

        if count > 3:
            preview = "\n".join(f"• {s}" for s in subjects) + f"\n• ... and {count-3} more"
        else:
            preview = "\n".join(f"• {s}" for s in subjects)

        # Open export dialog
        from ...exporters.export_dialog import ExportDialog

        try:
            dialog = ExportDialog(selected_messages, self)
            dialog.setWindowTitle(f"Export {count} Message{'s' if count != 1 else ''}")
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to open export dialog:\n{str(e)}")

    def clear(self):
        """Clear all messages"""

        self.messages.clear()
        self.filtered_messages.clear()
        self.current_collection = None
        self.tree.clear()
        self.status_label.setText("📧 Ready - Click 'Open Files' to load emails")
        self.export_btn.setEnabled(False)
        self.selection_info.setVisible(False)
