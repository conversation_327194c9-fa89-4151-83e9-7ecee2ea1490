﻿## Get MFA Status Report using MS Graph PowerShell
This script exports Microsoft 365 users and their MFA status using Microsoft Graph PowerShell. 

***Sample Output:***

This script exports an output CSV file that looks similar to the screenshot below.

![MFA Status report]( https://o365reports.com/wp-content/uploads/2022/04/Get-MFA-status-PowerShell-5.png)

## Microsoft 365 Reporting tool by AdminDroid
Take your Microsoft 365 data management to the next level with the [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub)! Get access to 1800+ pre-built reports and dashboards for in-depth analysis and efficient oversight.

*Unlock comprehensive MFA status reports with AdminDroid: [https://demo.admindroid.com/#/1/11/reports/82055/1/20](https://demo.admindroid.com/#/1/11/reports/82055/1/20)*



