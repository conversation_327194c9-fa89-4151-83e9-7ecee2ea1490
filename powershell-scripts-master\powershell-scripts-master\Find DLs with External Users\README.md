﻿## Get Distribution Lists with External Users in Microsoft 365  
Identify distribution lists with external users in Microsoft 365 to prevent sensitive information from being shared with unauthorized users.

***Sample Output:***

The script generates an output CSV file with details of distribution lists with external users in Microsoft 365. The sample output will resemble the screenshot below.

![DLs with external users in Microsoft 365](https://o365reports.com/wp-content/uploads/2024/12/DLs-with-external-users-in-Microsoft-365-768x224.png?v=1734340996)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive distribution lists based reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/1018/1/20](https://demo.admindroid.com/#/1/11/reports/1018/1/20)*









