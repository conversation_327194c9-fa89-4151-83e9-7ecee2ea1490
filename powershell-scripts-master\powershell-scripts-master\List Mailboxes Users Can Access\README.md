## Exchange Online: List All Mailboxes User Has Access Using PowerShell

The PowerShell script gets list of mailboxes a user has access to & export it to CSV. You can generate report for all users or specific users.

***Sample Output:***

This script gets list of mailboxes a user has access to & export it to CSV that looks like the screenshot below.

![All Mailboxes User Has Access List](https://m365scripts.com/wp-content/uploads/2022/04/mailboxes-user-can-access.png?v=1694788384)

## Microsoft 365 Reporting tool by AdminDroid

Ensure security by identifying all mailboxes a user can access with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), which includes over 1800+ pre-built reports and detailed dashboards.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/20388/1/20>*
