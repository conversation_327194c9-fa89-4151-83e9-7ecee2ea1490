﻿## Delete Phone Authentication for Microsoft 365 Users
Download this PowerShell script to remove phone-based MFA for users in Microsoft 365.

***Sample Output:***

The script outputs a detailed log, which looks like the screenshot provided below.

![Delete Phone Authentication for Microsoft 365 Users - Log file](https://blog.admindroid.com/wp-content/uploads/2024/10/csv-to-delete-phone-authentication-for-microsoft-365-users-1080x237.png)

## Microsoft 365 Reporting Tool by AdminDroid
Need a reporting boost? [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) features over 1800 pre-configured reports and dashboards, providing the perfect complement to your PowerShell automation tasks.

*Get more comprehensive MFA reports through AdminDroid: <https://demo.admindroid.com/#/1/11/reports/106/1/20>*

