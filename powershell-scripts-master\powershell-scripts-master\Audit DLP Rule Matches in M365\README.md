﻿## Audit DLP Policy Matches in Microsoft 365 Using PowerShell 
Execute the PowerShell script to exclusively audit DLP policy matches across workloads like Exhange, SharePoint, OneDrive, and Teams.

***Sample Output:***

The script generates an output CSV file with details of DLP policy matches. The sample output will resemble the screenshots below.

**DLP Detected Exchange Emails:** 

![DLP detected Exchange Email Messages](https://o365reports.com/wp-content/uploads/2024/11/DLP-detected-Exchange-Email-Messages.png)

**DLP Detected SharePoint Documents:** 

![DLP detected SPO documents](https://o365reports.com/wp-content/uploads/2024/11/DLP-detected-SPO-documents-1.png)

**DLP Detected OneDrive Files:** 

![DLP Detected OneDrive Files](https://o365reports.com/wp-content/uploads/2024/11/DLP-Detected-OneDrive-Files.png)

**DLP Detected Teams Messages:** 

![DLP Policy Detected MS Teams Messages](https://o365reports.com/wp-content/uploads/2024/11/DLP-Policy-Detected-MS-Teams-Messages.png)

**Overall DLP Rule Matches:**

![Microsoft 365 DLP Rule Matches](https://o365reports.com/wp-content/uploads/2024/11/DLP-Policy-Matches.png?v=1731407768)

## Microsoft 365 Reporting Tool by AdminDroid
Looking for more in-depth reporting? [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 out-of-the-box reports and dashboards. It’s the perfect complement to your PowerShell scripts. 

*View more comprehensive DLP based reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/24645/1/20](https://demo.admindroid.com/#/1/11/reports/24645/1/20)*





