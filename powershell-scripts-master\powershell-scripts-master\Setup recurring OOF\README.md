﻿## Set Up Recurring Out of Office Replies in Outlook
Set up recurring Out of Office automatic replies in Outlook for Microsoft 365 users using scheduling tools like Windows Task Scheduler or Azure Automation.

***Sample Output:***

Check out the screenshot below to see the script in action.

![ Configure recurring OOF replies - script execution]( https://m365scripts.com/wp-content/uploads/2024/10/Configure-recurring-OOF-replies-script-execution-1024x325.png)
## Microsoft 365 Reporting Tool by AdminDroid
Looking for a comprehensive analysis? [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) provides an extensive library of over 1,800 pre-built reports and dashboards, designed to enhance your PowerShell scripts.

*Explore a more detailed Out of Office email settings report using AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10782/1/20>*

