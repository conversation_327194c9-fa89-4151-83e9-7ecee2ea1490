## Audit File Deletion in SharePoint Online: Find Out Who Deleted Files from Office 365
This PowerShell script helps to audit file deletion in SharePoint and OneDrive for Business. With this report, admins can identify who deleted files in O365.

***Sample Output:***

The script exports an output CSV file that looks like the screenshot below.

![File Deletion Audit Report](https://o365reports.com/wp-content/uploads/2021/12/Audit-file-deletion-in-Office-365.png?v=1705576613)

## Microsoft 365 Reporting Tool by AdminDroid

For more extensive insights beyond this script, explore [AdminDroid's Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ out-of-the-box reports and insightful dashboards.

*Efficiently Track SharePoint Online File Deletions with AdminDroid Reports: <https://demo.admindroid.com/#/1/11/reports/22150/1/20>*

