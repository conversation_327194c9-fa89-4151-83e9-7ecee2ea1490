Start-Transcript -Path "C:\Scripts\ConverterImages\Scripts\log.txt" -Append
#Paths
$Input_Path = "C:\Scripts\ConverterImages\Input"
$Output_Path = "C:\Scripts\ConverterImages\Output"

Write-Host "
Convert To JPG 1.0

Supported file formats:
.BMP .DIB .RLE
.CUR
.DDS
.DNG
.GIF
.ICO .ICON
.EXIF .JFIF .JPE .JPEG .JPG
.ARW .CR2 .CRW .DNG .ERF .KDC .MRW .NEF .NRW .ORF .PEF .RAF .RAW .RW2 .RWL .SR2 .SRW
.AVCI .AVCS .HEIC .HEICS .HEIF .HEIFS
.WEBP
.PNG
.TIF .TIFF
.JXR .WDP

HEIC/HEIF images:
Install the Microsoft HEIF and HEVC Media Extensions bundle (or else both of HEVC Video Extensions and HEIF Image Extensions).
"

$directoryInfo = Get-ChildItem $Input_Path | Measure-Object
if ($directoryInfo.count -eq 0) {
    Write-Host "Error 'Input'is empty"-fore red
    Start-Sleep(10)
    exit
}
#Overwrite Caution
Write-Host "Caution already existing items in 'Output' will be overwritten" -fore red
$YN1 = Read-Host "Do you wish to continue y/n"

if ($YN1 -eq "y") {
    #Convert to JPG
    get-childitem $Input_Path | C:\Scripts\ConverterImages\Scripts\ConvertTo-Jpeg.ps1 -FixExtensionIfJpeg
    #Copy from $Input_Path to $Output_Path
    robocopy "$Input_Path" "$Output_Path" /MOVE /w:5 /r:2 /COPY:DATSOU /DCOPY:DAT /MT /NFL /NDL /NJH /NJS /nc /ns /np /if *.jpg
    Write-Host ".JPG Conversion completed"


    #Empty 'Input'
    $YN2 = Read-Host "Empty 'Input' folder y/n"
    if ($YN2 -eq "y") {
        get-childitem $Input_Path | Remove-Item
        Write-Host "'Input' is now empty" 
    }
    Start-Sleep(30)
}   
