## Export Office 365 Nested Distribution Group Members Using PowerShell

This PowerShell script exports Office 365 nested distribution group members to two CSV files, one with summary & another with detailed info.

***Sample Output:***

This script exports an output CSV file that looks similar to the screenshot below.

![Office 365 Nested Distribution Group Members](https://m365scripts.com/wp-content/uploads/2022/04/exprt-nested-distribution-group-members-summary-report-768x145.png?v=1694788413)

## Microsoft 365 Reporting tool by AdminDroid

Want deeper insights into your Microsoft 365 environment? [AdminDroid's Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub) offers over 1800+ pre-built reports and dashboards to simplify your management tasks.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/1011/1/20>*

