"""
MBOX format engine with robust parsing and error handling
"""

import mailbox
import email
from email import policy
from email.parser import Bytes<PERSON>ars<PERSON>
from pathlib import Path
from typing import Optional
import chardet

from .base_engine import MailEngineBase, ProgressCallback, MailEngineError
from ..core.email_message import EmailMessage, EmailCollection, Attachment


class MboxEngine(MailEngineBase):
    """Engine for MBOX format files"""
    
    def __init__(self):
        super().__init__()
        self.engine_name = "MBOX Engine"
        self.supported_extensions = ['.mbox', '.mbx']
    
    def can_handle(self, file_path: Path) -> bool:
        """Check if this is an MBOX file"""
        if file_path.suffix.lower() in self.supported_extensions:
            return True
        
        # Check file content for MBOX signature
        try:
            with open(file_path, 'rb') as f:
                first_line = f.readline()
                return first_line.startswith(b'From ')
        except Exception:
            return False
    
    def load_messages(self, file_path: Path, progress: Optional[ProgressCallback] = None) -> EmailCollection:
        """Load messages from MBOX file"""
        
        # Validate file
        is_valid, error_msg = self.validate_file(file_path)
        if not is_valid:
            raise MailEngineError(error_msg)
        
        collection = EmailCollection()
        
        if progress:
            progress.update("Opening MBOX file...", 0, 0)
        
        try:
            # Try to detect encoding
            encoding = self._detect_encoding(file_path)
            self.log_info(f"Detected encoding: {encoding}")
            
            # Open MBOX file
            mbox = mailbox.mbox(str(file_path))
            
            # Get total count for progress
            total_messages = len(mbox)
            self.log_info(f"Found {total_messages} messages in MBOX file")
            
            if progress:
                progress.update(f"Processing {total_messages} messages...", 0, total_messages)
            
            processed = 0
            errors = 0
            
            for i, raw_message in enumerate(mbox):
                try:
                    if progress and i % 10 == 0:  # Update every 10 messages
                        progress.update(f"Processing message {i+1}/{total_messages}...", i, total_messages)
                    
                    # Parse message
                    message = self._parse_message(raw_message, file_path)
                    if message:
                        collection.add_message(message)
                        processed += 1
                    
                except Exception as e:
                    self.log_warning(f"Error parsing message {i+1}: {e}")
                    errors += 1
                    continue
            
            if progress:
                progress.update(f"Completed: {processed} messages loaded, {errors} errors", 
                              total_messages, total_messages)
            
            self.log_info(f"Successfully loaded {processed} messages from {file_path}")
            if errors > 0:
                self.log_warning(f"Encountered {errors} errors during parsing")
            
            return collection
            
        except Exception as e:
            error_msg = f"Failed to load MBOX file {file_path}: {e}"
            self.log_error(error_msg)
            raise MailEngineError(error_msg)
    
    def save_messages(self, messages: EmailCollection, file_path: Path, 
                     progress: Optional[ProgressCallback] = None) -> bool:
        """Save messages to MBOX file"""
        
        try:
            if progress:
                progress.update("Creating MBOX file...", 0, len(messages))
            
            # Create new MBOX file
            mbox = mailbox.mbox(str(file_path))
            mbox.clear()  # Ensure it's empty
            
            for i, message in enumerate(messages):
                if progress and i % 10 == 0:
                    progress.update(f"Saving message {i+1}/{len(messages)}...", i, len(messages))
                
                # Convert EmailMessage to email.message.Message
                email_msg = self._create_email_message(message)
                mbox.add(email_msg)
            
            mbox.flush()
            mbox.close()
            
            if progress:
                progress.update(f"Saved {len(messages)} messages to MBOX", 
                              len(messages), len(messages))
            
            self.log_info(f"Successfully saved {len(messages)} messages to {file_path}")
            return True
            
        except Exception as e:
            error_msg = f"Failed to save MBOX file {file_path}: {e}"
            self.log_error(error_msg)
            raise MailEngineError(error_msg)
    
    def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding"""
        try:
            with open(file_path, 'rb') as f:
                # Read first 10KB for detection
                sample = f.read(10240)
                result = chardet.detect(sample)
                return result.get('encoding', 'utf-8') or 'utf-8'
        except Exception:
            return 'utf-8'
    
    def _parse_message(self, raw_message, source_file: Path) -> Optional[EmailMessage]:
        """Parse raw email message"""
        try:
            # Extract basic fields
            subject = str(raw_message.get('Subject', '(No Subject)'))
            sender = str(raw_message.get('From', ''))
            sender_name = self._extract_name_from_address(sender)
            
            # Recipients
            recipients = self._parse_addresses(str(raw_message.get('To', '')))
            cc = self._parse_addresses(str(raw_message.get('Cc', '')))
            bcc = self._parse_addresses(str(raw_message.get('Bcc', '')))

            # Date
            date_str = str(raw_message.get('Date', ''))

            # Message ID
            message_id = str(raw_message.get('Message-ID', ''))
            
            # Extract body and attachments
            body_text, body_html, attachments = self._extract_content(raw_message)
            
            # Create EmailMessage
            message = EmailMessage(
                subject=subject,
                sender=sender,
                sender_name=sender_name,
                recipients=recipients,
                cc=cc,
                bcc=bcc,
                body_text=body_text,
                body_html=body_html,
                message_id=message_id,
                date=date_str,
                source_file=str(source_file),
                source_format='mbox',
                attachments=attachments
            )
            
            # Add headers
            for key, value in raw_message.items():
                message.set_header(key, str(value))
            
            return message
            
        except Exception as e:
            self.log_warning(f"Error parsing message: {e}")
            return None
    
    def _extract_name_from_address(self, address: str) -> str:
        """Extract display name from email address"""
        try:
            parsed = email.utils.parseaddr(address)
            return parsed[0] if parsed[0] else ""
        except Exception:
            return ""
    
    def _parse_addresses(self, address_str: str) -> list:
        """Parse comma-separated email addresses"""
        if not address_str:
            return []
        
        try:
            addresses = email.utils.getaddresses([address_str])
            return [addr[1] for addr in addresses if addr[1]]
        except Exception:
            return [address_str.strip()] if address_str.strip() else []
    
    def _extract_content(self, message) -> tuple[str, str, list]:
        """Extract text, HTML content and attachments"""
        body_text = ""
        body_html = ""
        attachments = []
        
        try:
            if message.is_multipart():
                for part in message.walk():
                    content_type = part.get_content_type()
                    disposition = part.get_content_disposition()
                    
                    if disposition == 'attachment':
                        # Handle attachment
                        attachment = self._create_attachment(part)
                        if attachment:
                            attachments.append(attachment)
                    
                    elif content_type == 'text/plain' and not disposition:
                        # Plain text content
                        try:
                            content = part.get_content()
                            if isinstance(content, str):
                                body_text += content
                        except Exception:
                            # Fallback to payload
                            payload = part.get_payload(decode=True)
                            if payload:
                                body_text += payload.decode('utf-8', errors='ignore')
                    
                    elif content_type == 'text/html' and not disposition:
                        # HTML content
                        try:
                            content = part.get_content()
                            if isinstance(content, str):
                                body_html += content
                        except Exception:
                            # Fallback to payload
                            payload = part.get_payload(decode=True)
                            if payload:
                                body_html += payload.decode('utf-8', errors='ignore')
            
            else:
                # Single part message
                content_type = message.get_content_type()
                try:
                    content = message.get_content()
                    if content_type == 'text/html':
                        body_html = content
                    else:
                        body_text = content
                except Exception:
                    # Fallback to payload
                    payload = message.get_payload(decode=True)
                    if payload:
                        content = payload.decode('utf-8', errors='ignore')
                        if content_type == 'text/html':
                            body_html = content
                        else:
                            body_text = content
        
        except Exception as e:
            self.log_warning(f"Error extracting content: {e}")
        
        return body_text, body_html, attachments
    
    def _create_attachment(self, part) -> Optional[Attachment]:
        """Create attachment from email part"""
        try:
            filename = part.get_filename()
            if not filename:
                return None
            
            content_type = part.get_content_type()
            content_id = part.get('Content-ID', '').strip('<>')
            
            # Get attachment data
            data = None
            size = 0
            try:
                payload = part.get_payload(decode=True)
                if payload:
                    data = payload
                    size = len(payload)
            except Exception:
                pass
            
            return Attachment(
                filename=filename,
                content_type=content_type,
                size=size,
                data=data,
                content_id=content_id,
                is_inline=part.get_content_disposition() == 'inline'
            )
            
        except Exception as e:
            self.log_warning(f"Error creating attachment: {e}")
            return None
    
    def _create_email_message(self, message: EmailMessage):
        """Convert EmailMessage to email.message.Message"""
        from email.mime.multipart import MIMEMultipart
        from email.mime.text import MIMEText
        from email.mime.base import MIMEBase
        from email import encoders
        
        # Create message
        if message.has_attachments or message.body_html:
            msg = MIMEMultipart()
        else:
            msg = MIMEText(message.body_text)
            msg['Subject'] = message.subject
            msg['From'] = message.sender
            msg['To'] = ', '.join(message.recipients)
            if message.cc:
                msg['Cc'] = ', '.join(message.cc)
            if message.message_id:
                msg['Message-ID'] = message.message_id
            return msg
        
        # Set headers
        msg['Subject'] = message.subject
        msg['From'] = message.sender
        msg['To'] = ', '.join(message.recipients)
        if message.cc:
            msg['Cc'] = ', '.join(message.cc)
        if message.message_id:
            msg['Message-ID'] = message.message_id
        
        # Add body
        if message.body_text:
            msg.attach(MIMEText(message.body_text, 'plain'))
        if message.body_html:
            msg.attach(MIMEText(message.body_html, 'html'))
        
        # Add attachments
        for attachment in message.attachments:
            if attachment.data:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.data)
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {attachment.filename}'
                )
                msg.attach(part)
        
        return msg
