function Remove-UserMailboxAndCalendarAccess {
    param (
        [Parameter(Mandatory = $true)]
        [string]$userName
    )

    # Hent alle mailbokse én gang
    Write-Host " -- Henter mailbokse --" -ForegroundColor Gray
    $mailboxes = Get-Mailbox -RecipientTypeDetails UserMailbox,SharedMailbox -ResultSize Unlimited
    Write-Host " -- Fjerner mailboks- og kalender-adgang igang --`n" -ForegroundColor Gray

    $total = $mailboxes.Count
    $current = 0

    foreach ($mailbox in $mailboxes) {
        $current++
        Write-Progress -Activity "Fjerner adgang" -Status "Behandler $current af $total mailbokse" -PercentComplete (($current / $total) * 100)
        # Spring over hvis det er brugerens egen mailbox
        if ($mailbox.PrimarySmtpAddress -eq $userName) {
            continue
        }

        # Fjern mailbox-adgang
        $mailboxAccess = Get-MailboxPermission -Identity $mailbox.Identity -User $userName -ErrorAction SilentlyContinue
        if ($mailboxAccess -and $mailboxAccess.AccessRights) {
            Write-Host " -- Bruger har $($mailboxAccess.AccessRights) adgang til: $($mailbox.Identity) -- " -ForegroundColor Gray
            try {
                Remove-MailboxPermission -Identity $mailbox.Identity -User $userName -AccessRights $mailboxAccess.AccessRights -Confirm:$false -ErrorAction Stop -WhatIf
                Write-Host " -- Rettighed fjernet fra: $($mailbox.Identity) --" -ForegroundColor Green
            } catch {
                Write-Host " -- Fjernelse af rettighed fra $($mailbox.Identity) fejlede: $_ --" -ForegroundColor Red
            }
        }

        # Fjern kalender-adgang
        $calendarAccess = Get-MailboxFolderPermission -Identity "$($mailbox.PrimarySmtpAddress):\kalender" -User $userName -ErrorAction SilentlyContinue
        if ($calendarAccess -and $calendarAccess.AccessRights) {
            Write-Host " -- Bruger har $($calendarAccess.AccessRights) adgang til: $($mailbox.PrimarySmtpAddress) --" -ForegroundColor Gray
            try {
                Remove-MailboxFolderPermission -Identity "$($mailbox.PrimarySmtpAddress):\kalender" -User $userName -Confirm:$false -ErrorAction Stop -WhatIf
                Write-Host " -- Rettighed fjernet fra: $($mailbox.PrimarySmtpAddress):\kalender" -ForegroundColor Green
            } catch {
                Write-Host " -- Fjernelse af rettighed fra $($mailbox.PrimarySmtpAddress) fejlede: $_" -ForegroundColor Red
            }
        }
    }
    Write-Host "`n -- Fjerner mailboks- og kalender-adgang udført --" -ForegroundColor Gray
}

Remove-UserMailboxAndCalendarAccess -userName "hfr"