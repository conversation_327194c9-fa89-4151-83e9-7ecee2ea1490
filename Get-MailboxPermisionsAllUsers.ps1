# Opret forbindelse til Exchange Online
Confirm-ExoConnection

# Hent alle bruger- og delte mailbokse
$mailboxes = Get-Mailbox -RecipientTypeDetails UserMailbox, SharedMailbox -ResultSize Unlimited

$results = @()

foreach ($mailbox in $mailboxes) {
    # Hent mailbokstilladelser
    $mailboxPermissions = Get-MailboxPermission -Identity $mailbox.Identity | Where-Object { $_.User -notlike 'NT AUTHORITY\SELF' }
    foreach ($permission in $mailboxPermissions) {
        $results += [pscustomobject]@{
            Mailbox        = $mailbox.PrimarySmtpAddress
            PermissionType = 'Mailbox'
            User           = $permission.User
            AccessRights   = $permission.AccessRights -join ', '
        }
    }

    # Hent tilladelser for mappen 'Indbakke'
    $folderPermissions = Get-MailboxFolderPermission -Identity "$($mailbox.PrimarySmtpAddress):\Indbakke" -ErrorAction SilentlyContinue
    foreach ($permission in $folderPermissions) {
        if ($permission.User -notlike 'Default' -and $permission.User -notlike 'Anonymous') {
            $results += [pscustomobject]@{
                Mailbox        = $mailbox.PrimarySmtpAddress
                PermissionType = 'Folder'
                User           = $permission.User
                AccessRights   = $permission.AccessRights -join ', '
            }
        }
    }
}

# Eksportér resultaterne til en CSV-fil
$results | Export-Csv -Path "C:\Temp\MailboxPermissions.csv" -NoTypeInformation -Encoding UTF8
Write-Host "Result exported to C:\Temp\MailboxPermissions.csv" -ForegroundColor Cyan

# Afbryd forbindelsen til Exchange Online
Disconnect-ExchangeOnline -Confirm:$false
