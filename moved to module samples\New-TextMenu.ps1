# New-TextMenu.ps1

# info for publishing to PowerShellGallery

<#PSScriptInfo
.VERSION *******
.GUID 083e9407-4c18-470b-b0ce-f972fc58315c
.AUTHOR Bill Riedy
.COMPANYNAME Bill <PERSON>
.COPYRIGHT 2019
.TAGS text menu console
.LICENSEURI
.PROJECTURI
.ICONURI
.EXTERNALMODULEDEPENDENCIES
.REQUIREDSCRIPTS
.EXTERNALSCRIPTDEPENDENCIES
.RELEASENOTES
#>

# comment based help

<#
.SYNOPSIS
    Creates the logic for a new simple text based menu.
.DESCRIPTION
    Creates the logic for a new simple text based menu and sends to output. Can be redirected to a file.
.PARAMETER Option
    An array of [string] indicating the menu options. If you need to create a menu with a single option enclose the option in @().
.PARAMETER Title
    The title of the menu. Defaults to 'Menu Title'.
.PARAMETER ChoiceVarName
    The name of the choice variable. Defaults to 'choice'.
.PARAMETER TestMenu
    If this switch is enabled then the menu is saved to a temporary file and run.
.PARAMETER Clipboard
    If this switch is enabled then the menu logic is copied to the clipboard.
.NOTES
    Author:     Bill Riedy
    Version:    1.1
.EXAMPLE
    New-TextMenu.ps1 -Title 'Menu Title' -Option 'One', 'Two', 'Three'
    Creates a 3 option menu.
.EXAMPLE
    New-TextMenu.ps1 -Title 'My Menu' -Option 'One' -ChoiceVarName 'choice2'

    Creates a 1 option menu that looks like:

$choice2 = ''
while ($choice2 -ne 'q') {
    write-host 'My Menu'
    write-host '======='
    write-host ' '
    write-host '1 - One'
    write-host 'Q - Quit'
    write-host ' '
    $choice2 = read-host 'Selection'
    switch ($choice2) {
        q { 'Exit message and code' }
        1 { 'Option 1 code' }
        default { write-host 'Please enter a valid selection' }
    }
}
.EXAMPLE
    New-TextMenu.ps1 -Option 'Uno'

    Creates a 1 option menu that looks like:

$choice = ''
while ($choice -ne 'q') {
    write-host 'Menu Title'
    write-host '=========='
    write-host ' '
    write-host '1 - Uno'
    write-host 'Q - Quit'
    write-host ' '
    $choice = read-host 'Selection'
    switch ($choice) {
        q { 'Exit message and code' }
        1 { 'Option 1 code' }
        default { write-host 'Please enter a valid selection' }
    }
}
.OUTPUTS
    [string[]]
.LINK
    about_while
    write-host
    read-host
    about_switch
#>

    #region Parameter
    [cmdletbinding(
        DefaultParameterSetName = '',
        ConfirmImpact = 'low'
    )]
    [OutputType([string[]])]
    Param(
        [Parameter(
            Mandatory = $True,
            HelpMessage = 'Enter the text of the menu option.',
            Position = 0,
            ParameterSetName = '',
            ValueFromPipeline = $True)
            ]
            [string[]] $Option,
        [Parameter(
            Position = 1,
            ParameterSetName = '')]
            [string] $Title = 'Menu Title',
        [Parameter(
            Position = 2,
            ParameterSetName = '')]
            [string] $ChoiceVarName = 'choice',
        [Parameter(ParameterSetName = '')]
            [switch] $TestMenu,
        [Parameter(ParameterSetName = '')]
            [switch] $Clipboard
        )
    #endregion Parameter

    $result = [string[]] @()
    $result += "`$$ChoiceVarName = ''"
    $result += "while (`$$ChoiceVarName -ne 'q') {"
    $result += "    write-host '$title'"
    $result += "    write-host '$('='*$title.length)'"
    $result += "    write-host ' '"
    for ($i = 0; $i -lt $option.count; $i++) { $result += "    write-host '$($i+1) - $($option[$i])'" }
    $result += "    write-host 'Q - Quit'"
    $result += "    write-host ' '"
    $result += "    `$$ChoiceVarName = read-host 'Selection'"
    $result += "    switch (`$$ChoiceVarName) {"
    $result += "        q { 'Exit message and code' }"
    #$result += "             break }"
    for ($i = 0; $i -lt $option.count; $i++) { $result += "        $($i+1) { 'Option $($i+1) code' }" }
    $result += "        default { write-host 'Please enter a valid selection' }"
    $result += '    }'
    $result += '}'

    if ($TestMenu) {
        $tempFilename = new-temporaryfile
        $tempFilename = $tempFilename.directory.tostring() + '\' + $tempFilename.basename + '.ps1'
        $result > $tempFilename
        & $tempFilename
        remove-item $tempFilename
    }
    else {
        $result
    }
    if ($Clipboard) {
        $result | clip.exe
        write-host 'Menu logic copied to clipboard' -Foreground cyan
    }
# EOF New-TextMenu.ps1
