## Export Office 365 Calendar Permissions Report using PowerShell

This PowerShell script helps to get calendar permissions for all the Office 365 mailboxes. Also, it exports 6 different mailbox permission reports to CSV.

***Sample Output:*** 

The script exports an output CSV file that looks like the screenshot below. 

![Get Mailbox Calendar Permissions Report](https://o365reports.com/wp-content/uploads/2021/11/Get-mailbox-folder-calendar-permission-1.png?v=1705576626)

## Microsoft 365 Reporting Tool by AdminDroid 

Looking for detailed reporting? [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub) offers an extensive collection of over 1800 ready-made reports and dashboards, it seamlessly complements your PowerShell scripts.

*View more comprehensive Microsoft 365 reports through AdminDroid: <https://demo.admindroid.com/#/1/11/dashboards/10>*  



