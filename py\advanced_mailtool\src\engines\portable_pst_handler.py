"""
Portable PST handler that works without external dependencies
Uses built-in Python libraries and provides conversion guidance
"""

import struct
import os
from pathlib import Path
from typing import Optional, List, Tuple
import tempfile
import zipfile
import json

from .base_engine import MailEngineBase, ProgressCallback, MailEngineError
from ..core.email_message import EmailMessage, EmailCollection, Attachment


class PortablePstHandler:
    """Portable PST handler with conversion utilities"""
    
    def __init__(self):
        self.supported_tools = [
            {
                'name': 'PST Viewer',
                'type': 'gui',
                'free': True,
                'portable': True,
                'description': 'Free PST viewer with MBOX export',
                'download': 'Search "PST Viewer free download"'
            },
            {
                'name': 'MailStore Home',
                'type': 'gui', 
                'free': True,
                'portable': False,
                'description': 'Professional email archiving (free personal)',
                'download': 'https://www.mailstore.com/mailstore-home/'
            },
            {
                'name': 'Aid4Mail Community',
                'type': 'gui',
                'free': True,
                'portable': True,
                'description': 'Limited free email migration tool',
                'download': 'https://www.aid4mail.com/community'
            }
        ]
    
    def analyze_pst_file(self, file_path: Path) -> dict:
        """Analyze PST file and provide detailed information"""
        
        info = {
            'valid_pst': False,
            'version': 'unknown',
            'size_mb': 0.0,
            'estimated_emails': 0,
            'readable': False,
            'recommendations': []
        }
        
        try:
            # Basic file info
            stat = file_path.stat()
            info['size_mb'] = stat.st_size / (1024 * 1024)
            
            # Try to read PST header
            with open(file_path, 'rb') as f:
                # Read PST signature
                signature = f.read(4)
                if signature != b'!BDN':
                    info['recommendations'].append("File is not a valid PST file")
                    return info
                
                info['valid_pst'] = True
                info['readable'] = True
                
                # Read version
                f.seek(10)
                version_bytes = f.read(2)
                version = struct.unpack('<H', version_bytes)[0]
                
                if version == 14:
                    info['version'] = 'ANSI (Outlook 97-2002)'
                elif version == 23:
                    info['version'] = 'Unicode (Outlook 2003+)'
                else:
                    info['version'] = f'Unknown ({version})'
                
                # Estimate number of emails (very rough)
                # This is a heuristic based on file size
                if info['size_mb'] < 10:
                    info['estimated_emails'] = int(info['size_mb'] * 100)
                elif info['size_mb'] < 100:
                    info['estimated_emails'] = int(info['size_mb'] * 50)
                else:
                    info['estimated_emails'] = int(info['size_mb'] * 20)
                
                # Add recommendations based on file size
                if info['size_mb'] < 50:
                    info['recommendations'].append("Small PST - online converters may work")
                elif info['size_mb'] < 500:
                    info['recommendations'].append("Medium PST - use desktop tools")
                else:
                    info['recommendations'].append("Large PST - may need splitting")
                
        except PermissionError:
            info['readable'] = False
            info['recommendations'].append("Permission denied - close Outlook or copy file")
        except Exception as e:
            info['recommendations'].append(f"Error reading file: {e}")
        
        return info
    
    def create_conversion_package(self, pst_file: Path, output_dir: Path) -> Path:
        """Create a portable conversion package"""
        
        package_dir = output_dir / f"{pst_file.stem}_conversion_kit"
        package_dir.mkdir(exist_ok=True)
        
        # Copy PST file
        pst_copy = package_dir / pst_file.name
        if not pst_copy.exists():
            import shutil
            shutil.copy2(pst_file, pst_copy)
        
        # Create conversion instructions
        instructions = self._create_detailed_instructions(pst_file)
        instructions_file = package_dir / "CONVERSION_INSTRUCTIONS.md"
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        # Create tool recommendations
        tools_info = self._create_tools_info()
        tools_file = package_dir / "RECOMMENDED_TOOLS.json"
        with open(tools_file, 'w', encoding='utf-8') as f:
            json.dump(tools_info, f, indent=2)
        
        # Create batch script for Windows
        batch_script = self._create_batch_script(pst_file.name)
        batch_file = package_dir / "convert_pst.bat"
        with open(batch_file, 'w', encoding='utf-8') as f:
            f.write(batch_script)
        
        return package_dir
    
    def _create_detailed_instructions(self, pst_file: Path) -> str:
        """Create detailed conversion instructions"""
        
        pst_info = self.analyze_pst_file(pst_file)
        
        instructions = f"""
# 🔧 PST Conversion Instructions

## 📁 File Information
- **File**: {pst_file.name}
- **Size**: {pst_info['size_mb']:.1f} MB
- **Format**: {pst_info['version']}
- **Estimated Emails**: ~{pst_info['estimated_emails']}

## 🚀 Recommended Conversion Method

### Method 1: PST Viewer (Easiest)
1. **Download PST Viewer** (free tool)
2. **Open** this PST file
3. **Select all emails** (Ctrl+A)
4. **Export → MBOX format**
5. **Open MBOX** in Advanced MailTool

### Method 2: MailStore Home (Best Quality)
1. **Download MailStore Home** (free personal use)
2. **Import** PST file
3. **Export** to MBOX or EML format
4. **Load** in Advanced MailTool

### Method 3: Online Converter (Small Files Only)
- Only for files under 25MB
- Use Zamzar.com or CloudConvert.com
- Upload PST, download MBOX

## 📋 Step-by-Step: PST Viewer

1. **Download and Install**
   - Search "PST Viewer free download"
   - Choose reputable source
   - Install software

2. **Open PST File**
   - Launch PST Viewer
   - File → Open
   - Select: {pst_file.name}

3. **Navigate and Select**
   - Browse folder tree
   - Select emails to export
   - Ctrl+A for all emails

4. **Export to MBOX**
   - Tools → Export
   - Format: MBOX
   - Location: Same folder as this file
   - Filename: {pst_file.stem}.mbox

5. **Open in Advanced MailTool**
   - Launch Advanced MailTool
   - File → Open Files
   - Select the .mbox file

## 💡 Tips for Success

- **Close Outlook** before conversion
- **Free disk space**: Ensure 2x PST size available
- **Backup first**: Keep original PST safe
- **Large files**: May take 10-30 minutes
- **Attachments**: Verify they're included

## 🔧 Troubleshooting

### "Cannot open PST file"
- Close Microsoft Outlook completely
- Copy PST to different location
- Run converter as administrator

### "Export incomplete"
- Check available disk space
- Try exporting in smaller batches
- Verify PST file integrity

### "MBOX file won't open"
- Check file size (large files load slowly)
- Verify MBOX format
- Try opening with text editor first

## 📞 Need Help?

If conversion fails:
1. Try different tool from RECOMMENDED_TOOLS.json
2. Check PST file with Outlook first
3. Consider professional PST repair services

---

**After successful conversion, delete this folder and use the .mbox file with Advanced MailTool!**
        """.strip()
        
        return instructions
    
    def _create_tools_info(self) -> dict:
        """Create tools information JSON"""
        
        return {
            "recommended_tools": self.supported_tools,
            "conversion_tips": [
                "Always backup original PST file",
                "Close Outlook before conversion", 
                "Ensure sufficient disk space",
                "Verify MBOX output before deleting PST",
                "Test with small PST first"
            ],
            "format_info": {
                "mbox": {
                    "description": "Standard mailbox format",
                    "compatibility": "Excellent with Advanced MailTool",
                    "preserves": ["emails", "attachments", "metadata"]
                },
                "eml": {
                    "description": "Individual email files",
                    "compatibility": "Good with Advanced MailTool", 
                    "preserves": ["emails", "attachments", "headers"]
                }
            }
        }
    
    def _create_batch_script(self, pst_filename: str) -> str:
        """Create Windows batch script for conversion"""
        
        script = f"""@echo off
echo 🔧 PST Conversion Helper
echo.
echo File to convert: {pst_filename}
echo.
echo This script will help you convert your PST file.
echo.
echo STEP 1: Download PST Viewer
echo - Search "PST Viewer free download"
echo - Install the software
echo.
echo STEP 2: Convert PST to MBOX
echo - Open PST Viewer
echo - File → Open → {pst_filename}
echo - Select all emails (Ctrl+A)
echo - Tools → Export → MBOX format
echo - Save as: {pst_filename.replace('.pst', '.mbox')}
echo.
echo STEP 3: Open in Advanced MailTool
echo - Launch Advanced MailTool
echo - File → Open Files
echo - Select the .mbox file
echo.
echo Press any key to open instructions file...
pause > nul
start CONVERSION_INSTRUCTIONS.md
"""
        
        return script


def create_portable_pst_solution(pst_file: Path, output_dir: Path = None) -> Path:
    """Create a complete portable PST conversion solution"""
    
    if output_dir is None:
        output_dir = pst_file.parent
    
    handler = PortablePstHandler()
    package_dir = handler.create_conversion_package(pst_file, output_dir)
    
    return package_dir
