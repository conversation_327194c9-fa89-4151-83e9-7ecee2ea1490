import os
import json
import subprocess
import requests

SOURCE_DIR = "/home/<USER>/scripts"
DEST = "GDrive:Rclone"
LOG_FILE = "/home/<USER>/rclone.log"
STATE_FILE = "/home/<USER>/scripts/rclone_state.json"
NTFY_URL = "https://ntfy.sh/dit_emne"

def get_file_state(path):
    return {
        "mtime": os.path.getmtime(path),
        "size": os.path.getsize(path)
    }

def load_state():
    if os.path.exists(STATE_FILE):
        with open(STATE_FILE, "r") as f:
            return json.load(f)
    return {}

def save_state(state):
    with open(STATE_FILE, "w") as f:
        json.dump(state, f)

def main():
    prev_state = load_state()
    curr_state = {}
    changed_files = []

    for root, _, files in os.walk(SOURCE_DIR):
        for fname in files:
            fpath = os.path.join(root, fname)
            rel_path = os.path.relpath(fpath, SOURCE_DIR)
            curr_state[rel_path] = get_file_state(fpath)
            prev = prev_state.get(rel_path)
            curr = curr_state[rel_path]
            if not prev or prev["mtime"] != curr["mtime"] or prev["size"] != curr["size"]:
                changed_files.append(rel_path)

    if changed_files:
        for rel_path in changed_files:
            src = os.path.join(SOURCE_DIR, rel_path)
            dest = f"{DEST}/{rel_path}"
            cmd = [
                "rclone", "copyto", src, dest,
                "--log-file", LOG_FILE
            ]
            subprocess.run(cmd)
        save_state(curr_state)
        msg = f"Backup udført for filer: {', '.join(changed_files)}"
        requests.post(NTFY_URL, data=msg.encode("utf-8"))
    else:
        print("Ingen ændringer, ingen backup.")

if __name__ == "__main__":
    main()