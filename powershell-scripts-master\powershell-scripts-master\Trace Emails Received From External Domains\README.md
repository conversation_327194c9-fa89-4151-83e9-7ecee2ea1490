﻿## Trace Emails Received from External Domains in Exchange Online

Export emails received from external domains in Exchange Online using this PowerShell script to detect suspicious emails and enhance email security.

***Sample Output***

The script exports a detailed report of inbound emails received from external domains in Exchange Online. The sample output is shown in the screenshot below.

![Inbound external emails report](<https://o365reports.com/wp-content/uploads/2025/06/emails-received-from-external-domains-using-powershell-script-1080x243.png?v=1748947659>)

## Microsoft 365 Reporting tool by AdminDroid 
No more complex PowerShell scripts! Visualize your entire Microsoft 365 environment with 1900+ detailed reports and 30+ interactive dashboards using [AdminDroid’s Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub).



*Unlock comprehensive emails received from all external domains reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/10802/1/20>*







