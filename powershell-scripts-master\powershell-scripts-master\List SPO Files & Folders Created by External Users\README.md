## Get SharePoint Files & Folders Created by External Users Using PowerShell

Retrieve files and folders created by external users in SharePoint Online to avoid unwanted or malicious activity and improve security.

***Sample Output:***

This script verifies and exports all the files and folders created by the external users for all SharePoint Online sites that looks like the screenshot below.

![SharePoint Files & Folders Created by External Users](https://o365reports.com/wp-content/uploads/2024/06/SPO-Files-Folders-Created-By-External-Users-Output-1024x225.png?v=1718027084)

## Microsoft 365 Reporting tool by AdminDroid

Easily track all the files and folders activities with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), providing over 1800+ pre-built reports and intuitive dashboards.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/20388/1/20>*
