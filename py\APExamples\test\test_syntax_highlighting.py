#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script til at demonstrere PowerShell syntaksfremhævning
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# Tilføj stien til Test-APExamples.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import ScrolledTextEdit klassen direkte
import importlib.util
spec = importlib.util.spec_from_file_location("test_apexamples", os.path.join(os.path.dirname(__file__), "Test-APExamples.py"))
test_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(test_module)
ScrolledTextEdit = test_module.ScrolledTextEdit

class SyntaxTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PowerShell Syntaksfremhævning Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Label
        label = QLabel("PowerShell Syntaksfremhævning Test:")
        label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        layout.addWidget(label)
        
        # Text editor med syntaksfremhævning
        self.text_edit = ScrolledTextEdit()
        self.text_edit.setFont(QFont("Consolas", 12))
        self.text_edit.powershell_syntax_enabled = True
        layout.addWidget(self.text_edit)
        
        # Test knapper
        button_layout = QVBoxLayout()
        
        test1_btn = QPushButton("Test 1: Basis PowerShell kommandoer")
        test1_btn.clicked.connect(self.load_test1)
        button_layout.addWidget(test1_btn)
        
        test2_btn = QPushButton("Test 2: Avancerede kommandoer med parametre")
        test2_btn.clicked.connect(self.load_test2)
        button_layout.addWidget(test2_btn)
        
        test3_btn = QPushButton("Test 3: Script med variabler og operatorer")
        test3_btn.clicked.connect(self.load_test3)
        button_layout.addWidget(test3_btn)
        
        clear_btn = QPushButton("Ryd")
        clear_btn.clicked.connect(self.clear_text)
        button_layout.addWidget(clear_btn)
        
        layout.addLayout(button_layout)
        
        # Indlæs første test som standard
        self.load_test1()
        
    def load_test1(self):
        """Basis PowerShell kommandoer"""
        test_code = """# Basis PowerShell kommandoer
Get-Process | Where-Object {$_.CPU -gt 100}
Set-Location -Path "C:\\Windows"
New-Item -ItemType Directory -Name "TestFolder"
Remove-Item -Path "TestFile.txt" -Force
Start-Service -Name "Spooler"
Stop-Process -Name "notepad" -Force"""
        self.text_edit.setText(test_code)
        
    def load_test2(self):
        """Avancerede kommandoer med parametre"""
        test_code = """# Exchange Online kommandoer
Get-Mailbox -Identity "<EMAIL>" -RecipientTypeDetails UserMailbox
Set-MailboxPermission -Identity "SharedMailbox" -User "<EMAIL>" -AccessRights FullAccess
New-DistributionGroup -Name "TestGroup" -DisplayName "Test Distribution Group"
Get-MessageTrace -SenderAddress "<EMAIL>" -StartDate (Get-Date).AddDays(-7)
Set-CASMailbox -Identity "<EMAIL>" -OWAEnabled $false -ActiveSyncEnabled $true"""
        self.text_edit.setText(test_code)
        
    def load_test3(self):
        """Script med variabler og operatorer"""
        test_code = """# PowerShell script med variabler og kontrol strukturer
$users = Get-ADUser -Filter * -Properties Department
$count = 0

foreach ($user in $users) {
    if ($user.Department -eq "IT" -and $user.Enabled -eq $true) {
        Write-Host "Processing user: $($user.Name)" -ForegroundColor Green
        $count++
    }
    elseif ($user.Department -like "*Sales*") {
        Write-Warning "Sales user found: $($user.Name)"
    }
}

# Sammenligning operatorer
if ($count -gt 10) {
    Write-Output "Found $count IT users"
} else {
    Write-Output "Only $count IT users found"
}

# String manipulation
$message = "Hello World"
$result = $message -replace "World", "PowerShell"
Write-Host $result"""
        self.text_edit.setText(test_code)
        
    def clear_text(self):
        """Ryd tekst"""
        self.text_edit.clear()

def main():
    app = QApplication(sys.argv)
    
    # Sæt mørkt tema
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2d2d30;
            color: #ffffff;
        }
        QWidget {
            background-color: #2d2d30;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        QPushButton {
            background-color: #0e639c;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
            min-height: 30px;
        }
        QPushButton:hover {
            background-color: #1177bb;
        }
        QPushButton:pressed {
            background-color: #094771;
        }
        QLabel {
            color: #ffffff;
            padding: 8px;
        }
    """)
    
    window = SyntaxTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
