<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,"Remove-MailboxFolderPermission -Identity <EMAIL>:\kalender -user ""xxx"" -Confirm:$false",<PERSON>ej
<PERSON>,Remove-MailboxFolderPermission -Identity <EMAIL>:\kalender -ResetDelegateUserCollection -Confirm:$false,Nej
EXO,Remove-MailboxPermission <EMAIL> -User xxx -AccessRights FullAccess -Confirm:$false,Nej
EXO,Enable-Mailbox <user mailbox> -AutoExpandingArchive,Nej
<PERSON>O,"Search-MailboxAuditLog -Mailboxes ""UserNameHere"" -LogonTypes Owner -ShowDetails -Operations HardDelete, SoftDelete, MoveToDeletedItems -StartDate ""2024-11-15"" -EndDate ""2024-11-22""",Nej
<PERSON>,"New-DynamicDistributionGroup -Name ""Partnere1"" -RecipientFilter { (Title -like ""Partnere, Advokat*"") -and (RecipientType -eq ""UserMailbox"") }",<PERSON><PERSON>,"New-DynamicDistributionGroup -Name ""Alle1"" -PrimarySmtpAddress <EMAIL> -RecipientFilter { (RecipientTypeDetails -eq ""UserMailbox"") -and (Company -eq ""Andersen Partners"") -and (Enabled -eq $true) }",Nej
EXO,Add-Mailboxpermission -Identity <EMAIL> -User <EMAIL> -Accessrights fullaccess -Automapping:$false,Ja
EXO,Add-RecipientPermission <EMAIL>-AccessRights SendAs -Trustee <EMAIL>,Nej
EXO,Add-MailboxFolderPermission -Identity <EMAIL>:\kalender -user <EMAIL> -AccessRights editor,Ja
EXO,Add-DistributionGroupMember -Identity groupName -Member userName,Nej
EXO,"Add-MailboxFolderPermission -Identity <EMAIL>:\kalender -User <EMAIL> -AccessRights Editor -SharingPermissionFlags Delegate, CanViewPrivateItems",Nej
EXO,Set-MailboxFolderPermission -Identity <EMAIL>:\kalender -User Default -AccessRights Editor,Ja
EXO,"Set-MailboxFolderPermission -Identity <EMAIL>:\kalender -User <EMAIL> -AccessRights Editor -SharingPermissionFlags Delegate, CanViewPrivateItems",Nej
EXO,"Set-Mailbox -Identity ""<EMAIL>"" -ForwardingAddress ""<EMAIL>"" -DeliverToMailboxAndForward $True",Nej
EXO,Set-Mailbox <EMAIL> -Type Shared,Nej
EXO,Set-Mailbox <EMAIL> -Type Regular,Nej
EXO,Set-Mailbox <EMAIL> -HiddenFromAddressListsEnabled:$true,Nej
EXO,"Set-Mailbox DEsagsadm -GrantSendOnBehalfTo @{Add=""lpa"", ""swj"", ""llv""}",Nej
EXO,"Set-Mailbox -Identity ""username"" -AcceptMessagesOnlyFrom @{add=""jdh_admin""} -RequireSenderAuthenticationEnabled $true",Nej
EXO,Set-DistributionGroup <EMAIL> -GrantSendOnBehalfOfTo <EMAIL>,Nej
EXO,Set-FocusedInbox -Identity <EMAIL> -FocusedInboxOn $false,Nej
EXO,Set-CASMailbox <EMAIL> -OWAforDevicesEnabled $false,Nej
EXO,Set-CASMailbox <EMAIL> -ActiveSyncEnabled $false,Nej
EXO,Set-CASMailbox <EMAIL> -OWAEnabled $false,Nej
EXO,"Set-ExternalInOutlook -AllowList ""<EMAIL>""",Nej
EXO,Set-ExternalInOutlook -Enabled $true,Nej
EXO,"Set-ExternalInOutlook -AllowList @{Add=""<EMAIL>""}",Nej
EXO,"Set-ExternalInOutlook -AllowList @{Remove=""<EMAIL>""}",Nej
EXO,"Set-MailboxRegionalConfiguration -Identity ""[navn her]"" -Language da-DK -DateFormat ""dd-MM-yyyy"" -LocalizeDefaultFolderName",Nej
EXO,"Get-Mailbox <EMAIL> | fl displayname, GrantSendOnBehalfTo",Nej
EXO,Get-MailboxFolderPermission -Identity <EMAIL>:\kalender,Ja
EXO,Get-FocusedInbox -Identity <EMAIL>,Nej
EXO,"Get-CASMailbox <EMAIL>| Select ActiveSyncEnabled, OwaEnabled, OWAforDevicesEnabled",Nej
EXO,Get-Mailbox <EMAIL> | Select HiddenFromAddressListsEnabled,Nej
EXO,"Get-Mailbox -RecipientTypeDetails UserMailbox,SharedMailbox -ResultSize Unlimited | Get-MailboxPermission -User <EMAIL>",Nej
EXO,"Get-Mailbox | % { Get-MailboxFolderPermission (($_.PrimarySmtpAddress.ToString())+”:\kalender”) -User *UserNameHere* -ErrorAction SilentlyContinue} | select Identity,User,AccessRights",Nej
EXO,(Get-Mailbox -ResultSize Unlimited).count,Nej
EXO,(Get-Mailbox -RecipientTypeDetails SharedMailbox -ResultSize Unlimited).count,Nej
EXO,Get-InboxRule -Mailbox <EMAIL>,Nej
EXO,"Get-MailboxFolderStatistics -Identity [username] | Select-Object FolderPath, FolderAndSubfolderSize, ArchivePolicy",Nej
EXO,"Get-MailboxStatistics [username] | ft DisplayName, TotalItemSize, ItemCount",Nej
EXO,Get-CalendarProcessing [room name here] | fl,Nej
EXO,Get-MailboxRegionalConfiguration [navn her],Nej
EXO,Get-UnifiedGroup <group name> | Set-UnifiedGroup -UnifiedGroupWelcomeMessageEnabled:$false,Nej
EXO,Get-AntiPhishPolicy | select TargetedUsersToProtect -ExpandProperty TargetedUsersToProtect | Sort-Object -Descending,Nej
EXO,Get-Mailboxpermission -Identity <EMAIL>,Nej
SPO,"Get-SPOSite -limit ALL -includepersonalsite $True | Select URL, Owner",Nej
SPO,Restore-SPODeletedSite -Identity <url here>,Nej
SPO,Set-SPOUser -Site [link her] -LoginName <EMAIL> -IsSiteCollectionAdmin $True,Nej
SPO,Get-SPOUser -Site [link her] -LoginName [username her] | fl,Nej
SPO,Set-SPOUser -site [link her] -LoginName [username her] -IsSiteCollectionAdmin $False,Nej
SPO,"Add-SPOOrgAssetsLibrary -LibraryURL ""url her"" -OrgAssetType OfficeTemplateLibrary",Nej
MsGraph,Get-MgContext,Nej
MsGraph,"Connect-MgGraph -Scopes ""User.Read.All""",Nej
MsGraph,Get-MgContext | Select -ExpandProperty Scopes,Nej
MsGraph,"Get-MgUserLicenseDetail -UserId ""<EMAIL>"" | Select Name, SkuPartNumber",Nej
MsGraph,"Connect-MgGraph -Scopes ""User.Read.All"", ""Group.Read.All"", ""Directory.Read.All""",Nej
ASR and Defender,(Get-MpPreference).AttackSurfaceReductionOnlyExclusions,Nej
ASR and Defender,Add-MpPreference -AttackSurfaceReductionOnlyExclusions <fully qualified path or resource>,Nej
ASR and Defender,Get-MpPreference | Select-Object -Property ExclusionPath -ExpandProperty ExclusionPath,Nej
ASR and Defender,"Get-MpPreference | Select AttackSurfaceReductionOnlyExclusions, AttackSurfaceReductionRules_Actions, AttackSurfaceReductionRules_Ids",Nej
