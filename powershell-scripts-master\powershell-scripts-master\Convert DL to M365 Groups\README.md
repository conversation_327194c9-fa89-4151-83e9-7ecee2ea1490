﻿## Upgrade Distribution Lists to Microsoft 365 Groups Using PowerShell
Are you searching for a way to automate distribution list migrations? Here is the PowerShell script to upgrade the distribution lists to Microsoft 365 groups.

***Sample Output:***

This PowerShell script automates the process of upgrade and eases your conversion, as it converts the distribution groups to Microsoft 365 groups more securely in no time and without much human intervention.

![Upgrade Distribution Lists to Microsoft 365 Groups]( https://o365reports.com/wp-content/uploads/2023/02/Input-file-dl-1024x292.png)

## Microsoft 365 Reporting tool by AdminDroid
Searching for comprehensive M365 data? Download the [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub) and see how it helps you as it provides 1800+ ready-to-use reports and dashboards for better insights.

*Get a complete Group reports view through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/1001/1/20](https://demo.admindroid.com/#/1/11/reports/1001/1/20)*

