## Export Microsoft 365 Guest Users’ Last Logon Time Report Using PowerShell

Track guest users’ last login time report to avoid inactive/stale guest accounts in the organization.

***Sample Output:***

This script utilizes MS Graph PowerShell to export a report on Microsoft 365 guest users' last logon time, exporting the data to a CSV file, as shown in the screenshot below.

![Microsoft 365 Guest Users’ Last Logon Time](https://o365reports.com/wp-content/uploads/2024/04/Microsoft-365-Guest-Users-Last-Logon-Time-Report.png?v=**********)

## Microsoft 365 Reporting tool by AdminDroid

Elevate your M365 reporting capabilities with [AdminDroid Microsoft 365 Reporting tool](https://admindroid.com/?src=GitHub), offering 1800+ pre-built reports and insightful dashboards for informed decision-making.

*Access more comprehensive M365 reports with AdminDroid: <https://demo.admindroid.com/#/1/11/reports/20385/1/20>*

