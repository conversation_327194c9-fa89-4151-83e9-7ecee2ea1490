﻿## Groups with Disabled Users Report
Use this PowerShell script to quickly identify groups with disabled users in Microsoft 365, simplifying tracking of active users.

***Sample Output***

The exported report on Office 365 groups with disabled users looks like the screenshot below.

![Groups with disabled users report](https://o365reports.com/wp-content/uploads/2025/01/2025-01-18-15_24_34-GroupsWithDisabledUsers_2025-Jan-18-Sat-12-51-36-PM.csv-Excel-768x175.png?v=1737441898)
## Powerful Microsoft 365 Reporting Tool by AdminDroid
If this script is helpful, you’ll love [AdminDroid Microsoft 365 reporting tool](https://admindroid.com/?src=GitHub)! With over 1800 pre-built reports and dashboards, it’s a fantastic way to dive deeper into your M365 data.

*View more comprehensive M365 mailbox reports through AdminDroid: [https://demo.admindroid.com/#/1/11/reports/1070/1/20
](https://demo.admindroid.com/#/1/11/reports/1070/1/20)*

