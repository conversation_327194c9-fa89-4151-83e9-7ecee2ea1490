{"search_history": ["add-mailbox", "add-mailbo", "add-mailb", "add-mail", "add-", "add", "e", "get-", "get", "test", "tes", "te", "t"], "command_history": ["Test -smarp", "(Get-MpPreference).AttackSurfaceReductionOnlyExclusions", "Test -test", "Test", "Add-SPOOrgAssetsLibrary -LibraryURL \"url her\" -OrgAssetType OfficeTemplateLibrary", "Add-Mailboxpermission -Identity <EMAIL> -User <EMAIL> -Accessrights fullaccess -Automapping:$false", "Connect-MgGraph -Scopes \"User.Read.All\"", "Get-MailboxFolderPermission -Identity <EMAIL>:\\kalender", "Add-DistributionGroupMember -Identity groupName -Member userName", "Get-MgUserMemberOf -UserId \"<EMAIL>\" | Select DisplayName, Id", "Connect-MgGraph -Scopes \"User.Read.All\", \"Group.Read.All\", \"Directory.Read.All\"", "Get-MailboxFolderPermission -Identity <EMAIL>:\\kalender"]}